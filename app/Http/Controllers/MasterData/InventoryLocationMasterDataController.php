<?php

namespace App\Http\Controllers\MasterData;

use App\Helpers\IntegrationReferenceHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\InventoryLocation\CreateInventoryLocationRequest;
use App\Http\Requests\InventoryLocation\GetInventoryLocationRequest;
use App\Http\Requests\InventoryLocation\UpdateInventoryLocationRequest;
use App\Http\Resources\SpotApiResponse;
use App\Jobs\ProcessOutboundBusinessScenario;
use App\Models\BusinessUnit;
use App\Models\Counterparty;
use App\Models\Country;
use App\Models\ExternalSystem;
use App\Models\InventoryLocation;
use App\Models\State;
use App\Repositories\ExternalSystemsRepository;
use App\Repositories\InventoryLocationRepository;
use App\Repositories\MasterDataRepository;
use App\Services\Integration\Upstream\Outbound\BS406;
use App\Services\Integration\Upstream\Outbound\PNG\BS406 as BS406P;
use App\Services\Integration\Upstream\Outbound\BS407;
use App\Services\Integration\Upstream\Outbound\BS531;
use App\Services\Integration\Upstream\Outbound\BS532;
use App\Traits\BreadcrumbTrait;
use Illuminate\Http\Request;

class InventoryLocationMasterDataController extends Controller
{
    use BreadcrumbTrait;

    protected $inventoryLocationRepository;
    protected $masterDataRepository;
    protected $apiResponse;

    public function __construct(
        MasterDataRepository $masterDataRepository,
        InventoryLocationRepository $inventoryLocationRepository,
        SpotApiResponse $apiResponse,
        ExternalSystemsRepository $externalSystemRepository
    ) {
        $this->masterDataRepository = $masterDataRepository;
        $this->inventoryLocationRepository = $inventoryLocationRepository;
        $this->apiResponse = $apiResponse;
        $this->externalSystemRepository = $externalSystemRepository;
    }

    public function viewIndex(Request $request)
    {

        $input = $request->all();

        if (!isset($input['per_page'])) {
            $input['per_page'] = 50;
        }

        $list = $this->inventoryLocationRepository->getInventoryLocationMaster($input, true, true, ['counterparty', 'legalEntity']);

        $this->addBreadcrumb('home', route('home'))
            ->addBreadcrumb('master data', route('master-data'))
            ->addBreadcrumb('inventory location master');

        $bu = BusinessUnit::BUSINESS_UNITS_OPTIONS;
        array_pop($bu);

        return view('master-data.inventory-location.index', [
            'input' => $input,
            'breadcrumbs' => $this->makeBreadcrumb(),
            'data' => $list,
            'business_units' => $bu
        ]);

    }

    public function viewCreate()
    {

        $countries = $this->masterDataRepository->getCountryMaster();
        $states = $this->masterDataRepository->getStateMaster(['exclude_cxc_rubber_id' => true]);
        $regions = $this->masterDataRepository->getRegionMaster();
        $ports = $this->masterDataRepository->getPortMaster(['is_active' => true]);
        $tender_point_operators = $this->masterDataRepository->getCounterpartyMaster(['is_tender_point_operator' => 1]);
        $counterparties = $this->masterDataRepository->getCounterpartyMaster();
        $external_systems = $this->externalSystemRepository->getExternalSystemMaster(['include_legacy' => true], true);
        $legalEntities = $this->masterDataRepository->getLegalEntityMaster();

        $this->addBreadcrumb('home', route('home'))
            ->addBreadcrumb('master data', route('master-data'))
            ->addBreadcrumb('inventory location master', route('master-inventory-location'))
            ->addBreadcrumb('create');

        return view('master-data.inventory-location.create', [
            'breadcrumbs' => $this->makeBreadcrumb(),
            'countries' => $countries,
            'states' => $states,
            'regions' => $regions,
            'ports' => $ports,
            'tender_point_operators' => $tender_point_operators,
            'counterparties' => $counterparties,
            'is_update' => false,
            'external_systems' => $external_systems,
            'legalEntities' => $legalEntities,
        ]);

    }

    public function viewCreateSubmit(CreateInventoryLocationRequest $request)
    {

        $input = $request->all();

        try {

            if (!empty($input['counterparty_id']) && !empty($input['legal_entity_id'])) {
                throw new \Exception("Only allowed to select Legal Entity or Counterparty");
            }

            if (isset($input['country']) && $input['country'] != Country::name('Malaysia')->first()->code) {
                $input['state'] = State::name('N/A')->first()->name;
            }

            if (!isset($input['is_minamas'])) {
                $input['is_minamas'] = false;
            }

            if (isset($input['to_emails']) || $input['cc_emails']) {
                $input['to_emails'] = explode("\n", str_replace("\r\n", "\n", $input['to_emails']));
                $input['cc_emails'] = explode("\n", str_replace("\r\n", "\n", $input['cc_emails']));
            }

            $inventoryLocation = $this->inventoryLocationRepository->addInventoryLocation($input);

            foreach ($input['reference_system_code'] as $key => $code) {
                if ($input['reference_value'][$key]) {
                    IntegrationReferenceHelper::insertOrUpdate($code, InventoryLocation::class, 'code', $inventoryLocation->code, $input['reference_value'][$key]);
                }
            }

            if (isset($input['mirror_deliver_to'])) {
                $this->inventoryLocationRepository->syncDeliverToInformation($inventoryLocation, $input);
                $inventoryLocation->refresh();
                $this->inventoryLocationRepository->syncIntegrationReference($inventoryLocation, $inventoryLocation->mirrorDeliverTo);
            }

            $profit_center = $inventoryLocation->counterparty;

            if ($profit_center) {
                // rubber & physical is mutually exclusive
                if ($profit_center->is_rubber_integration) {

                    $vendor_id = IntegrationReferenceHelper::getExternalValueByType(ExternalSystem::SAP, Counterparty::class, 'code', $profit_center->code, 'vendor_id');

                    if (!empty($vendor_id)) {
                        ProcessOutboundBusinessScenario::dispatch(new BS532, $profit_center);
                    }

                    $customer_id = IntegrationReferenceHelper::getExternalValueByType(ExternalSystem::SAP, Counterparty::class, 'code', $profit_center->code, 'customer_id');

                    if (!empty($customer_id)) {
                        ProcessOutboundBusinessScenario::dispatch(new BS531, $profit_center);
                    }

                } else {

                    $is_png = ($profit_center->legalEntity && $profit_center->legalEntity->is_png) || $profit_center->is_png_integration;

                    if ($is_png) {
                        ProcessOutboundBusinessScenario::dispatch(new BS406P, $profit_center);
                    } else {
                        ProcessOutboundBusinessScenario::dispatch(new BS406, $profit_center);
                    }

                    if ($profit_center->is_ffb_integration) {
                        ProcessOutboundBusinessScenario::dispatch(new BS407, $profit_center);
                    }
                }
            }

            $legal_entity = $inventoryLocation->legalEntity;

            if ($legal_entity && $legal_entity->is_png) {
                foreach ($legal_entity->profitCenters as $profitCenter) {
                    ProcessOutboundBusinessScenario::dispatch(new BS406P, $profitCenter);
                }
            }

            return redirect()->route('master-inventory-location')->with('success', 'Inventory Location created successfully.');

        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', '206 - ' . $e->getMessage());
        }

    }

    public function viewUpdate(GetInventoryLocationRequest $request)
    {

        $input = $request->all();

        $countries = $this->masterDataRepository->getCountryMaster();
        $states = $this->masterDataRepository->getStateMaster();
        $regions = $this->masterDataRepository->getRegionMaster();
        $ports = $this->masterDataRepository->getPortMaster(['is_active' => true]);
        $legalEntities = $this->masterDataRepository->getLegalEntityMaster();
        $tender_point_operators = $this->masterDataRepository->getCounterpartyMaster(['is_tender_point_operator' => 1]);
        $counterparties = $this->masterDataRepository->getCounterpartyMaster();
        $external_systems = $this->externalSystemRepository->getExternalSystemMaster(['include_legacy' => true], true);
        $integration_references = $this->masterDataRepository->getIntegrationReferenceMaster(['integratable_type' => 'App\Models\InventoryLocation', 'integratable_id' => $input['id']], true);

        $inventoryLocation = $this->inventoryLocationRepository->getInventoryLocationMaster(['id' => $input['id']], false);

        $this->addBreadcrumb('home', route('home'))
            ->addBreadcrumb('master data', route('master-data'))
            ->addBreadcrumb('inventory location master', route('master-inventory-location'))
            ->addBreadcrumb('update');

        return view('master-data.inventory-location.create', [
            'breadcrumbs' => $this->makeBreadcrumb(),
            'data' => $inventoryLocation,
            'is_update' => true,
            'countries' => $countries,
            'states' => $states,
            'regions' => $regions,
            'ports' => $ports,
            'tender_point_operators' => $tender_point_operators,
            'counterparties' => $counterparties,
            'legalEntities' => $legalEntities,
            'external_systems' => $external_systems,
            'integration_references' => $integration_references,
        ]);

    }

    public function viewUpdateSubmit(UpdateInventoryLocationRequest $request)
    {

        $input = $request->all();

        try {

            if (!empty($input['counterparty_id']) && !empty($input['legal_entity_id'])) {
                throw new \Exception("Only allowed to select Legal Entity or Counterparty");
            }

            $inventoryLocation = $this->inventoryLocationRepository->getInventoryLocationMaster(['id' => $input['id']], false);

            if (isset($input['country']) && $input['country'] != Country::name('Malaysia')->first()->code) {
                $input['state'] = State::name('N/A')->first()->name;
            }

            if (!isset($input['is_minamas'])) {
                $input['is_minamas'] = false;
            }

            $to_emails = isset($input['to_emails']) ? array_diff(explode("\n", str_replace("\r\n", "\n", $input['to_emails'])), ['']) : [];
            $inventoryLocation->setMailingListTo($to_emails);

            $cc_emails = isset($input['cc_emails']) ? array_diff(explode("\n", str_replace("\r\n", "\n", $input['cc_emails'])), ['']) : [];
            $inventoryLocation->setMailingListCc($cc_emails);

            $inventoryLocation = $this->inventoryLocationRepository->updateInventoryLocation($inventoryLocation, $input);

            foreach ($input['reference_system_code'] as $key => $code) {
                if ($input['reference_value'][$key]) {
                    IntegrationReferenceHelper::insertOrUpdate($code, InventoryLocation::class, 'code', $inventoryLocation->code, $input['reference_value'][$key]);
                } else {
                    IntegrationReferenceHelper::delete($code, InventoryLocation::class, $inventoryLocation->id, 'code');
                }
            }

            // after update, refresh the model
            $inventoryLocation->refresh();


            // deliver to mirrored link remove
            if (!isset($input['mirror_deliver_to']) && $inventoryLocation->mirrorDeliverTo()->exists()) {

                if ( $inventoryLocation->mirrorDeliverTo != null ){
                    $this->inventoryLocationRepository->syncIntegrationReference($inventoryLocation, $inventoryLocation->mirrorDeliverTo, $delete = true);
                    $this->masterDataRepository->updateDeliverToCounterParty($inventoryLocation->mirrorDeliverTo, []);
                    $inventoryLocation->mirrorDeliverTo()->delete();
                }

            } else {
                $this->inventoryLocationRepository->syncDeliverToInformation($inventoryLocation, $input);
            }


            $profit_center = $inventoryLocation->counterparty;

            if ($profit_center) {
                // rubber & physical is mutually exclusive
                if ($profit_center->is_rubber_integration) {

                    $vendor_id = IntegrationReferenceHelper::getExternalValueByType(ExternalSystem::SAP, Counterparty::class, 'code', $profit_center->code, 'vendor_id');

                    if (!empty($vendor_id)) {
                        ProcessOutboundBusinessScenario::dispatch(new BS532, $profit_center);
                    }

                    $customer_id = IntegrationReferenceHelper::getExternalValueByType(ExternalSystem::SAP, Counterparty::class, 'code', $profit_center->code, 'customer_id');

                    if (!empty($customer_id)) {
                        ProcessOutboundBusinessScenario::dispatch(new BS531, $profit_center);
                    }

                } else {

                    $is_png = ($profit_center->legalEntity && $profit_center->legalEntity->is_png) || $profit_center->is_png_integration;

                    if ($is_png) {
                        ProcessOutboundBusinessScenario::dispatch(new BS406P, $profit_center);
                    } else {
                        ProcessOutboundBusinessScenario::dispatch(new BS406, $profit_center);
                    }

                    if ($profit_center->is_ffb_integration) {
                        ProcessOutboundBusinessScenario::dispatch(new BS407, $profit_center);
                    }
                }
            }

            $legal_entity = $inventoryLocation->legalEntity;

            if ($legal_entity && $legal_entity->is_png) {
                foreach ($legal_entity->profitCenters as $profitCenter) {
                    ProcessOutboundBusinessScenario::dispatch(new BS406P, $profitCenter);
                }
            }

            return redirect()->route('master-inventory-location')->with('success', 'Inventory Location updated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', '207 - ' . $e->getMessage());
        }

    }

    public function viewDeleteSubmit(GetInventoryLocationRequest $request)
    {
        $input = $request->all();

        try {

            $inventoryLocation = $this->inventoryLocationRepository->getInventoryLocationMaster(['id' => $input['id']], false);

            $inventoryLocation->integrate()->delete();

            $this->inventoryLocationRepository->deleteInventoryLocation($inventoryLocation);

            return redirect()->route('master-inventory-location')->with('success', 'Inventory Location deleted successfully.');

        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', '208 - ' . $e->getMessage());
        }
    }

}
