<?php

namespace App\Http\Controllers\MasterData;

use App\Helpers\DateHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\MasterData\ForexIndexEntry\CreateForexIndexEntryRequest;
use App\Http\Requests\MasterData\ForexIndexEntry\GetForexIndexByPeriodRequest;
use App\Http\Requests\MasterData\ForexIndexEntry\GetForexIndexEntryCreateRequest;
use App\Http\Requests\MasterData\ForexIndexEntry\GetForexIndexEntryRequest;
use App\Http\Requests\MasterData\ForexIndexEntry\UpdateForexIndexEntryRequest;
use App\Http\Resources\SpotApiResponse;
use App\Models\ForexIndex;
use App\Repositories\ForexIndexEntryRepository;
use App\Repositories\ForexIndexRepository;
use App\Repositories\MasterDataRepository;
use App\Traits\BreadcrumbTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ForexIndexEntryMasterDataController extends Controller
{
    use BreadcrumbTrait;
    //
    /* @var MasterDataRepository */
    protected $masterDataRepository;

    /* @var ForexIndexEntryRepository */
    protected $forexIndexEntryRepository;

    /**
     * ForexIndexEntryMasterDataController constructor.
     * @param MasterDataRepository $masterDataRepository
     * @param ForexIndexEntryRepository $forexIndexEntryRepository
     */
    public function __construct(
        MasterDataRepository $masterDataRepository,
        ForexIndexEntryRepository $forexIndexEntryRepository
    ) {
        $this->masterDataRepository = $masterDataRepository;
        $this->forexIndexEntryRepository = $forexIndexEntryRepository;
    }

    /**
     * View Index
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function viewIndex(Request $request)
    {
        $input = $request->all();

        if (!isset($input['per_page'])) {
            $input['per_page'] = 50;
        }

        if ( !isset($input['sort_by']) ){
            $input['sort_by'] = 'delivery_start_date';
            $input['sort_direction'] = 'desc';
        }

        $list = $this->forexIndexEntryRepository->getForexIndexEntryMaster($input, true, true);

        $this->addBreadcrumb('home', route('home'))
            ->addBreadcrumb('master data', route('master-data'))
            ->addBreadcrumb('forex index entry master');

        return view('master-data.forex-index-entry.index', ['breadcrumbs' => $this->makeBreadcrumb(), 'data' => $list, 'input' => $input]);
    }

    /**
     * View Create
     * @param GetForexIndexEntryCreateRequest $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function viewCreate(GetForexIndexEntryCreateRequest $request)
    {
        $sources = $this->forexIndexEntryRepository->getSources();

        $model = $request->input('type');

        $this->addBreadcrumb('home', route('home'))
            ->addBreadcrumb('master data', route('master-data'))
            ->addBreadcrumb('forex index entry master', route('master-forex-index-entry'))
            ->addBreadcrumb('create');

        return view('master-data.forex-index-entry.create', [
            'breadcrumbs' => $this->makeBreadcrumb(),
            'sources' => $sources,
            'indexable_type' => $request->input('type'),
            'indexable_id' => $request->input('id'),
            'is_update' => false
        ]);
    }

    /**
     * View Create Submit
     * @param CreateForexIndexEntryRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function viewCreateSubmit(CreateForexIndexEntryRequest $request)
    {
        $input = $request->all();

        try {

            if (isset($input['entry_date'])) {
                $input['entry_date'] = Carbon::parse($input['entry_date'], Auth::user()->timezone)->tz('UTC');
            }

            $input['delivery_start_date'] = null;
            $input['delivery_end_date'] = null;

            if (!empty($input['delivery_period'])) {
                $input['delivery_start_date'] = Carbon::parse(DateHelper::getDateFromPeriod($input['delivery_period'])[0], Auth::user()->timezone)->tz('UTC');
                $input['delivery_end_date'] = Carbon::parse(DateHelper::getDateFromPeriod($input['delivery_period'])[1], Auth::user()->timezone)->tz('UTC');
                unset($input['delivery_period']);
            }

            $forex_index_entry = $this->forexIndexEntryRepository->createForexIndexEntry($input);
            return redirect()->route('master-forex-index-entry')->with('success', 'Forex Index Entry created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', '100 - ' . $e->getMessage());
        }
    }

    /**
     * View Update
     * @param GetForexIndexEntryRequest $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function viewUpdate(GetForexIndexEntryRequest $request)
    {
        $input = $request->all();

        $sources = $this->forexIndexEntryRepository->getSources();

        $forex_index_entry = $this->forexIndexEntryRepository->getForexIndexEntryMaster(['id' => $input['id']], false, false);

        $this->addBreadcrumb('home', route('home'))
            ->addBreadcrumb('master data', route('master-data'))
            ->addBreadcrumb('forex index entry master', route('master-forex-index-entry'))
            ->addBreadcrumb('update');

        return view('master-data.forex-index-entry.create', [
            'breadcrumbs' => $this->makeBreadcrumb(),
            'sources' => $sources,
            'indexable_type' => $forex_index_entry->indexable_type,
            'indexable_id' => $forex_index_entry->indexable_id,
            'data' => $forex_index_entry,
            'is_update' => true
        ]);
    }

    /**
     * View Update Submit
     * @param UpdateForexIndexEntryRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function viewUpdateSubmit(UpdateForexIndexEntryRequest $request)
    {
        $input = $request->all();

        try {
            $forex_index_entry = $this->forexIndexEntryRepository->getForexIndexEntryMaster(['id' => $input['id']], false, false);

            $this->forexIndexEntryRepository->updateForexIndexEntry($forex_index_entry, $input);

            return redirect()->route('master-forex-index-entry')->with('success', 'Forex Index Entry updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', '100 - ' . $e->getMessage());
        }
    }

    /**
     * Delete Submit
     * @param GetForexIndexEntryRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function viewDeleteSubmit(GetForexIndexEntryRequest $request)
    {
        $input = $request->all();

        try {
            $forex_index_entry = $this->forexIndexEntryRepository->getForexIndexEntryMaster(['id' => $input['id']], false, false);

            $this->forexIndexEntryRepository->deleteForexIndexEntry($forex_index_entry);

            return redirect()->route('master-forex-index-entry')->with('success', 'Forex Index Entry deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', '100 - ' . $e->getMessage());
        }
    }


    public function apiGetForexIndexByPeriod(GetForexIndexByPeriodRequest $request)
    {

        $input = $request->all();
        $user = Auth::user();

        try {

            // Get price index entries for shipment month
            $forexIndexEntryRepository = new ForexIndexEntryRepository();

            $forex_index = (new ForexIndexRepository())->getForexIndexMaster([
                'base_currency' => $input['from_currency'],
                'target_currency' => $input['to_currency']
            ], false, false);

            if ($forex_index == null) {
                throw new \Exception('Forex index not found for currency pair ' . strtoupper($input['from_currency'] . $input['to_currency']));
            }

            $filter = [
                'indexable_type' => ForexIndex::class,
                'indexable_id' => $forex_index->id,
                'sort_by' => 'delivery_start_date',
                'sort_direction' => 'desc',
                'delivery_date_from' => !empty($input['delivery_date_from']) ? Carbon::parse($input['delivery_date_from'], 'Asia/Kuala_Lumpur')->startOfDay()->tz('UTC')->toDateTimeString() : null,
                'delivery_date_to' => !empty($input['delivery_date_to']) ? Carbon::parse($input['delivery_date_to'], 'Asia/Kuala_Lumpur')->endOfDay()->tz('UTC')->toDateTimeString() : null
            ];

            $entry = $forexIndexEntryRepository->getForexIndexEntryMaster($filter, true, false, ['indexable'])->first();

            if (empty($entry) && !empty($input['nearest_to_date'])) {

                $filter['delivery_start_date_before'] = $filter['delivery_date_from'];

                unset($filter['delivery_date_from']);
                unset($filter['delivery_date_to']);
                $entry = $forexIndexEntryRepository->getForexIndexEntryMaster($filter, true, false, ['indexable'])->first();
            }

            return (new SpotApiResponse())->setData([
                'code' => $entry->indexable->code,
                'delivery_date' => Carbon::parse($entry->delivery_start_date, 'UTC')->tz($user->timezone)->format(config('spot.date')),
                'delivery_start_date' => Carbon::parse($entry->delivery_start_date, 'UTC')->tz($user->timezone)->format(config('spot.datetime')),
                'delivery_end_date' => Carbon::parse($entry->delivery_end_date, 'UTC')->tz($user->timezone)->format(config('spot.datetime')),
                'rate' => $entry->close
            ])->send();
        } catch (\Exception $e) {
            return (new SpotApiResponse())->setError($e->getMessage(), 400)->send();
        }
    }
}
