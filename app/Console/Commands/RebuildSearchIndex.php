<?php

namespace App\Console\Commands;

use App\Models\AdditionalCostGroup;
use App\Models\BillingDocument;
use App\Models\BillOfLading;
use App\Models\CertificateTradeContract;
use App\Models\Contract;
use App\Models\ContractStatus;
use App\Models\ContractString;
use App\Models\ContractType;
use App\Models\Counterparty;
use App\Models\DeliverTo;
use App\Models\Estate;
use App\Models\ForexIndex;
use App\Models\FuturesContract;
use App\Models\Incoterms;
use App\Models\IntegrationReference;
use App\Models\InventoryLocation;
use App\Models\LegalEntity;
use App\Models\LongTermAgreementContract;
use App\Models\MonthlyContract;
use App\Models\Nsr;
use App\Models\OptionsContract;
use App\Models\OutstandingContract;
use App\Models\PaymentTerm;
use App\Models\PhysicalContract;
use App\Models\PhysicalLoad;
use App\Models\Port;
use App\Models\PriceIndex;
use App\Models\Product;
use App\Models\RubberContract;
use App\Models\SearchIndex;
use App\Models\SpecificationGroup;
use App\Models\StockTransferContract;
use App\Models\SystemFunction;
use App\Models\Transporter;
use App\Models\Uom;
use App\Models\UomConversion;
use App\Models\User;
use App\Models\Vessel;
use App\Repositories\BillingDocumentRepository;
use App\Repositories\DeliverToRepository;
use App\Repositories\ForexIndexRepository;
use App\Repositories\FuturesContractRepository;
use App\Repositories\InventoryLocationRepository;
use App\Repositories\LongTermAgreementContractRepository;
use App\Repositories\MasterDataRepository;
use App\Repositories\MonthlyContractRepository;
use App\Repositories\OptionsContractRepository;
use App\Repositories\PhysicalContractRepository;
use App\Repositories\PhysicalLoadRepository;
use App\Repositories\PriceIndexRepository;
use App\Repositories\ProductRepository;
use App\Repositories\RubberContractRepository;
use App\Repositories\StockTransferContractRepository;
use App\Repositories\UomConversionRepository;
use App\Repositories\UserRepository;
use App\Repositories\CertificateTradeContractRepository;
use App\Repositories\NsrRepository;
use App\Repositories\BillOfLadingRepository;
use App\Repositories\SystemFunctionRepository;
use App\Repositories\VesselRepository;
use App\Services\StringContractService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use App\Services\SearchService;

/**
 * Class OutstandingContract
 * @package App\Console\Commands
 */
class RebuildSearchIndex extends Command
{
    /** @var Carbon */
    protected $now;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'search:rebuild {--module=all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rebuild the index for search. This might take a while..';

    /**
     * Things to index:
     * Physical contract  (X)
     * Rubber contract (X)
     * FFB contract LTA and Monthly (X)
     * IDT (X)
     * Futures (X)
     * Options (X)
     * Cert Trade (X)
     * Billing documents  (X)
     * Physical Loads (X)
     * Vessel nomination (X)
     * Fixed vessels (X)
     * BL - (X)
     * NSR - (X)
     * Master data - profit center (X)
     * Master data - counterparty (X)
     * Master data - users (X)
     * Master data - deliver to (X)
     * Master data - inventory location (X)
     * Master data - products (X)
     * Master data - price index (X)
     * Master data - forex index (X)
     * Master data - legal entity (X)
     * Master data - Port master (X)
     * Integration References (X)
     * System Functions - (X)
     */

    protected $masterDataRepository, $userRepository, $deliverToRepository,
        $inventoryLocationRepository, $productRepository, $priceIndexRepository,
        $forexIndexRepository, $physicalContractRepository, $billingDocumentRepository,
        $rubberContractRepository, $ffbMonthlyRepository, $ffbLtaRepository, $physicalLoadRepository,
        $stockTransferContractRepository, $futuresContractRepository, $optionsContractRepository,
        $certificateTradeContractRepository, $nsrRepository, $billOfLadingRepository,
        $systemFunctionRepository, $searchService, $vesselRepository, $stringContractService;

    /**
     * GenerateOutstandingContract constructor.
     * @param UomConversionRepository $uomConversionRepository
     */
    public function __construct(
        MasterDataRepository $masterDataRepository,
        UserRepository $userRepository,
        DeliverToRepository $deliverToRepository,
        InventoryLocationRepository $inventoryLocationRepository,
        ProductRepository $productRepository,
        PriceIndexRepository $priceIndexRepository,
        ForexIndexRepository $forexIndexRepository,
        PhysicalContractRepository $physicalContractRepository,
        BillingDocumentRepository $billingDocumentRepository,
        RubberContractRepository $rubberContractRepository,
        MonthlyContractRepository $ffbMonthlyRepository,
        LongTermAgreementContractRepository $ffbLtaRepository,
        PhysicalLoadRepository $physicalLoadRepository,
        StockTransferContractRepository $stockTransferContractRepository,
        FuturesContractRepository $futuresContractRepository,
        OptionsContractRepository $optionsContractRepository,
        CertificateTradeContractRepository $certificateTradeContractRepository,
        NsrRepository $nsrRepository,
        BillOfLadingRepository $billOfLadingRepository,
        SystemFunctionRepository $systemFunctionRepository,
        SearchService $searchService,
        VesselRepository $vesselRepository,
        StringContractService $stringContractService
    ) {
        parent::__construct();

        $this->masterDataRepository = $masterDataRepository;
        $this->userRepository = $userRepository;
        $this->deliverToRepository = $deliverToRepository;
        $this->inventoryLocationRepository = $inventoryLocationRepository;
        $this->productRepository = $productRepository;
        $this->priceIndexRepository = $priceIndexRepository;
        $this->forexIndexRepository = $forexIndexRepository;
        $this->physicalContractRepository = $physicalContractRepository;
        $this->billingDocumentRepository = $billingDocumentRepository;
        $this->rubberContractRepository = $rubberContractRepository;
        $this->ffbMonthlyRepository = $ffbMonthlyRepository;
        $this->ffbLtaRepository = $ffbLtaRepository;
        $this->physicalLoadRepository = $physicalLoadRepository;
        $this->stockTransferContractRepository = $stockTransferContractRepository;
        $this->futuresContractRepository = $futuresContractRepository;
        $this->optionsContractRepository = $optionsContractRepository;
        $this->certificateTradeContractRepository = $certificateTradeContractRepository;
        $this->nsrRepository = $nsrRepository;
        $this->billOfLadingRepository = $billOfLadingRepository;
        $this->systemFunctionRepository = $systemFunctionRepository;
        $this->searchService = $searchService;
        $this->vesselRepository = $vesselRepository;
        $this->stringContractService = $stringContractService;
    }

    public function handle()
    {

        $module = $this->option('module');

        Auth::loginUsingId(1);

        // Run all indexing functions
        if ($module == 'all' || $module == 'counterparty') {
            $this->indexCounterpartyMaster();
        }
        if ($module == 'all' || $module == 'user') {
            $this->indexUsers();
        }
        if ($module == 'all' || $module == 'deliverto') {
            $this->indexDeliverTo();
        }
        if ($module == 'all' || $module == 'inventorylocation') {
            $this->indexInventoryLocation();
        }
        if ($module == 'all' || $module == 'product') {
            $this->indexProducts();
        }
        if ($module == 'all' || $module == 'priceindex') {
            $this->indexPriceIndex();
        }
        if ($module == 'all' || $module == 'forexindex') {
            $this->indexForexIndex();
        }
        if ($module == 'all' || $module == 'legalentity') {
            $this->indexLegalEntity();
        }
        if ($module == 'all' || $module == 'port') {
            $this->indexPorts();
        }
        if ($module == 'all' || $module == 'integrationreference') {
            $this->indexIntegrationReference();
        }
        if ($module == 'all' || $module == 'physicalcontract') {
            $this->indexPhysicalContracts();
        }
        if ($module == 'all' || $module == 'billingdocument') {
            $this->indexBillingDocuments();
        }
        if ($module == 'all' || $module == 'rubbercontract') {
            $this->indexRubberContracts();
        }
        if ($module == 'all' || $module == 'ffbmonthly'){
            $this->indexFfbMonthlyContracts();
        }
        if ($module == 'all' || $module == 'ffblta'){
            $this->indexFfbLtaContracts();
        }
        if ($module == 'all' || $module == 'load'){
            $this->indexPhysicalLoads();
        }
        if ($module == 'all' || $module == 'stocktransfer'){
            $this->indexStockTransfer();
        }
        if ($module == 'all' || $module == 'futures'){
            $this->indexFutures();
        }
        if ($module == 'all' || $module == 'options'){
            $this->indexOptions();
        }
        if ($module == 'all' || $module == 'certificatetrade') {
            $this->indexCertificateTrades();
        }
        if ($module == 'all' || $module == 'nsr') {
            $this->indexNSRs();
        }
        if ($module == 'all' || $module == 'bl') {
            $this->indexBLs();
        }
        if($module == 'all' || $module == 'systemfunction') {
            $this->indexSystemFunctions();
        }
        if($module == 'all' || $module == 'vessel') {
            $this->indexFixVessels();
        }
        if($module == 'all' || $module == 'string') {
            $this->indexString();
        }


        $this->info("Populated search index table. Running TNTSearch Indexing..");

        $this->call('tntsearch:import', [
            'model' => SearchIndex::class
        ]);

        // Change permission of sqlite file (Staging and PRD only)
        if ( app()->environment('staging') || app()->environment('production') ){
            $index_file = config('scout.tntsearch.storage') . '/search_index.index';

            $this->info("Index sqlite file created at " . $index_file);

            chown($index_file, 'www-data');
            chgrp($index_file, 'www-data');
            chmod($index_file, 0775);

            $this->info("Permissions updated for sqlite file.");
        }

        $this->info("Indexing completed.");
    }

    private function indexCounterpartyMaster()
    {

        $this->info("Indexing Counterparty and Profit Center Master");

        // Delete all old indexes
        SearchIndex::where('indexable_type', Counterparty::class)
            ->delete();

        // Query DB for latest entries
        $counterparties = $this->masterDataRepository->getCounterpartyMaster([], true, false, ['legalEntity', 'countryRelationship']);

        $bar = $this->output->createProgressBar(count($counterparties));

        foreach ($counterparties->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $counterparty) {

                $data = $this->searchService->getSearchIndexDataForModel($counterparty);
                if ( $data != null ){
                    $insert[] = $data;
                }

            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');

    }

    private function indexUsers()
    {

        $this->info("Indexing User Master");

        // Delete all old indexes
        SearchIndex::where('indexable_type', User::class)
            ->delete();

        // Query DB for latest entries
        $users = $this->userRepository->getUserMaster([], true, false);

        $now = Carbon::now();

        $bar = $this->output->createProgressBar(count($users));

        foreach ($users->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $user) {

                $data = $this->searchService->getSearchIndexDataForModel($user);
                if ( $data != null ){
                    $insert[] = $data;
                }
            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');

    }

    private function indexDeliverTo()
    {

        $this->info("Indexing Deliver To Master");

        // Delete all old indexes
        SearchIndex::where('indexable_type', DeliverTo::class)
            ->delete();

        // Query DB for latest entries
        $deliver_tos = $this->deliverToRepository->getDeliverToMaster([], true, false);

        $bar = $this->output->createProgressBar(count($deliver_tos));

        foreach ($deliver_tos->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $dt) {

                $data = $this->searchService->getSearchIndexDataForModel($dt);
                if ( $data != null ){
                    $insert[] = $data;
                }
            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');

    }

    private function indexInventoryLocation()
    {

        $this->info("Indexing Inventory Location Master");

        // Delete all old indexes
        SearchIndex::where('indexable_type', InventoryLocation::class)
            ->delete();

        // Query DB for latest entries
        $inventory_locations = $this->inventoryLocationRepository->getInventoryLocationMaster([], true, false, ['counterparty', 'countryRelationship']);

        $bar = $this->output->createProgressBar(count($inventory_locations));

        foreach ($inventory_locations->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $location) {

                $data = $this->searchService->getSearchIndexDataForModel($location);
                if ( $data != null ){
                    $insert[] = $data;
                }
            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');

    }

    private function indexProducts()
    {

        $this->info("Indexing Product Master");

        // Delete all old indexes
        SearchIndex::where('indexable_type', Product::class)
            ->delete();

        // Query DB for latest entries
        $products = $this->productRepository->getProductMaster([], true, false);

        $bar = $this->output->createProgressBar(count($products));

        foreach ($products->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $product) {

                $data = $this->searchService->getSearchIndexDataForModel($product);
                if ( $data != null ){
                    $insert[] = $data;
                }
            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');

    }

    private function indexPriceIndex()
    {

        $this->info("Indexing Price Index Master");

        // Delete all old indexes
        SearchIndex::where('indexable_type', PriceIndex::class)
            ->delete();

        // Query DB for latest entries
        $price_index = $this->priceIndexRepository->getPriceIndexMaster([], true, false);

        $bar = $this->output->createProgressBar(count($price_index));

        foreach ($price_index->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $index) {

                $data = $this->searchService->getSearchIndexDataForModel($index);
                if ( $data != null ){
                    $insert[] = $data;
                }

            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');

    }

    private function indexForexIndex()
    {

        $this->info("Indexing Forex Index Master");

        // Delete all old indexes
        SearchIndex::where('indexable_type', ForexIndex::class)
            ->delete();

        // Query DB for latest entries
        $forex_index = $this->forexIndexRepository->getForexIndexMaster([], true, false);

        $bar = $this->output->createProgressBar(count($forex_index));

        foreach ($forex_index->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $index) {

                $data = $this->searchService->getSearchIndexDataForModel($index);
                if ( $data != null ){
                    $insert[] = $data;
                }

            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');

    }

    private function indexLegalEntity()
    {

        $this->info("Indexing Legal Entity Master");

        // Delete all old indexes
        SearchIndex::where('indexable_type', LegalEntity::class)
            ->delete();

        // Query DB for latest entries
        $legal_entities = $this->masterDataRepository->getLegalEntityMaster([], true, false);

        $bar = $this->output->createProgressBar(count($legal_entities));

        foreach ($legal_entities->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $legal_entity) {

                $data = $this->searchService->getSearchIndexDataForModel($legal_entity);
                if ( $data != null ){
                    $insert[] = $data;
                }

            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');

    }

    private function indexPorts()
    {

        $this->info("Indexing Port Master");

        // Delete all old indexes
        SearchIndex::where('indexable_type', Port::class)
            ->delete();

        // Query DB for latest entries
        $ports = $this->masterDataRepository->getPortMaster([], true, false, ['country', 'state']);

        $bar = $this->output->createProgressBar(count($ports));

        foreach ($ports->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $port) {

                $data = $this->searchService->getSearchIndexDataForModel($port);
                if ( $data != null ){
                    $insert[] = $data;
                }
            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');
    }

    private function indexIntegrationReference()
    {

        $this->info("Indexing Integration References");

        // Delete all old indexes
        SearchIndex::where('indexable_type', IntegrationReference::class)
            ->delete();

        // Query DB for latest entries
        $references = IntegrationReference::with('system')->get();

        $bar = $this->output->createProgressBar(count($references));

        foreach ($references->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $ref) {

                $data = $this->searchService->getSearchIndexDataForModel($ref);
                if ( $data != null ){
                    $insert[] = $data;
                }
            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');
    }

    private function indexPhysicalContracts()
    {

        // Delete all old indexes
        SearchIndex::where('indexable_type', PhysicalContract::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $contracts = $this->physicalContractRepository->getContracts([
                'public_only' => true,
                'status' => ContractStatus::STATUSES_NOT_DRAFT_AND_VOIDED,
                'lazy_load' => ['counterparty', 'profitCenter', 'product', 'allocations.mill', 'allocations.deliverTo', 'createdBy'],
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ]);

            $this->info("Indexing Physical Contracts.. Page " . $page);

            $bar = $this->output->createProgressBar(count($contracts));

            foreach ($contracts->chunk(50) as $chunks) {

                $insert = [];

                foreach ($chunks as $contract) {

                    $data = $this->searchService->getSearchIndexDataForModel($contract);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(50);

            }

            $bar->finish();

            if ( $contracts->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    private function indexBillingDocuments()
    {
        // Delete all old indexes
        SearchIndex::where('indexable_type', BillingDocument::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            // Query DB for latest entries
            $documents = $this->billingDocumentRepository->getInvoices([
                'status' => BillingDocument::INVOICE_STATUS_NOT_DRAFT_AND_VOIDED,
                'lazy_load' => ['contractable', 'contractable.counterparty', 'contractable.profitCenter',
                    'createdBy'],
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ], true, true);

            $this->info("Indexing Billing Documents.. Page " . $page);

            $bar = $this->output->createProgressBar(count($documents));

            foreach ($documents->chunk(50) as $chunks) {

                $insert = [];

                foreach ($chunks as $document) {

                    $data = $this->searchService->getSearchIndexDataForModel($document);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(50);

            }

            $bar->finish();

            if ( $documents->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    private function indexRubberContracts()
    {

        // Delete all old indexes
        SearchIndex::where('indexable_type', RubberContract::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $this->info("Indexing Rubber Contracts.. Page " . $page);

            // Query DB for latest entries
            $contracts = $this->rubberContractRepository->getContracts([
                'public_only' => true,
                'status' => ContractStatus::STATUSES_NOT_DRAFT_AND_VOIDED,
                'lazy_load' => ['counterparty', 'profitCenter', 'product', 'allocations.mill', 'allocations.deliverTo', 'createdBy'],
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ]);

            $bar = $this->output->createProgressBar(count($contracts));

            foreach ($contracts->chunk(50) as $chunks) {

                $insert = [];

                foreach ($chunks as $contract) {

                    $data = $this->searchService->getSearchIndexDataForModel($contract);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(50);

            }

            $bar->finish();

            if ( $contracts->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    private function indexFfbMonthlyContracts()
    {
        // Delete all old indexes
        SearchIndex::where('indexable_type', MonthlyContract::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $this->info("Indexing FFB Monthly Contracts.. Page " . $page);

            // Query DB for latest entries
            $contracts = $this->ffbMonthlyRepository->getContracts([
                'status' => ContractStatus::STATUSES_NOT_DRAFT_AND_VOIDED,
                'lazy_load' => ['counterparty', 'profitCenter', 'product', 'inventoryLocation', 'createdBy', 'cpoPriceIndex', 'pkPriceIndex'],
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ]);

            $bar = $this->output->createProgressBar(count($contracts));

            foreach ($contracts->chunk(50) as $chunks) {

                $insert = [];

                foreach ($chunks as $contract) {

                    $data = $this->searchService->getSearchIndexDataForModel($contract);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(50);

            }

            $bar->finish();

            if ( $contracts->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    private function indexFfbLtaContracts()
    {

        // Delete all old indexes
        SearchIndex::where('indexable_type', LongTermAgreementContract::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $this->info("Indexing FFB LTA.. Page " . $page);

            // Query DB for latest entries
            $contracts = $this->ffbLtaRepository->getContracts([
                'status' => ContractStatus::STATUSES_NOT_DRAFT_AND_VOIDED,
                'lazy_load' => ['counterparty', 'profitCenter', 'product', 'inventoryLocation', 'estate',
                    'createdBy', 'cpoPriceIndex', 'pkPriceIndex'],
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ]);

            $bar = $this->output->createProgressBar(count($contracts));

            foreach ($contracts->chunk(50) as $chunks) {

                $insert = [];

                foreach ($chunks as $contract) {

                    $data = $this->searchService->getSearchIndexDataForModel($contract);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(50);

            }

            $bar->finish();

            if ( $contracts->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    public function indexPhysicalLoads() {

        // Delete all old indexes
        SearchIndex::where('indexable_type', PhysicalLoad::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $this->info("Indexing physical loads.. Page " . $page);

            // Query DB for latest entries
            $loads = $this->physicalLoadRepository->getLoad([
                'lazy_load' => ['deliverTo', 'mill', 'contractable', 'product'],
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ], true, true);

            $bar = $this->output->createProgressBar(count($loads));

            foreach ($loads->chunk(50) as $chunks) {

                $insert = [];

                foreach ($chunks as $load) {

                    $data = $this->searchService->getSearchIndexDataForModel($load);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(50);

            }

            $bar->finish();

            if ( $loads->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');


    }

    public function indexStockTransfer() {

        // Delete all old indexes
        SearchIndex::where('indexable_type', StockTransferContract::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $this->info("Indexing Stock Transfer.. Page " . $page);

            // Query DB for latest entries
            $contracts = $this->stockTransferContractRepository->getContracts([
                'status' => ContractStatus::STATUSES_NOT_DRAFT_AND_VOIDED,
                'lazy_load' => ['profitCenter', 'counterparty', 'allocations.deliverTo', 'trader', 'createdBy'],
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ], true, true);

            $bar = $this->output->createProgressBar(count($contracts));

            foreach ($contracts->chunk(50) as $chunks) {

                $insert = [];

                foreach ($chunks as $contract) {

                    $data = $this->searchService->getSearchIndexDataForModel($contract);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(50);

            }

            $bar->finish();

            if ( $contracts->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    public function indexFutures() {

        // Delete all old indexes
        SearchIndex::where('indexable_type', FuturesContract::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $this->info("Indexing BMD Futures Contract.. Page " . $page);

            // Query DB for latest entries
            $contracts = $this->futuresContractRepository->getContracts([
                'status' => ContractStatus::STATUSES_NOT_DRAFT_AND_VOIDED,
                'lazy_load' => ['profitCenter', 'counterparty', 'allocations.deliverTo', 'allocations.mill', 'createdBy'],
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ], true, true);

            $bar = $this->output->createProgressBar(count($contracts));

            foreach ($contracts->chunk(50) as $chunks) {

                $insert = [];

                foreach ($chunks as $contract) {

                    $data = $this->searchService->getSearchIndexDataForModel($contract);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(50);

            }

            $bar->finish();

            if ( $contracts->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    public function indexOptions() {

        // Delete all old indexes
        SearchIndex::where('indexable_type', OptionsContract::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $this->info("Indexing Options Contract.. Page " . $page);

            // Query DB for latest entries
            $contracts = $this->optionsContractRepository->getContracts([
                'status' => ContractStatus::STATUSES_NOT_DRAFT_AND_VOIDED,
                'lazy_load' => ['profitCenter', 'createdBy'],
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ], true, true);


            $bar = $this->output->createProgressBar(count($contracts));

            foreach ($contracts->chunk(50) as $chunks) {

                $insert = [];

                foreach ($chunks as $contract) {

                    $data = $this->searchService->getSearchIndexDataForModel($contract);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(50);

            }

            $bar->finish();

            if ( $contracts->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    private function indexCertificateTrades()
    {
        // Delete all old indexes
        SearchIndex::where('indexable_type', CertificateTradeContract::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $this->info("Indexing Certificate Trades Contract.. Page " . $page);

            // Query DB for latest entries
            $contracts = $this->certificateTradeContractRepository->getContracts([
                'status' => ContractStatus::STATUSES_NOT_DRAFT_AND_VOIDED,
                'lazy_load' => ['counterparty', 'profitCenter', 'product', 'createdBy'],
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ]);

            $bar = $this->output->createProgressBar(count($contracts));

            foreach ($contracts->chunk(50) as $chunks) {

                $insert = [];

                foreach ($chunks as $contract) {

                    $data = $this->searchService->getSearchIndexDataForModel($contract);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(50);

            }

            $bar->finish();

            if ( $contracts->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    private function indexNSRs()
    {

        // Delete all old indexes
        SearchIndex::where('indexable_type', Nsr::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $this->info("Indexing NSRs.. Page " . $page);

            // Query DB for latest entries
            $nsrs = $this->nsrRepository->searchNsr([
                'valid' => true,
                'lazy_load' => ['deliverTo'],
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ], true, true);

            $bar = $this->output->createProgressBar(count($nsrs));

            foreach ($nsrs->chunk(100) as $chunks) {

                $insert = [];

                foreach ($chunks as $nsr) {

                    $data = $this->searchService->getSearchIndexDataForModel($nsr);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(100);

            }

            $bar->finish();

            if ( $nsrs->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    private function indexBLs()
    {

        // Delete all old indexes
        SearchIndex::where('indexable_type', BillOfLading::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $this->info("Indexing BLs.. Page " . $page);

            // Query DB for latest entries
            $bls = $this->billOfLadingRepository->searchBillOfLadings([
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ], true, true);

            $bar = $this->output->createProgressBar(count($bls));

            foreach ($bls->chunk(100) as $chunks) {

                $insert = [];

                foreach ($chunks as $bl) {

                    $data = $this->searchService->getSearchIndexDataForModel($bl);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(100);

            }

            $bar->finish();

            if ( $bls->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    private function indexSystemFunctions()
    {

        $this->info("Indexing System Functions");

        // Delete all old indexes
        SearchIndex::where('indexable_type', SystemFunction::class)
            ->delete();

        // Query DB for latest entries
        $system_functions = $this->systemFunctionRepository->getSystemFunctions([], true, false);

        $bar = $this->output->createProgressBar(count($system_functions));

        foreach ($system_functions->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $system_function) {

                $data = $this->searchService->getSearchIndexDataForModel($system_function);
                if ( $data != null ){
                    $insert[] = $data;
                }
            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');
    }

    private function indexFixVessels()
    {

        // Delete all old indexes
        SearchIndex::where('indexable_type', Vessel::class)
            ->delete();

        // Query DB for latest entries
        $page = 1;

        while ( true ){

            $this->info("Indexing Fix Vessels.. Page " . $page);

            // Query DB for latest entries
            $vessels = $this->vesselRepository->getVesselMaster([
                'manual_pagination_page' => $page,
                'per_page' => 1000
            ], true, true);


            $bar = $this->output->createProgressBar(count($vessels));

            foreach ($vessels->chunk(100) as $chunks) {

                $insert = [];

                foreach ($chunks as $vessel) {

                    $data = $this->searchService->getSearchIndexDataForModel($vessel);
                    if ( $data != null ){
                        $insert[] = $data;
                    }
                }

                SearchIndex::insert($insert);
                $bar->advance(100);

            }

            $bar->finish();

            if ( $vessels->hasMorePages() ){
                $page++;
            }else{
                break;
            }

        }

        $this->info('Finished');

    }

    private function indexString()
    {

        $this->info("Indexing Strings");

        // Delete all old indexes
        SearchIndex::where('indexable_type', ContractString::class)
            ->delete();

        // Query DB for latest entries
        $strings = $this->stringContractService->search([
            'is_active' => true
        ], 'AND', false);

        $bar = $this->output->createProgressBar(count($strings));

        foreach ($strings->chunk(100) as $chunks) {

            $insert = [];

            foreach ($chunks as $string) {

                $data = $this->searchService->getSearchIndexDataForModel($string);
                if ( $data != null ){
                    $insert[] = $data;
                }
            }

            SearchIndex::insert($insert);
            $bar->advance(100);

        }

        $bar->finish();
        $this->info('Finished');
    }
}
