<?php

namespace App\Console\Commands;

use App\Jobs\ProcessOutboundBusinessScenario;
use App\Models\Allocation;
use App\Models\Contract;
use App\Models\InventoryLocation;
use App\Repositories\AllocationRepository;
use App\Services\GeneralContractService;
use App\Services\IntegrationService;
use Illuminate\Console\Command;

class ManualContractRelease extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contract:manual-post { --contract-number= : contract number, single or separated by comma}
                                                 { --allocation-id= : allocation id, this is for when you want to send to inventory location with 0 allocated quantity. Limited to 1 contract}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manual retriggering for posting of contract (the same posting as per allocation)';

    private $integrationService;

    private $contractService;

    private $allocationRepository;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->integrationService = app(IntegrationService::class);
        $this->contractService = app(GeneralContractService::class);
        $this->allocationRepository = app(AllocationRepository::class);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $contractNumberString = $this->option('contract-number');
        $allocation_id = $this->option('allocation-id');

        // try alllocation first, if id provided
        if ($allocation_id) {

            $allocation = Allocation::find($allocation_id);

            if (!$allocation) {
                $this->error("Allocation id provided (ID: {$allocation_id}) is not found");
            }

            $trigger_point = $this->integrationService->getOutboundTriggerPointFromAllocation($allocation->inventoryLocation, $allocation->contractable);

            $businessScenario = $this->integrationService->getBusinessScenarioInstanceByTriggerPoint($trigger_point);

            if (!$businessScenario) {
                throw new \Exception("Outbound Integration for Contract Release not set up");
            }

            $this->info('Begin dispatch BS ( trigger point : ' . $businessScenario->getTriggerPoint() . ' ) for Contract: ' . $allocation->contractable->contract_number . " | Allocation ID: " . $allocation->id);
            ProcessOutboundBusinessScenario::dispatch($businessScenario, $allocation);
            $this->line('End dispatch');
            exit(0);

        }

        if (empty($contractNumberString)) {
            $this->error('Contract number is required');
            exit(1);
        }

        $contractNumbers = explode(',', $contractNumberString);

        $contracts = [];

        array_walk($contractNumbers, function($c) use (&$contracts) {
            $contractObj = $this->contractService->getModelFromContractNumber($c);
            array_push($contracts, $contractObj);
        });

        if (empty($contracts)) {
            $this->error('Contract not found');
            exit(1);
        }

        if (count($contractNumbers) != count($contracts)) {
            $this->error('Contains contract in the argument cannot be resolved');
            exit(1);
        }
        
        foreach ($contracts as $contract) {

            $contractClass = $contract->getClass();

            if ( !in_array($contractClass, [Contract::PHYSICAL_CONTRACT_CLASS, Contract::RUBBER_CONTRACT_CLASS, Contract::FUTURES_CONTRACT_CLASS] ) ) {
                $this->error('Contract ' . $contract->contract_number . ' of contract type ' .  $contractClass . ' is not supported');
                exit(1);
            }

            $this->line('checking information on allocation');

            $allocations = $this->allocationRepository->listGroupedAllocations(['contract_number' => $contract->contract_number]);

            if ($allocations->isEmpty()) {
                $this->error('Contract ' . $contract->contract_number . ' has no allocation');
            }

            $inventoryLocations = $allocations->pluck('inventory_location_id')->unique();

            foreach ($inventoryLocations as $inventory_location_id) {

                $mill = InventoryLocation::find($inventory_location_id);

                $this->line('running contract release for ' . $mill->short_name . ' | Contract: ' . $contract->contract_number);

                $trigger_point = $this->integrationService->getOutboundTriggerPointFromAllocation($mill, $contract);

                $businessScenario = $this->integrationService->getBusinessScenarioInstanceByTriggerPoint($trigger_point);

                if (!$businessScenario) {
                    throw new \Exception("Outbound Integration for Contract Release not set up");
                }

                $this->info('Begin dispatch BS ( trigger point : ' . $businessScenario->getTriggerPoint() . ' ) for Contract: ' . $contract->contract_number);
                ProcessOutboundBusinessScenario::dispatch($businessScenario, $contract);
                $this->line('End dispatch');
                
            }
            
        }
    }
}
