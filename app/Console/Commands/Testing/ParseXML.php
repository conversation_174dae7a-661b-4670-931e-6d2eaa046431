<?php

namespace App\Console\Commands\Testing;

use Illuminate\Console\Command;

class ParseXML extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'parse:xml';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Try to parse XML';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {

        $xml_file_path = storage_path('temp/sample1.xml');

        $xml_raw = file_get_contents($xml_file_path);

        // Sanitize soap response, remove unnecessary markups
        $xml_raw = str_ireplace(['SOAP-ENV:', 'SOAP:'], '', $xml_raw);
        $xml_raw = preg_replace("/\sxmlns\:(SOAP|ns[1-9])\=(\"|\').+(\"|\')/", '', $xml_raw);
        $xml_raw = preg_replace("/ns[1-9]\:/", '', $xml_raw);

        $output = simplexml_load_string($xml_raw);

        $xml_body = $output->Body;
        $json_body = json_encode($xml_body);

        $data_array = json_decode($json_body, true);

    }

}
