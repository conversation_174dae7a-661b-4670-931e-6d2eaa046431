<?php

namespace App\Console\Commands\Testing;

use App\Models\Contract;
use App\Models\ContractStatus;
use App\Models\ContractType;
use App\Models\PhysicalLoad;
use App\Services\EmailService;
use App\Services\GeneralContractService;
use App\Services\ReportService;
use Carbon\Carbon;
use Faker\Factory;
use Faker\Generator;
use Faker\Guesser\Name;
use Faker\Provider\Internet;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;

class GenerateLoadsForContract extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:loads {--contract-number=} {--count=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate fake loads for a contract. Specify a contract number, and how many loads to generate';


    /**
     * GenerateContractSummary constructor.
     * @param ReportService $report_service
     * @param EmailService $email_service
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {

        $contract_number = $this->option('contract-number');
        $loads_count = $this->option('count');

        if ( $contract_number == null ){
            throw new \Exception('Please provide a contract number');
        }
        if ( $loads_count < 1 || $loads_count > 100 ){
            throw new \Exception('Load count supported is 1 - 100');
        }

        $contract = app()->make(GeneralContractService::class)->getModelFromContractNumber($contract_number);
        $faker = Factory::create();

        DB::beginTransaction();

        try{

            for ( $i = 0; $i < $loads_count; $i++ ){

                $num = $this->random();
                $ticketNumber = 'TICKET-' . $faker->word . '-' . time().rand(0,100);

                $load = factory(PhysicalLoad::class)->create([
                    'fulfillment_type' => $contract->transaction_type == Contract::TYPE_SALES ? PhysicalLoad::TYPE_DISPATCH : PhysicalLoad::TYPE_RECEIPT,
                    'reference_no' => $ticketNumber,
                    'contract_number' => $contract->contract_number,
                    'contractable_id' => $contract->id,
                    'contractable_type' => get_class($contract),
                    'scheduled_quantity' => $num,
                    'dispatched_received_quantity' => $num,
                    'delivered_collected_quantity' => $num,
                    'invoiceable_quantity' => $num,
                    'driver_name' => 'MUTHU',
                    'system_reference' => $ticketNumber . '_' . $contract->contract_number
                ]);

                $this->info("Load generated - " . $load->reference_no . ' with ' . number_format($num, 2) . ' MT');

            }

            DB::commit();

        }catch(\Exception $e){
            DB::rollBack();
            $this->error($e->getMessage());
        }
    }

    private function random() {
        return rand(100, 10000) / 100;
    }

}
