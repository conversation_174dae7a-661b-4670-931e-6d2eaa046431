<?php

namespace App\Console\Commands\Testing;

use App\Models\Contract;
use App\Models\ContractStatus;
use App\Models\ContractType;
use App\Models\PhysicalLoad;
use App\Services\EmailService;
use App\Services\GeneralContractService;
use App\Services\PhysicalContractService;
use App\Services\ReportService;
use Carbon\Carbon;
use Faker\Factory;
use Faker\Generator;
use Faker\Guesser\Name;
use Faker\Provider\Internet;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;

class UnblockSalesContract extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sales:unblock {--contract-number=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Unblock sales contract (simulating success response from SAP credit check)';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {

        $contract_number = $this->option('contract-number');
        Auth::loginUsingId(1);

        if ( $contract_number == null ){
            throw new \Exception('Please provide a contract number');
        }

        $contract = app()->make(GeneralContractService::class)->getModelFromContractNumber($contract_number);

        if ( $contract->status !== ContractStatus::TYPE_POSTED ){
            $this->error("Contract status must be posted.");
            return;
        }

        $this->info("Manually unblock sales contract {$contract_number}");
        Log::info("Manually unblock sales contract {$contract_number}");
        app()->make(PhysicalContractService::class)->updateContractStatus($contract, ContractStatus::TYPE_CONFIRMED, $contract->contract_number);
        app()->make(PhysicalContractService::class)->updateContractBlockedStatus($contract, false);
        app()->make(PhysicalContractService::class)->updateContractDoBlockedStatus($contract, false);

    }


}
