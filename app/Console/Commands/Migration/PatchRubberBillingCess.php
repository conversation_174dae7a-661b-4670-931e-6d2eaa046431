<?php

namespace App\Console\Commands\Migration;

use App\Models\BillingDocument;
use App\Models\RubberContract;
use Illuminate\Console\Command;

class PatchRubberBillingCess extends Command
{

    protected $signature = 'patch:rubber-billing-cess {--actual}';

    protected $description = 'Patch rubber billing documents to inherit CESS settings from contract.';

    protected $errors = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->info("START!");

        $billing_documents = BillingDocument::where('contractable_type', RubberContract::class)
            ->where(function($q) {
                $q->whereNull('cess_payable')->orWhereNull('cess_inclusive');
            })
            ->whereNull('deleted_at')
            ->orderBy('id', 'ASC')
            ->get();

        $this->info(count($billing_documents) . ' billing documents to be processed.');

        foreach( $billing_documents as $doc ){

            $contract = $doc->contractable;

            if ( $contract == null ){
                continue;
            }

            $cess_payable = (bool)($contract->cess_payable === null ? false : $contract->cess_payable);
            $cess_inclusive = (bool)($contract->cess_inclusive === null ? false : $contract->cess_inclusive);

            $this->info("Patching billing document {$doc->reference_number} from contract {$contract->contract_number}. Set cess_payable = {$cess_payable}, cess_inclusive = {$cess_inclusive}");

            if ( $this->option('actual') != null ) {
                $doc->cess_payable = $cess_payable;
                $doc->cess_inclusive = $cess_inclusive;
                $doc->save();
            }

        }

        $this->info("DONE!");

    }

}
