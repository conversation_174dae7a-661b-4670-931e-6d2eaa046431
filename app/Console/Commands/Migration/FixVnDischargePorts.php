<?php

namespace App\Console\Commands\Migration;

use App\Exports\MigrationTestResultExport;
use App\Models\BillingDocument;
use App\Models\BillingDocumentTransaction;
use App\Models\MonthlyContract;
use App\Models\PhysicalContract;
use App\Models\PhysicalLoad;
use App\Models\RubberContract;
use App\Models\VesselNomination;
use App\Services\FileManagementService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class FixVnDischargePorts extends Command
{

    protected $signature = 'migration:patch-vn';

    protected $description = 'Patch vn, set discharge ports as array';

    protected $errors = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $total_rows = VesselNomination::count();

        $i = 1;

        foreach (VesselNomination::cursor() as $vn) {

            $this->info("Processing " . $i++ . " of " . $total_rows);

            $discharge_ports_id = $vn->discharge_ports_id;

            if ( $discharge_ports_id == null ){
                continue;
            }

            if ( !is_array($discharge_ports_id) ){
                $vn->discharge_ports_id = [$discharge_ports_id];
                $vn->save();
            }

        }

    }

}
