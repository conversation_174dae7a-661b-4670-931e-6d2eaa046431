<?php

namespace App\Console\Commands\Migration;

use App\Exports\MigrationTestResultExport;
use App\Models\BillingDocument;
use App\Models\BillingDocumentTransaction;
use App\Models\MonthlyContract;
use App\Models\PhysicalContract;
use App\Models\PhysicalLoad;
use App\Models\RubberContract;
use App\Services\FileManagementService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class RemoveLoadDoPrefix extends Command
{

    protected $signature = 'migration:patch-loads';

    protected $description = 'Patch loads for migration loads, remove prefix';

    protected $errors = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $total_rows = PhysicalLoad::where('contractable_type', '!=', RubberContract::class)
            ->where('reference_datetime', '>=', Carbon::parse("2019-01-01 00:00:00"))
            ->count();

        $i = 1;

        foreach (PhysicalLoad::where('contractable_type', '!=', RubberContract::class)
                     ->where('reference_datetime', '>=', Carbon::parse("2019-01-01 00:00:00"))
                     ->cursor() as $load) {

            $this->info("Processing " . $i++ . " of " . $total_rows);

            $reference_no = $load->reference_no;

            // ignore any dash reference number
            if ( strlen($reference_no) < 14 ){
                continue;
            }

            $temp = explode("-", $reference_no);

            if ( count($temp) < 2 ){
                continue;
            }

            $new_reference_no = $temp[count($temp) - 1];
            $load->reference_no = $new_reference_no;

            $load->save();

            DB::table('loads_physical_patched')->insert([
                'load_id' => $load->id,
                'contract_number' => $load->contract_number,
                'ori_reference_no' => $reference_no,
                'new_reference_no' => $new_reference_no,
                'created_at' => Carbon::now()
            ]);

        }

    }

}
