<?php

namespace App\Console\Commands\Migration;

use App\Exports\MigrationTestResultExport;
use App\Models\BillingDocument;
use App\Models\BillingDocumentTransaction;
use App\Models\LegalEntity;
use App\Models\MonthlyContract;
use App\Models\PhysicalContract;
use App\Models\PhysicalLoad;
use App\Models\RubberContract;
use App\Services\FileManagementService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class UpdateLegalEntitySettings extends Command
{

    protected $signature = 'migration:update-legal-entity-settings';

    protected $description = 'Update legal entity contract number and billing number format.';

    protected $errors = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {

        $all_legal_entities = LegalEntity::all();

        foreach ( $all_legal_entities as $le ){

            if ( $le->is_minamas == true ){
                $le->contract_number_format = LegalEntity::CONTRACT_NUMBER_FORMAT_MINAMAS_UPSTREAM;
                $le->billing_number_format = LegalEntity::BILLING_NUMBER_FORMAT_MINAMAS_UPSTREAM;

                if ( $le->code == 'SDOI' || $le->code == 'GHN' ){
                    $le->contract_number_format = LegalEntity::CONTRACT_NUMBER_FORMAT_MINAMAS_DOWNSTREAM;
                    $le->billing_number_format = LegalEntity::BILLING_NUMBER_FORMAT_MINAMAS_DOWNSTREAM;
                }

            }else{
                $le->contract_number_format = LegalEntity::CONTRACT_NUMBER_FORMAT_GTM_MY;
                $le->billing_number_format = LegalEntity::BILLING_NUMBER_FORMAT_GTM_MY;
            }

            $le->save();

        }

    }

}
