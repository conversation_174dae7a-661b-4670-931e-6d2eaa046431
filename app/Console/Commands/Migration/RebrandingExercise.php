<?php

namespace App\Console\Commands\Migration;

use App\Exports\MigrationTestResultExport;
use App\Models\BillingDocument;
use App\Models\BillingDocumentTransaction;
use App\Models\BusinessUnit;
use App\Models\LegalEntity;
use App\Models\MasterLegalEntityHistory;
use App\Models\MonthlyContract;
use App\Models\PhysicalContract;
use App\Models\PhysicalLoad;
use App\Models\RubberContract;
use App\Services\FileManagementService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class RebrandingExercise extends Command
{
    protected $signature = 'migration:rebranding';

    protected $description = 'Perform migration action for rebranding.';

    protected $errors = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->malaysia();
        $this->indonesia();
    }

    public function indonesia() {

        $business_unit_id = BusinessUnit::where('name', 'Minamas')->first();

        // foreach MY and SG legal entity
        $this->info("Updating for Indonesia legal entities");
        $legal_entities = LegalEntity::where('business_unit_id', $business_unit_id->id)
            ->whereIn('code', ['GHN', 'SDOI', 'SCT', 'SMR'])
            ->get();

        $this->patchData($legal_entities);
    }

    public function malaysia() {
        $business_unit_my = BusinessUnit::where('name', 'GTM Malaysia')->first();

        // foreach MY and SG legal entity
        $this->info("Updating for MY legal entities");
        $legal_entities = LegalEntity::where('business_unit_id', $business_unit_my->id)->get();

        $this->patchData($legal_entities);
    }

    private function patchData($legal_entities) {

        foreach ($legal_entities as $legal_entity) {

            $this->info("Updating for LE {$legal_entity->code} - {$legal_entity->name}");

            DB::transaction(function() use ($legal_entity) {

                $logo_changed = false;
                $website_changed = false;
                $old_legal_entity = clone $legal_entity;

                // patch legal entity master with blank logo
                if ( $legal_entity->printout_logo == 'SDP' ){
                    $legal_entity->printout_logo = 'SDP_NOLOGO';
                    $logo_changed = true;
                }
                else if ( $legal_entity->printout_logo == 'SDO' ){
                    $legal_entity->printout_logo = 'SDO_NOLOGO';
                    $logo_changed = true;
                }
                else{
                    $this->info("Invalid printout logo value");
                }

                // patch legal entity master with new website
                if ( $legal_entity->website == 'www.simedarbyoils.com' ){
                    $legal_entity->website = 'sdguthrie-international.com';
                    $website_changed = true;
                }
                else if ( $legal_entity->website == 'www.simedarbyplantation.com' ){
                    $legal_entity->website = 'sdguthrie.com';
                    $website_changed = true;
                }
                else{
                    $this->info("Invalid website value");
                }

                $legal_entity->save();

                if ( $website_changed ) {
                    // create history for logo and website (no name change)
                    MasterLegalEntityHistory::create([
                        'legal_entity_id' => $legal_entity->id,
                        'field' => MasterLegalEntityHistory::WEBSITE_FIELD,
                        'old_value' => $old_legal_entity->website ?? 'www.simedarbyplantation.com',
                        'effective_datetime_from' => Carbon::parse('2000-01-01 00:00:00', 'Asia/Kuala_Lumpur')->tz('UTC')->toDateTimeString(),
                        'effective_datetime_to' => Carbon::parse('2024-05-30 23:59:59', 'Asia/Kuala_Lumpur')->tz('UTC')->toDateTimeString(),
                        'created_at' => now()
                    ]);
                }
                if ( $logo_changed ){
                    MasterLegalEntityHistory::create([
                        'legal_entity_id' => $legal_entity->id,
                        'field' => MasterLegalEntityHistory::LOGO_FIELD,
                        'old_value' => $old_legal_entity->printout_logo,
                        'effective_datetime_from' => Carbon::parse('2000-01-01 00:00:00', 'Asia/Kuala_Lumpur')->tz('UTC')->toDateTimeString(),
                        'effective_datetime_to' => Carbon::parse('2024-05-30 23:59:59', 'Asia/Kuala_Lumpur')->tz('UTC')->toDateTimeString(),
                        'created_at' => now()
                    ]);
                }

            });

            $this->info("Done");

        }

    }
}
