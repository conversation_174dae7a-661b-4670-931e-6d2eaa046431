<?php

namespace App\Console\Commands\Migration;

use App\Exports\MigrationTestResultExport;
use App\Models\BillingDocument;
use App\Models\BillingDocumentTransaction;
use App\Models\MonthlyContract;
use App\Models\PhysicalContract;
use App\Models\RubberContract;
use App\Services\FileManagementService;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;

class MigrationUnitTests extends Command
{

    protected $signature = 'migration:post-tests {--type=physical}';

    protected $description = 'running various post-migration tests';

    protected $errors = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        if ( $this->option('type') == 'physical' ){
            $types = [PhysicalContract::class];
        }
        else if ( $this->option('type') == 'rubber' ){
            $types = [RubberContract::class];
        }
        else if ( $this->option('type') == 'ffb' ){
            $types = [MonthlyContract::class];
        }
        else if ( $this->option('type') == 'all' ){
            $types = [PhysicalContract::class, RubberContract::class, MonthlyContract::class];
        }

        $this->testBillingDocumentWithLineItems($types);

        $filepath = 'migration/migration_test_results_' . time() . '.xlsx';

        if ( count($this->errors) > 0 ){
            Excel::store((new MigrationTestResultExport())->setData($this->errors), $filepath, 'local', \Maatwebsite\Excel\Excel::XLSX);
        }

    }

    private function testBillingDocumentWithLineItems($types) {

        $i = 1;
        $this->errors = [];

        BillingDocument::with(['lineItems', 'invoiceTransactions', 'checkedOutWith'])
            ->whereIn('contractable_type', $types)
            ->chunk(500, function($chunk) use (&$i){

                $this->info("Processing chunk... " . $i++);

                foreach ( $chunk as $billing_document ){

                    // test for billing document amount vs line item amounts
                    $this->testAmounts($billing_document);

                    // Test unit price, premium and discount
                    $this->testUnitPrice($billing_document);

                }

            });

    }

    private function testAmounts($billing_document) {

        // TEST Billing Document amount vs line item amounts
        $amount_before_tax = $billing_document->amount_before_tax;
        $amount_after_tax = $billing_document->amount_after_tax;
        $tax_amount = $billing_document->tax_amount;
        $amount_after_tax_and_knockoff = $billing_document->amount_after_tax_and_knockoff;

        $line_items = $billing_document->lineItems;

        $li_amount_before_tax = $line_items->sum('amount_before_tax');
        $li_tax_amount = $line_items->sum('tax_amount');
        $li_amount_after_tax = $line_items->sum('amount_after_tax');

        $li_amount_after_tax_offset = $line_items->reduce(function($carry, $item){
            if ( $item->is_deduct_advance == true ){
                return $carry + $item->amount_after_tax;
            }else{
                return $carry;
            }
        }, 0);
        $li_amount_before_tax_offset = $line_items->reduce(function($carry, $item){
            if ( $item->is_deduct_advance == true ){
                return $carry + $item->amount_before_tax;
            }else{
                return $carry;
            }
        }, 0);
        $li_tax_amount_offset = $line_items->reduce(function($carry, $item){
            if ( $item->is_deduct_advance == true ){
                return $carry + $item->tax_amount;
            }else{
                return $carry;
            }
        }, 0);

        if ( $billing_document->type == BillingDocument::TYPE_CREDIT_NOTE || $billing_document->type == BillingDocument::TYPE_DEBIT_NOTE ){

            if ( bccomp($amount_before_tax, abs($li_amount_before_tax), 2) == -1  ){
                $this->logErrors($billing_document, 'AMOUNT_BEFORE_TAX', $amount_before_tax,  abs($li_amount_before_tax), $billing_document->reference_number);
            }
            if ( bccomp($amount_after_tax, abs($li_amount_after_tax), 2) == -1  ){
                $this->logErrors($billing_document, 'AMOUNT_AFTER_TAX', $amount_after_tax, abs($li_amount_after_tax), $billing_document->reference_number);
            }
            if ( bccomp($amount_after_tax_and_knockoff, abs($li_amount_after_tax), 2) == -1  ){
                $this->logErrors($billing_document, 'AMOUNT_AFTER_TAX_AND_KNOCKOFF', $amount_after_tax_and_knockoff, abs($li_amount_after_tax), $billing_document->reference_number);
            }
            if ( bccomp($tax_amount, abs($li_tax_amount), 2)  == -1 ){
                $this->logErrors($billing_document, 'TAX_AMOUNT', $tax_amount, abs($li_tax_amount), $billing_document->reference_number);
            }

        }else{

            if ( bccomp($amount_before_tax, ($li_amount_before_tax - $li_amount_before_tax_offset), 2) == -1 ){
                $this->logErrors($billing_document, 'AMOUNT_BEFORE_TAX', $amount_before_tax, ($li_amount_before_tax - $li_amount_before_tax_offset), $billing_document->reference_number);
            }
            if ( bccomp($amount_after_tax, ($li_amount_after_tax - $li_amount_after_tax_offset), 2) == -1 ){
                $this->logErrors($billing_document, 'AMOUNT_AFTER_TAX', $amount_after_tax, ($li_amount_after_tax - $li_amount_after_tax_offset), $billing_document->reference_number);
            }
            if ( bccomp($amount_after_tax_and_knockoff, ($li_amount_after_tax), 2) == -1 ){
                $this->logErrors($billing_document, 'AMOUNT_AFTER_TAX_AND_KNOCKOFF', $amount_after_tax_and_knockoff, $li_amount_after_tax, $billing_document->reference_number);
            }
            if ( bccomp($tax_amount, ($li_tax_amount - $li_tax_amount_offset), 2) == -1 ){
                $this->logErrors($billing_document, 'TAX_AMOUNT', $tax_amount, ($li_tax_amount - $li_tax_amount_offset), $billing_document->reference_number);
            }

        }

    }

    private function testUnitPrice($billing_document) {

        $price_with_premium_discount = ($billing_document->base_price + $billing_document->premium - $billing_document->discount);

        if ( bccomp($price_with_premium_discount, $billing_document->final_price, 2) == -1){
            $this->logErrors($billing_document, 'FINAL_PRICE_NOT_TALLY', $price_with_premium_discount,  $billing_document->final_price, $billing_document->reference_number);
        }

        if ( $billing_document->premium > 0 && $billing_document->discount > 0 ){
            $this->logErrors($billing_document, 'PREMIUM_AND_DISCOUNT_HAVE_VALUE', $billing_document->premium, $billing_document->discount, $billing_document->reference_number);
        }

        if ( $billing_document->premium < 0 || $billing_document->discount < 0 || $billing_document->base_price < 0 || $billing_document->final_price < 0 ||
            $billing_document->fixed_price < 0 || $billing_document->amount_after_tax < 0 || $billing_document->amount_after_tax_and_knockoff < 0 ||
            $billing_document->amount_before_tax < 0 || $billing_document->tax_amount < 0 ){
            $this->logErrors($billing_document, 'VALUE_CANNOT_NEGATIVE', 0, 0, $billing_document->reference_number);
        }

        if ( $billing_document->tax_amount > 0 && $billing_document->tax_percentage == 0 ){
            $this->logErrors($billing_document, 'INCORRECT_TAX', $billing_document->tax_amount, $billing_document->tax_percentage, $billing_document->reference_number);
        }

    }

    private function logErrors($object, $event, $value_1, $value_2, $description = null) {

        $this->errors[] = [
            'object' => get_class($object),
            'object_id' => $object->id,
            'description' => $description,
            'event' => $event,
            'value_1' => $value_1,
            'value_2' => $value_2,
            'difference' => ($value_2 - $value_1)
        ];

        return $this;

    }
}
