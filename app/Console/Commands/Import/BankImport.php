<?php

namespace App\Console\Commands\Import;

use App\Imports\Commands\BankImport as ImportsBankImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class BankImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bank:import {filepath : file must be starting from storage path as root}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Bank via filepath given';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $filepath = $this->argument('filepath');

        if (!$filepath) {
            $this->error('Filepath is required for argument');
        }

        $import = new ImportsBankImport;

        Log::info('[Bank] Importing..');

        Excel::import($import, $filepath, 'local_root');

        Log::info("[Bank] Import Completed." . $import->getImportedCount() . " of " . $import->getRowCount() . ' were successfully imported.');
    }
}
