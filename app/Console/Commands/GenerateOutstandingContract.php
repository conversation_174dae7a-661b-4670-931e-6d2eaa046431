<?php


namespace App\Console\Commands;


use App\Models\ContractStatus;
use App\Models\OutstandingContract;
use App\Models\PhysicalContract;
use App\Models\Uom;
use App\Models\UomConversion;
use App\Repositories\UomConversionRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * Class OutstandingContract
 * @package App\Console\Commands
 */
class GenerateOutstandingContract extends Command
{
    /** @var Carbon */
    protected $now;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:outstanding-contract {--now= }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate all outstanding contract';

    /** @var UomConversionRepository  */
    protected $uomConversionRepository;
    /** @var UomConversion */
    protected $uom_conversions;
    /** @var Uom */
    protected $uoms;

    /**
     * GenerateOutstandingContract constructor.
     * @param UomConversionRepository $uomConversionRepository
     */
    public function __construct(UomConversionRepository $uomConversionRepository)
    {
        $this->uomConversionRepository = $uomConversionRepository;
        parent::__construct();
    }

    public function handle()
    {
        if ($this->option('now')) {
            $this->now = Carbon::parse($this->option('now'), 'Asia/Kuala_Lumpur');
        } else {
            $this->now = Carbon::now('Asia/Kuala_Lumpur');
        }

        $this->uoms = Uom::all();
        $mt = $this->uoms->filter(function ($uom) {
            return $uom->code == Uom::METRIC_TONNES;
            })->first();
        if (!$mt) {
            throw new \Exception('MT not found');
        }
        $this->uom_conversions = $this->uomConversionRepository->getUomConversionMapping($mt);
        $this->uoms = $this->uoms->groupBy('code');

        $this->processPhysicalContracts('CPO');
        $this->processPhysicalContracts('PK');
    }

    /**
     * @param $product_code
     */
    protected function processPhysicalContracts($product_code)
    {
        $cutoff_datetime = $this->now->startOfMonth();
        $contracts = PhysicalContract::productCode($product_code)
            ->status(ContractStatus::TYPE_CONFIRMED)
            ->transactionType(PhysicalContract::TYPE_SALES)
            ->basePriceUom(Uom::METRIC_TONNES)
            ->where('shipment_month', '<', $cutoff_datetime)
            ->get();

        $this->info(sprintf('Total Physical Contract %s: %', $product_code, $contracts->count()));

        $total_quantity = 0;
        $total_price = 0;
        $contract_quantity = 0;
        $grand_total = 0;

        /** @var PhysicalContract $contract */
        $metadata = collect();
        foreach ($contracts as $contract) {
            $price = $contract->getPriceConversion('MYR');
            if ($price == null) {
                continue;
            }
            $this->info($contract->contract_number);

            // if quantity_uom is not MT
            $quantity = 0;
            if ($contract->quantity_uom != Uom::METRIC_TONNES) {
                if ($contract->quantity_in_mt > 0) { // if quantity_in_mt got value then use quantity_in_mt
                    $quantity = $contract->quantity_in_mt;
                } elseif ( isset($this->uom_conversions[$contract->product_id]) && isset($this->uom_conversions[$contract->product_id][$contract->quantity_uom])) {
                    $quantity = $contract->quantity * $this->uom_conversions[$contract->product_id][$contract->quantity_uom]->multiplier;
                } else {
                    Log::error(sprintf('GenerateOutstandingContract::class - %s unknown quantity', $contract->contract_number));
                    continue;
                }
            } else {
                $quantity = $contract->quantity;
            }

            $total_quantity += $quantity;
            $total_price += $price;
            $grand_total += ($price * $quantity);

            $contract_quantity += 1;
            $metadata->push([
                'id' => $contract->id
            ]);
        }

        $data = [
            'cutoff_datetime' => $cutoff_datetime,
            'contractable_type' => PhysicalContract::class,
            'product_code' => $product_code,
            'transaction_type' => PhysicalContract::TYPE_SALES,
            'quantity' => $total_quantity,
            'quantity_uom' => Uom::METRIC_TONNES,
            'currency' => 'MYR',
            'total_price' => $total_price,
            'average_price' => $total_quantity > 0 ? $grand_total / $total_quantity : 0 ,
            'contract_quantity' => $contract_quantity,
            'metadata' => $metadata,
        ];

        OutstandingContract::updateOrCreate([
            'cutoff_datetime' => $cutoff_datetime,
            'contractable_type' => PhysicalContract::class,
            'product_code' => $product_code,
            'transaction_type' => PhysicalContract::TYPE_SALES,
        ], $data);
    }
}
