<?php

namespace App\Console\Commands;

use App\Exports\AdhocReportExport;
use App\Exports\LoadMatchingExport;
use App\Helpers\NumberHelper;
use App\Imports\LoadMatchingImport;
use App\Models\PhysicalLoad;
use App\Services\GeneralContractService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;

class SemuaLoadMatchingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'load:show-unmatch { filepath : filepath inside storage. Eg: storage/file.xlsx should only input "file.xlsx" }
                                               { --table : output as cli table }
                                               { --export : export as excel in local storage path }
                                               { --ffb : indicate is ffb import excel format }
                                               { --rubber : indicate is rubber import excel format }
                                               { --gd : downstream goods dispatch }
                                               { --gr : downstream goods receipt }
                                               { --bulking : bulking }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show load matching or not matching';

    private $formatType;

    private $contractService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        $this->contractService = app(GeneralContractService::class);
    }

    private function getHeader()
    {
        if ($this->formatType == 'physical') {
            return [
                "customer",
                "contract",
                "basisx_ex_millb_delivered",
                "location",
                "ticket_no",
                "do_date",
                "consignmentno",
                "driver",
                "vehicle",
                "mill_wt",
                "buyer_wt",
                "contract_status",
                "reason",
            ];
        }

        if ($this->formatType == 'ffb') {
            return [
                "supplier",
                "location",
                "ticket_no",
                "date",
                "driver",
                "vehicle",
                "mill_wt",
                "supplier_wt",
                "reason",
            ];
        }

        if ($this->formatType == 'rubber') {
            return [
                'customer',
                'contract',
                'basis',
                'location',
                'ticket_no',
                'do_date',
                'driver',
                'vehicle',
                'factory_wt',
                'invoice_wt',
                'reason',
            ];
        }

        if ($this->formatType == 'downstream-receipt') {
            return [
                'Location',
                'Plant',
                'Supplier',	
                'Supplier_Code',
                'Mill',
                'Contract',
                'PO',
                'Ticket_No',
                'Date',
                'Driver',
                'Vehicle',
                'Refinery_Wt',
                'Contract Status',
                'Reason',
            ];
        }

        if ($this->formatType == 'downstream-dispatch') {
            return [
                'Location',
                'Plant',
                'Customer',	
                'Customer_Code',
                'Contract',
                'SO',
                'DO',
                'Basis',
                'Ticket_No',
                'Date',
                'Driver',
                'Vehicle',
                'Refinery_Wt',
                'Buyer_Wt',
                'Contract Status',
                'Reason',
            ];
        }

        if ($this->formatType == 'bulking') {
            return [
                'Location',
                'Supplier',
                'Supplier_Code',	
                'Mill',
                'Contract',
                'Ticket_No',
                'Date',
                'Driver',
                'Vehicle',
                'Bulking_Wt',
                'Contract Status',
                'Reason',
            ];
        }

        throw new \Exception("Unknown format");
    }

    private function setFormatType($isFfb = false, $isRubber = false, $isDownstreamGoodsDispatch =  false, $isDownstreamGoodsReceipt = false, $bulking = false)
    {
        $this->formatType = 'physical';

        if ($isFfb) {
            $this->formatType = 'ffb';
        }

        if ($isRubber) {
            $this->formatType = 'rubber';
        }

        if ($isDownstreamGoodsDispatch) {
            $this->formatType = 'downstream-dispatch';
        }

        if ($isDownstreamGoodsReceipt) {
            $this->formatType = 'downstream-receipt';
        }

        if ($bulking) {
            $this->formatType = 'bulking';
        }
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $filepath = $this->argument('filepath');
        $cliTable = $this->option('table');
        $exportExcel = $this->option('export');

        $this->setFormatType($this->option('ffb'), $this->option('rubber'), $this->option('gd'), $this->option('gr'), $this->option('bulk'));
        
        if (!file_exists(storage_path($filepath))) {
            $this->error('File not found: ' . storage_path($filepath));
            exit(1);
        }

        if (config('app.env') == 'development') {
            // check memory iteration usage
            $init_mem = memory_get_usage(true);
            echo 'Init memory use: ' . NumberHelper::convertByteSize($init_mem) . PHP_EOL;
            $start = microtime(true);
        }

        $rawCollections = Excel::toCollection(new LoadMatchingImport, $filepath , 'local_root');

        if (config('app.env') == 'development') {
            // check memory iteration usage
            echo 'Load into Memory from Excel used: ' . NumberHelper::convertByteSize(memory_get_usage(true) - $init_mem) . PHP_EOL;
            echo microtime(true) - $start . ' seconds used' . PHP_EOL;
        }

        $collections = $rawCollections->first();

        $payload = [];

        if ($collections && $collections->count() > 0) {

            foreach( $this->getInvalidRows($collections) as $reason => $row) {

                $result = $this->mapPayload($row);

                $result['reason'] = $reason;

                $payload[] = $result;
                
            }
        }
        

        $header = $this->getHeader();

        if ($cliTable) {
            $this->table($header, $payload);
        }

        if ($exportExcel) {

            if (!file_exists(storage_path('semua'))) {
                mkdir(storage_path('semua'));
            }

            $datetime = Carbon::now(config('spot.local_timezone'))->format('YmdHis');
            Excel::store(new AdhocReportExport($payload, $header), 'semua/' . $this->formatType . '-'. $datetime . '.xlsx' , 'local_root');
        }
    }

    private function getInvalidRows($collections)
    {
        foreach ($collections as $idx => $collection) {
            $this->line('Validating entry '. ($idx + 1));

            switch ($this->formatType) {
                case 'physical':
                case 'downstream-dispatch':
                case 'downstream-receipt':
                case 'bulking':
                    $reason = $this->isInOrder($collection);
                    break;

                case 'ffb':
                    $reason = $this->isInOrderFfb($collection);
                    break;
                
                case 'rubber':
                    $reason = $this->isInOrderRubber($collection);
                    break;

                default:
                    throw new \Exception("Unknown format");
                    break;
            }

            if ( $reason !== true) {
                $this->warn($reason);
                yield $reason => $collection;
            }

        }
    }

    private function isInOrder($item)
    {

        $ticketNumber = $item['ticket_no'];

        if ($this->formatType == 'downstream-dispatch') {
            $ticketNumber = $item['ticket_no'] . '/' . $item['do'];
        }

        $load = PhysicalLoad::where('reference_no', $ticketNumber)->where('contract_number', $item['contract'])->first();

        if (!$load) {
            return "Load {$item['ticket_no']} not found for contract {$item['contract']}";
        }

        if (isset($item['refinery_wt'])) {
            if ($load->dispatched_received_quantity != $item['refinery_wt']) {
                return "SW Refinery Wt {$item['refinery_wt']} not match SPOT Dispatch Wt {$load->dispatched_received_quantity}";    
            }
        }

        if (isset($item['mill_wt'])) {
            if ($load->dispatched_received_quantity != $item['mill_wt'] ) {
                return "SW Mill Wt {$item['mill_wt']} not match SPOT Dispatch Wt {$load->dispatched_received_quantity}";
            }
        }

        if (isset($item['buyer_wt'])) {
            if ($load->buyer_weight != $item['buyer_wt'] ) {
                return "SW Buyer Wt {$item['buyer_wt']} not match SPOT Buyer Wt {$load->buyer_weight}";
            }
        }

        if (isset($item['bulking_wt'])) {
            if ($load->invoiceable_quantity != $item['bulking_wt'] ) {
                return "SW Bulking Wt {$item['bulking_wt']} not match SPOT Invoiceable Wt {$load->invoiceable_quantity}";
            }
        }

        return true;
    }

    private function isInOrderFfb($item)
    {

        $load = PhysicalLoad::where('reference_no', $item['ticket_no'])->first();

        if (!$load) {
            return "Load {$item['ticket_no']} not found";
        }

        if (strval($load->dispatched_received_quantity) != $item['supplier_wt'] ) {
            return "SW Supplier Wt {$item['mill_wt']} not match SPOT Dispatch Wt {$load->dispatched_received_quantity}";
        }

        if (strval($load->invoiceable_quantity) != $item['mill_wt'] ) {
            return "SW Supplier Wt {$item['buyer_wt']} not match SPOT Invoiceable Wt {$load->invoiceable_quantity}";
        }

        return true;
    }

    private function isInOrderRubber($item)
    {

        $load = PhysicalLoad::where('reference_no', $item['ticket_no'])->where('contract_number', $item['contract'])->first();

        if (!$load) {
            return "Load {$item['ticket_no']} not found for contract {$item['contract']}";
        }

        if (strval($load->dispatched_received_quantity/1000) != $item['factory_wt'] ) {
            return "SW Factory Wt {$item['factory_wt']} not match SPOT Dispatch Wt " . strval($load->dispatched_received_quantity/1000) ;
        }

        if (strval($load->invoiceable_quantity/1000) != $item['invoice_wt'] ) {
            return "SW Invoice Wt {$item['invoice_wt']} not match SPOT Invoiceable Wt " . strval($load->invoiceable_quantity/1000);
        }

        return true;
    }

    private function mapPayload($payload)
    {
        if ($this->formatType == 'physical') {

            $contract = $this->contractService->getModelFromContractNumber($payload['contract']);

            return [
                "customer" => $payload["customer"],
                "contract" => $payload["contract"],
                "basisx_ex_millb_delivered" => $payload["basisx_ex_millb_delivered"],
                "location" => $payload["location"],
                "ticket_no" => $payload["ticket_no"],
                "do_date" => $payload["do_date"],
                "consignmentno" => $payload["consignmentno"],
                "driver" => $payload["driver"],
                "vehicle" => $payload["vehicle"],
                "mill_wt" => $payload["mill_wt"],
                "buyer_wt" => $payload["buyer_wt"],
                "contract_status" => $contract ? $contract->status : null,
            ];
        }

        if ($this->formatType == 'downstream-dispatch') {

            $contract = $this->contractService->getModelFromContractNumber($payload['contract']);

            return [
                'location' => $payload['location'],
                'plant' => $payload['plant'],
                'customer' => $payload['customer'],
                'customer_code' => $payload['customer_code'],
                'contract' => $payload['contract'],
                'so' => $payload['so'],
                'do' => $payload['do'],
                'basis' => $payload['basis'],
                'ticket_no' => $payload['ticket_no'],
                'date' => $payload['date'],
                'driver' => $payload['driver'],
                'vehicle' => $payload['vehicle'],
                'refinery_wt' => $payload['refinery_wt'],
                'buyer_wt' => $payload['buyer_wt'],
                "contract_status" => $contract ? $contract->status : null,
            ];
        }

        if ($this->formatType == 'downstream-receipt') {

            $contract = $this->contractService->getModelFromContractNumber($payload['contract']);

            return [
                'location' => $payload['location'],
                'plant' => $payload['plant'],
                'supplier' => $payload['supplier'],
                'supplier_code' => $payload['supplier_code'],
                'mill' => $payload['mill'],
                'contract' => $payload['contract'],
                'po' => $payload['po'],
                'ticket_no' => $payload['ticket_no'],
                'date' => $payload['date'],
                'driver' => $payload['driver'],
                'vehicle' => $payload['vehicle'],
                'refinery_wt' => $payload['refinery_wt'],
                "contract_status" => $contract ? $contract->status : null,
            ];
        }

        if ($this->formatType == 'ffb') {
            
            return [
                "supplier" => $payload["supplier"],
                "location" => $payload["location"],
                "ticket_no" => $payload["ticket_no"],
                "date" => $payload["date"],
                "driver" => $payload["driver"],
                "vehicle" => $payload["vehicle"],
                "mill_wt" => $payload["mill_wt"],
                "supplier_wt" => $payload["supplier_wt"],
            ];
        }

        if ($this->formatType == 'rubber') {
            return [
                'customer' => $payload['customer'],
                'contract' => $payload['contract'],
                'basis' => $payload['basis'],
                'location' => $payload['location'],
                'ticket_no' => $payload['ticket_no'],
                'do_date' => $payload['do_date'],
                'driver' => $payload['driver'],
                'vehicle' => $payload['vehicle'],
                'factory_wt' => $payload['factory_wt'],
                'invoice_wt' => $payload['invoice_wt'],
            ];
        }

        if ($this->formatType == 'bulking') {

            $contract = $this->contractService->getModelFromContractNumber($payload['contract']);

            return [
                'location' => $payload['location'],
                'supplier' => $payload['supplier'],
                'supplier_code' => $payload['supplier_code'],
                'mill' => $payload['mill'],
                'contract' => $payload['contract'],
                'ticket_no' => $payload['ticket_no'],
                'date' => $payload['date'],
                'driver' => $payload['driver'],
                'vehicle' => $payload['vehicle'],
                'bulking_wt' => $payload['bulking_wt'],
                'contract_status' => $contract ? $contract->status : null,
            ];
        }

        throw new \Exception("Unknown format");
    }
}
