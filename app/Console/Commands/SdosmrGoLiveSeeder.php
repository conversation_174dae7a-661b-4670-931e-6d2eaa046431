<?php

namespace App\Console\Commands;

use App\Models\BankAccount;
use App\Models\BusinessUnit;
use App\Models\ContractType;
use App\Models\Counterparty;
use App\Models\Country;
use App\Models\DeliverTo;
use App\Models\DeliverToCounterParty;
use App\Models\ExternalSystem;
use App\Models\IntegrationReference;
use App\Models\InventoryLocation;
use App\Models\LegalEntity;
use App\Models\LegalEntityContractType;
use App\Models\LegalEntityTax;
use App\Models\PaymentTerm;
use App\Models\PaymentTermsProfitCenter;
use App\Models\PricingType;
use App\Models\Product;
use App\Models\ProductsProfitCenter;
use App\Models\Region;
use App\Models\State;
use App\Models\Tax;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SdosmrGoLiveSeeder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sdosmr:seed {--actual} {--action=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run seeder for SDOSMR data during SDOSMR go live';

    protected $isActual;
    protected $action;
    protected $counterparties_data;

    const ACTION_PRODUCT = 'product';
    const ACTION_PAYMENT_TERMS = 'payment-term';
    const ACTION_DUPLICATE_COUNTERPARTY = 'duplicate-counterparty';
    const ACTION_PROFIT_CENTER = 'profit-center';
    const ACTION_USER = 'user';
    const ACTION_PRICING_TYPE = 'pricing-type';
    const ACTION_DELIVER_TO = 'deliver-to';
    const ACTION_CONTRACT_TYPE = 'contract-type';
    const ACTION_TAX = 'tax';
    const ACTION_INVENTORY_LOCATION = 'inventory-location';
    const ACTION_LEGAL_ENTITY = 'legal-entity';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->isActual = false;
        $this->action = null;
        $this->counterparties_data = [
            [
                'name' => 'PDV CHILE',
                'integration_reference_SAP_MY_vendor' => null,
                'integration_reference_SAP_MY_customer' => '1001022168',
                'integration_reference_SAP_ID_vendor' => null,
                'integration_reference_SAP_ID_customer' => '1001022168',
                'deliver_to_name' => 'P.D.V. CHILE',
            ],
            [
                'name' => 'GRAINCORP COMMODITY MANAGEMENT',
                'integration_reference_SAP_MY_vendor' => null,
                'integration_reference_SAP_MY_customer' => '1001000949',
                'integration_reference_SAP_ID_vendor' => null,
                'integration_reference_SAP_ID_customer' => '1001000949',
                'deliver_to_name' => 'GRAINCORP COMMODITY MANAGEMENT',
            ],
            [
                'name' => 'NESTLE SGP',
                'integration_reference_SAP_MY_vendor' => null,
                'integration_reference_SAP_MY_customer' => '1001001616',
                'integration_reference_SAP_ID_vendor' => null,
                'integration_reference_SAP_ID_customer' => '1001001616',
                'deliver_to_name' => 'NESTLE SGP',
            ],
            [
                'name' => 'GUTHRIE AGRI BIO',
                'integration_reference_SAP_MY_vendor' => null,
                'integration_reference_SAP_MY_customer' => '5003002530',
                'integration_reference_SAP_ID_vendor' => null,
                'integration_reference_SAP_ID_customer' => '5003002530',
                'deliver_to_name' => 'GUTHRIE AGRI BIO',
            ],
            [
                'name' => 'SDO ZWIJNDRECHT',
                'integration_reference_SAP_MY_vendor' => null,
                'integration_reference_SAP_MY_customer' => '5003002584',
                'integration_reference_SAP_ID_vendor' => null,
                'integration_reference_SAP_ID_customer' => '5003002584',
                'deliver_to_name' => 'SIME DARBY OILS ZWIJNDRECHT',
            ],
            [
                'name' => 'SDO LIVERPOOL REF',
                'integration_reference_SAP_MY_vendor' => '5003002729',
                'integration_reference_SAP_MY_customer' => '5003002747',
                'integration_reference_SAP_ID_vendor' => '5003002729',
                'integration_reference_SAP_ID_customer' => '5003002729',
                'deliver_to_name' => 'SIME DARBY OILS LIVERPOOL REFINERY'
            ],
        ];
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual');
        $this->action = $this->option('action');

        DB::transaction(function() {

            switch ($this->action) {
                case self::ACTION_DUPLICATE_COUNTERPARTY:
                    $this->duplicateCounterparty();
                    break;
                case self::ACTION_PAYMENT_TERMS:
                    $this->paymentTerms();
                    break;
                case self::ACTION_PRODUCT:
                    $this->product();
                    break;
                case self::ACTION_DELIVER_TO:
                    $this->deliverTo();
                    break;
                case self::ACTION_PROFIT_CENTER:
                    $this->profitCenter();
                    break;
                case self::ACTION_PRICING_TYPE:
                    $this->pricingType();
                    break;
                case self::ACTION_USER:
                    $this->user();
                    break;
                case self::ACTION_CONTRACT_TYPE:
                    $this->contractType();
                    break;
                case self::ACTION_TAX:
                    $this->tax();
                    break;
                case self::ACTION_INVENTORY_LOCATION:
                    $this->inventoryLocation();
                    break;
                case self::ACTION_LEGAL_ENTITY:
                    $this->legalEntity();
                    break;
                default:
                    $this->error('Action not found.');
                    break;
            }

        });

    }

    private function duplicateCounterparty()
    {

        $duplicated_counterparties = collect([]);
        $external_MY = ExternalSystem::code(ExternalSystem::SAP)->first();
        if (empty($external_MY)) {
            $this->error("External System SAP MY not found.");
            return;
        }
        $external_ID = ExternalSystem::code(ExternalSystem::SAP_ID)->first();
        if (empty($external_ID)) {
            $this->error("External System SAP ID not found.");
            return;
        }

        DB::transaction(function() use (&$duplicated_counterparties, $external_MY, $external_ID) {
            foreach ($this->counterparties_data as $datum) {

                $new_counterparty_ref = collect([]);

                $counterparty = Counterparty::where('name', $datum['name'])->where('is_minamas_integration', true)->first();
                if (empty($counterparty)) {

                    $this->info("Proceeding to duplicate from MY for counterparty {$datum['name']}");
                    $counterparty_MY = Counterparty::where('name', $datum['name'])->first();

                    if (!empty($counterparty_MY)) {
                        $duplicating_counterparty = $counterparty_MY;
                    } else {
                        $this->error("Counterparty {$datum['name']} not found from MY counterparties. Skip duplicating.");
                        continue;
                    }

                    $counterparty_data = $duplicating_counterparty->toArray();
                    $bank_accounts = $duplicating_counterparty->bankAccounts;

                    if ($this->isActual) {
                        $this->info("Duplicating counterparty {$datum['name']} for SDOSMR");
                        unset($counterparty_data['id']);
                        $counterparty_data['is_png_integration'] = 0;
                        $counterparty_data['is_minamas_integration'] = 1;
                        $counterparty_data['is_active'] = 1;
                        $duplicated_counterparties->push($new_counterparty = Counterparty::create($counterparty_data));
                        $this->info("Duplicated counterparty {$datum['name']} for SDOSMR where is_minamas_integration is true, new counterparty id : {$new_counterparty->id}");

                        $this->info("Duplicating bank accounts for new counterparty");

                        foreach ($bank_accounts as $bank_account) {
                            $this->info("Duplicating bank account id {$bank_account->id} for new counterparty id : {$new_counterparty->id}");
                            $bank_account_data = $bank_account->toArray();
                            unset($bank_account_data['id']);
                            $bank_account_data['counterparty_id'] = $new_counterparty->id;
                            $new_bank_account = BankAccount::updateOrCreate($bank_account_data);
                            $this->info("Duplicated bank account new id: {$new_bank_account->id}, name: {$new_bank_account->account_name} for new counterparty id : {$new_counterparty->id}");
                        }

                        if (!empty($datum['integration_reference_SAP_ID_vendor'])) {
                            $new_counterparty_ref->push(IntegrationReference::updateOrCreate([
                                'integratable_type' => Counterparty::class,
                                'integratable_id' =>  $new_counterparty->id,
                                'external_system_id' => $external_ID->id,
                                'type' => 'vendor_id'
                            ], [
                                'reference_no' => $datum['integration_reference_SAP_ID_vendor']
                            ]));
                        }

                        if (!empty($datum['integration_reference_SAP_ID_customer'])) {
                            $new_counterparty_ref->push(IntegrationReference::updateOrCreate([
                                'integratable_type' => Counterparty::class,
                                'integratable_id' =>  $new_counterparty->id,
                                'external_system_id' => $external_ID->id,
                                'type' => 'customer_id'
                            ], [
                                'reference_no' => $datum['integration_reference_SAP_ID_customer']
                            ]));
                        }

                        $this->info("Created or updated ".count($new_counterparty_ref)." integration reference for {$datum['name']}");

                    }
                }
            }
        });
        $this->info(count($duplicated_counterparties) . " counterparty duplicated for SDOSMR " . json_encode($duplicated_counterparties->pluck('name')) );

    }

    private function product()
    {
        $this->info('Seeding product');
        $file = storage_path('csv-source/sdosmr_product_master.csv');

        if(file_exists($file)){
            $row = 0;

            $external_SW_ID = ExternalSystem::code(ExternalSystem::SIMEWEIGH_ID)->first();
            if (empty($external_SW_ID)) {
                $this->error("External System SIMEWEIGH ID not found.");
                return;
            }
            $external_SAP_ID = ExternalSystem::code(ExternalSystem::SAP_ID)->first();
            if (empty($external_SAP_ID)) {
                $this->error("External System SAP ID not found.");
                return;
            }

            $product_id_list = [];

            if (($handle = fopen($file, "r")) !== false) {

                $updated_product_integration_ref = collect([]);

                DB::transaction(function() use (&$handle, &$row, &$updated_product_integration_ref, $external_SW_ID, $external_SAP_ID, &$product_id_list) {
                    while (($data = fgetcsv($handle, null, ',')) !== false) {
                        $row ++;
                        // skip header row
                        if($row == 1){
                            continue;
                        }
                        // $data['0'] = code
                        // $data['1'] = description
                        // $data['2'] = minamas_description
                        // $data['3'] = contract_number_reference
                        // $data['4'] = minamas_contract_number_reference
                        // $data['5'] = code2 (packing unit)
                        // $data['6'] = category
                        // $data['7'] = code3 (uom)
                        // $data['8'] = rspo_type
                        // $data['9'] = SW ID integration ref
                        // $data['10'] = SAP ID integration ref (new)
                        // $data['11'] = SAP ID integration ref (old)

                        $product = Product::where('code', $data['0'])->first();

                        if(empty($product)){
                            $this->error("Product {$data['0']} not found.");
                            continue;
                        }

                        $product_id_list[] = $product->id;

                        if ($this->isActual) {
                            if(strtoupper($data['9']) !== 'NULL'){
                                $updated_product_integration_ref->push(IntegrationReference::firstOrCreate([
                                    'integratable_type' => Product::class,
                                    'integratable_id' => $product->id,
                                    'external_system_id' => $external_SW_ID->id,
                                    'type' => 'code'
                                ], [
                                    'reference_no' => $data['9']
                                ]));
                            }

                            if(strtoupper($data['10']) !== 'NULL'){
                                $updated_product_integration_ref->push(IntegrationReference::firstOrCreate([
                                    'integratable_type' => Product::class,
                                    'integratable_id' => $product->id,
                                    'external_system_id' => $external_SAP_ID->id,
                                    'type' => 'code'
                                ], [
                                    'reference_no' => $data['10']
                                ]));
                            }
                            $this->info(count($updated_product_integration_ref) . " product integration reference created/updated ");
                        }
                    }


                    $profit_center = Counterparty::where('code', 'SMR')->where('is_internal', true)->first();

                    if(empty($profit_center)){
                        $this->error("SDOSMR profit center not found");
                    } else{
                        if ($this->isActual) {
                            $product_assigned = collect();
                            foreach($product_id_list as $product_id){
                                $product_assigned->push(ProductsProfitCenter::updateOrCreate([
                                    'counterparty_id' => $profit_center->id,
                                    'product_id' => $product_id
                                ]));
                            }
                            $this->info(count($product_assigned) . " product assigned to profit center.");
                        }
                    }

                });
                fclose($handle);
            }
        }else{
            $this->error('File not found');
        }
    }

    private function paymentTerms()
    {
        $file = storage_path('csv-source/sdosmr_payment_terms_master.csv');

        if(file_exists($file)){
            $row = 0;

            $external_SW_ID = ExternalSystem::code(ExternalSystem::SIMEWEIGH_ID)->first();
            if (empty($external_SW_ID)) {
                $this->error("External System SIMEWEIGH ID not found.");
                return;
            }
            $external_SAP_ID = ExternalSystem::code(ExternalSystem::SAP_ID)->first();
            if (empty($external_SAP_ID)) {
                $this->error("External System SAP ID not found.");
                return;
            }

            $payment_term_id_list = [];

            if (($handle = fopen($file, "r")) !== false) {

                $updated_payment_term_integration_ref = collect([]);

                DB::transaction(function() use (&$handle, &$row, &$updated_payment_term_integration_ref, $external_SW_ID, $external_SAP_ID, &$payment_term_id_list) {
                    while (($data = fgetcsv($handle, null, ',')) !== false) {
                        $row ++;
                        // skip header row
                        if($row == 1){
                            continue;
                        }

                        // $data['0'] = contract_description
                        // $data['1'] = invoice_description
                        // $data['2'] = payment_mode
                        // $data['3'] = lc_type
                        // $data['4'] = is_advance
                        // $data['5'] = due_date_days
                        // $data['6'] = SW ID integration ref
                        // $data['7'] = SAP ID integration ref

                        $payment_term = PaymentTerm::where('contract_description', $data['0'])
                            ->where('payment_mode', $data['2'])
                            ->where('lc_type', $data['3'])
                            ->where('is_advance', $data['4'])
                            ->where('due_date_days', $data['5'])
                            ->first();

                        if(empty($payment_term)){
                            $this->error("Payment Term {$data['0']} not found.");
                            continue;
                        }

                        $payment_term_id_list[] = $payment_term->id;

                        if ($this->isActual) {
                            if(strtoupper($data['6']) !== 'NULL'){
                                $updated_payment_term_integration_ref->push(IntegrationReference::updateOrCreate([
                                    'integratable_type' => PaymentTerm::class,
                                    'integratable_id' => $payment_term->id,
                                    'external_system_id' => $external_SW_ID->id,
                                    'type' => 'id'
                                ], [
                                    'reference_no' => $data['6']
                                ]));
                            }

                            if(strtoupper($data['7']) !== 'NULL'){
                                $updated_payment_term_integration_ref->push(IntegrationReference::updateOrCreate([
                                    'integratable_type' => PaymentTerm::class,
                                    'integratable_id' => $payment_term->id,
                                    'external_system_id' => $external_SAP_ID->id,
                                    'type' => 'id'
                                ], [
                                    'reference_no' => $data['7']
                                ]));
                            }
                        }
                    }

                    // assign payment terms to SDOSMR
                    $payment_terms_assigned = collect([]);

                    $profit_center_SMR = Counterparty::where('code', 'SMR')->where('is_internal', true)->first();

                    if(empty($profit_center_SMR)){
                        $this->error("Profit center SMR not found.");
                    }else{
                        $id = $profit_center_SMR->id;
                    }

                    if ($this->isActual) {
                        foreach($payment_term_id_list as $payment_term_id){
                            $payment_terms_assigned->push(PaymentTermsProfitCenter::updateOrCreate([
                                'payment_terms_id' => $payment_term_id,
                                'counterparty_id' => $id
                            ]));
                        }
                    }
                    $this->info(count($payment_terms_assigned) . " payment term assigned to profit center SMR.");


                });
                $this->info(count($updated_payment_term_integration_ref) . " payment term integration reference created/updated ");
                fclose($handle);
            }
        }else{
            $this->error('File not found');
        }
    }

    private function profitCenter()
    {
        $this->info('Seeding SDOSMR Profit Center');

        // $sdoe profit center
        $sdosmr_le = LegalEntity::where('code', 'SMR')->first();

        if ( $sdosmr_le == null ){
            throw new \Exception('Please create SDOSMR legal entity first.');
        }

        $profit_center = null;

        $external_sw_id = ExternalSystem::code(ExternalSystem::SIMEWEIGH_ID)->first();
        if (empty($external_sw_id)) {
            $this->error("External System SW ID not found.");
            return;
        }

        $external_sap_id = ExternalSystem::code(ExternalSystem::SAP_ID)->first();
        if (empty($external_sap_id)) {
            $this->error("External System SAP ID not found.");
            return;
        }

        if ( $this->isActual ){

            $profit_center = Counterparty::updateOrCreate([
                'code' => 'SMR',
                'is_internal' => 1,
            ], [
                'name' => 'SDO SEI MANGKEI REFINERY',
                'long_name' => 'PT. SIME DARBY OILS SEI MANGKEI REFINERY',
                'legal_entity_id' => $sdosmr_le->id,
                'is_upstream' => 0,
                'is_downstream' => 1,
                'is_minamas_integration' => 1,
//                'company_registration_number' => 'pending',
//                'tax_registration_number' => 'pending',
                'is_active' => 1,
                'address' => "JL. MH. THAMRIN KAV. 28-30,\nTHE PLAZA OFFICE TOWER LT. 36 RT. 009 RW. 005,\nGONDANGDIA MENTENG JAKARTA PUSAT - DKI JAKARTA RAYA - 10350",
                'country' => 'ID',
                'billing_address' => "JL. MH. THAMRIN KAV. 28-30,\nTHE PLAZA OFFICE TOWER LT. 36 RT. 009 RW. 005,\nGONDANGDIA MENTENG JAKARTA PUSAT - DKI JAKARTA RAYA - 10350",
                'billing_country' => 'ID',
                'is_trading' => 0,
            ]);

            $this->info('Created/Updated SDOSMR profit center');

            $this->info('Updating integration reference');

            $new_ref = IntegrationReference::updateOrCreate([
                'integratable_type' => Counterparty::class,
                'integratable_id' =>  $profit_center->id,
                'external_system_id' => $external_sw_id->id,
                'type' => 'customer_id'
            ], [
                'reference_no' => 'SMR'
            ]);

            $new_ref = IntegrationReference::updateOrCreate([
                'integratable_type' => Counterparty::class,
                'integratable_id' =>  $profit_center->id,
                'external_system_id' => $external_sw_id->id,
                'type' => 'vendor_id'
            ], [
                'reference_no' => 'SMR'
            ]);

            $new_ref = IntegrationReference::updateOrCreate([
                'integratable_type' => Counterparty::class,
                'integratable_id' =>  $profit_center->id,
                'external_system_id' => $external_sap_id->id,
                'type' => 'customer_id'
            ], [
                'reference_no' => '5003002755'
            ]);

            $new_ref = IntegrationReference::updateOrCreate([
                'integratable_type' => Counterparty::class,
                'integratable_id' =>  $profit_center->id,
                'external_system_id' => $external_sap_id->id,
                'type' => 'vendor_id'
            ], [
                'reference_no' => '5003002755'
            ]);

            $this->info('Updated integration reference');

        }

        return $profit_center;
    }

    private function pricingType()
    {
        $this->info('Seeding SDOSMR pricing type');

        $profit_center = Counterparty::where('code', 'SMR')->firstOrFail();

        $to_add_pricing_types = [
            PricingType::TYPE_OUTRIGHT,
            PricingType::TYPE_PROVISIONAL,
            PricingType::TYPE_MPOB
        ];

        $pricing_types = PricingType::whereIn('code', $to_add_pricing_types)->get();

        if ( $this->isActual ) {
            $profit_center->pricingTypes()->sync(
                $pricing_types->pluck('id')
            );

            $this->info('Assigned pricing types to profit center');
        }
    }

    private function user()
    {
        $this->info('Seeding SDOSMR user');

        $profit_center = Counterparty::where('code', 'SMR')->firstOrFail();

        $to_add_user_email = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];

        $this->info('Total users to attach: ' . count($to_add_user_email));

        $users = User::whereIn('email', $to_add_user_email)->get();

        $this->info('Total users found: ' . count($users));

        if (count($to_add_user_email) != count($users)) {
            $missing_users = array_diff($to_add_user_email, $users->pluck('email')->toArray());
            $this->info('User not found: ' . json_encode(array_values($missing_users)));
        }

        if ( $this->isActual ) {
            $profit_center->users()->syncWithoutDetaching($users->pluck('id'));
            $this->info('Assigned users to profit center.');
        }
    }

    private function deliverTo()
    {
        $this->info('Seeding SDOSMR deliver to.');

        $profit_center_SMR = Counterparty::where('code', 'SMR')->where('is_internal', true)->first();

        $external_sw_id = ExternalSystem::code(ExternalSystem::SIMEWEIGH_ID)->first();
        if (empty($external_sw_id)) {
            $this->error("External System SW ID not found.");
            return;
        }

        $external_sap_id = ExternalSystem::code(ExternalSystem::SAP_ID)->first();
        if (empty($external_sap_id)) {
            $this->error("External System SAP ID not found.");
            return;
        }

        $new_deliver_to = [
            'name' => 'SDOSMR',
            'company_name' => 'SDO SEI MANGKEI REFINERY',
            'country_id' => Country::where('code', 'ID')->first()->id,
            'state_id' => State::where('name', 'NA')->first()->id,
            'address' => 'SDO SEI MANGKEI REFINERY',
        ];

        $existing_deliver_to = [
            'P.D.V. CHILE' => '2168',
            'GRAINCORP COMMODITY MANAGEMENT' => '0949',
            'NESTLE SGP' => '5618',
            'GUTHRIE AGRI BIO' => '8751',
            'SIME DARBY OILS ZWIJNDRECHT' => '8716',
            'SIME DARBY OILS LIVERPOOL REFINERY' => '5003002729'
        ];

        $deliver_tos = DeliverTo::whereIn('name', array_keys($existing_deliver_to))->get();

        // create smr deliver to
        $smr_deliver_to = DeliverTo::firstOrCreate(['name' => 'SDOSMR'], $new_deliver_to);
        $this->info('Created new deliver to');

        if ( $this->isActual ) {
            DeliverToCounterParty::firstOrCreate([
                'deliver_to_id' => $smr_deliver_to->id,
                'counterparty_id' => $profit_center_SMR->id
            ]);
        }

        $this->info('Assigned SDOSMR profit center to new deliver to');

        $updated_model = [];
        if ( $this->isActual ) {

            $new_ref = IntegrationReference::updateOrCreate([
                'integratable_type' => DeliverTo::class,
                'integratable_id' => $smr_deliver_to->id,
                'external_system_id' => $external_sw_id->id,
                'type' => 'customer_id'
            ], [
                'reference_no' => 'SMR'
            ]);
            $updated_model[] = $new_ref->integratable_id;

        }

        if ( $this->isActual ) {

            $new_ref = IntegrationReference::updateOrCreate([
                'integratable_type' => DeliverTo::class,
                'integratable_id' =>  $smr_deliver_to->id,
                'external_system_id' => $external_sw_id->id,
                'type' => 'vendor_id'
            ], [
                'reference_no' => 'SMR'
            ]);

            $updated_model[] = $new_ref->integratable_id;

            $new_ref = IntegrationReference::updateOrCreate([
                'integratable_type' => DeliverTo::class,
                'integratable_id' =>  $smr_deliver_to->id,
                'external_system_id' => $external_sap_id->id,
                'type' => 'customer_id'
            ], [
                'reference_no' => '9908/5003002755'
            ]);

            $updated_model[] = $new_ref->integratable_id;

            $new_ref = IntegrationReference::updateOrCreate([
                'integratable_type' => DeliverTo::class,
                'integratable_id' =>  $smr_deliver_to->id,
                'external_system_id' => $external_sap_id->id,
                'type' => 'vendor_id'
            ], [
                'reference_no' => '9908/5003002755'
            ]);

            $updated_model[] = $new_ref->integratable_id;

        }

        // customer id
        foreach ($deliver_tos as $deliver_to) {

            if ($this->isActual) {
                $new_ref = IntegrationReference::updateOrCreate([
                    'integratable_type' => DeliverTo::class,
                    'integratable_id' =>  $deliver_to->id,
                    'external_system_id' => $external_sw_id->id,
                    'type' => 'customer_id'
                ], [
                    'reference_no' => $existing_deliver_to[$deliver_to->name]
                ]);

                $updated_model[] = $new_ref->integratable_id;

                $new_ref = IntegrationReference::updateOrCreate([
                    'integratable_type' => DeliverTo::class,
                    'integratable_id' =>  $deliver_to->id,
                    'external_system_id' => $external_sap_id->id,
                    'type' => 'customer_id'
                ], [
                    'reference_no' => $existing_deliver_to[$deliver_to->name]
                ]);

                $updated_model[] = $new_ref->integratable_id;
            }

        }

        // vendor id
        unset($existing_deliver_to['P.D.V. CHILE']);
        unset($existing_deliver_to['GUTHRIE AGRI BIO']);

        foreach ($deliver_tos as $deliver_to) {
            if (!isset($existing_deliver_to[$deliver_to->name])) {
                continue;
            }

            if ($this->isActual) {
                $new_ref = IntegrationReference::updateOrCreate([
                    'integratable_type' => DeliverTo::class,
                    'integratable_id' =>  $deliver_to->id,
                    'external_system_id' => $external_sw_id->id,
                    'type' => 'vendor_id'
                ], [
                    'reference_no' => $existing_deliver_to[$deliver_to->name]
                ]);

                $updated_model[] = $new_ref->integratable_id;

                $new_ref = IntegrationReference::updateOrCreate([
                    'integratable_type' => DeliverTo::class,
                    'integratable_id' =>  $deliver_to->id,
                    'external_system_id' => $external_sap_id->id,
                    'type' => 'vendor_id'
                ], [
                    'reference_no' => $existing_deliver_to[$deliver_to->name]
                ]);

                $updated_model[] = $new_ref->integratable_id;
            }

        }

        $this->info(count($updated_model) . " deliver to integration reference created: " . json_encode($updated_model) );

        $this->info('Assigning deliver to to Minamas counterparties');
        $assigned_deliver_to = collect([]);
        foreach ($this->counterparties_data as $counterparties_datum) {
            $counterparty = Counterparty::where('name', $counterparties_datum['name'])
                ->where('is_minamas_integration', true)->first();
            $to_assign_deliver_to = $deliver_tos->where('name', $counterparties_datum['deliver_to_name'])->first();
            if (empty($counterparties_datum)) {
                $this->error('Counterparty ' . $counterparties_datum['name'] . ' not found');
                continue;
            }
            if (empty($to_assign_deliver_to)) {
                $this->error('Deliver to ' . $counterparties_datum['deliver_to_name'] . ' not found');
                continue;
            }
            if ($this->isActual) {
                $assigned_deliver_to->push(DeliverToCounterParty::firstOrCreate([
                    'deliver_to_id' => $to_assign_deliver_to->id,
                    'counterparty_id' => $counterparty->id
                ]));
            }

        }
        $this->info("Assigned {$assigned_deliver_to->count()} deliver to to Minamas counterparties");
    }

    private function contractType()
    {
        $this->info('Seeding contract type');

        $minamas_business_unit = BusinessUnit::where('name', 'Minamas')->firstOrFail();

        $sdosmr_legal_entity = LegalEntity::firstOrCreate(
            ['code' => 'SMR'],
            [
                'business_unit_id' => $minamas_business_unit->id,
                'name' => 'PT. SIME DARBY OILS SEI MANGKEI REFINERY',
                'name_cn' => 'PT. SIME DARBY OILS SEI MANGKEI REFINERY',
                'is_active' => true,
                'currency' => 'IDR',
                'address' => 'JL. MH. THAMRIN KAV. 28-30, THE PLAZA OFFICE TOWER LT. 36 RT. 009 RW. 005, GONDANGDIA MENTENG JAKARTA PUSAT - DKI JAKARTA RAYA - 10350',
                'billing_address' => 'JL. MH. THAMRIN KAV. 28-30, THE PLAZA OFFICE TOWER LT. 36 RT. 009 RW. 005, GONDANGDIA MENTENG JAKARTA PUSAT - DKI JAKARTA RAYA - 10350',
                'country' => 'ID',
                'billing_country' => 'ID',
                'is_gst' => false,
                'is_do_required' => false,
                'is_minamas' => true,
                'can_include_tax_in_price' => true,
                'contract_number_format' => 'MINAMAS_DOWNSTREAM',
                'billing_number_format' => 'MINAMAS_DOWNSTREAM',
                'timezone' => 'Asia/Jakarta',
            ]
        );

        $physical_contract_type = ContractType::where('code', 'physical')->firstOrFail();

        if ( $this->isActual ) {
            LegalEntityContractType::firstOrCreate([
                'legal_entity_id' => $sdosmr_legal_entity->id,
                'contract_type_id' => $physical_contract_type->id,
            ]);

            $this->info('Assgined physical contract to SDOSMR');
        }

    }

    private function tax()
    {
        $this->info('Seeding tax');
        $minamas_business_unit = BusinessUnit::where('name', 'Minamas')->firstOrFail();

        $sdosmr_legal_entity = LegalEntity::firstOrCreate(
            ['code' => 'SMR'],
            [
                'business_unit_id' => $minamas_business_unit->id,
                'name' => 'PT. SIME DARBY OILS SEI MANGKEI REFINERY',
                'name_cn' => 'PT. SIME DARBY OILS SEI MANGKEI REFINERY',
                'is_active' => true,
                'currency' => 'IDR',
                'address' => 'JL. MH. THAMRIN KAV. 28-30, THE PLAZA OFFICE TOWER LT. 36 RT. 009 RW. 005, GONDANGDIA MENTENG JAKARTA PUSAT - DKI JAKARTA RAYA - 10350',
                'billing_address' => 'JL. MH. THAMRIN KAV. 28-30, THE PLAZA OFFICE TOWER LT. 36 RT. 009 RW. 005, GONDANGDIA MENTENG JAKARTA PUSAT - DKI JAKARTA RAYA - 10350',
                'country' => 'ID',
                'billing_country' => 'ID',
                'is_gst' => false,
                'is_do_required' => false,
                'is_minamas' => true,
                'can_include_tax_in_price' => true,
                'contract_number_format' => 'MINAMAS_DOWNSTREAM',
                'billing_number_format' => 'MINAMAS_DOWNSTREAM',
                'timezone' => 'Asia/Jakarta',
            ]
        );

        $taxes = Tax::whereIn('code', ['VAT', '0%'])->get();

        if ( $this->isActual ) {
            $inserted_taxes = collect([]);
            foreach($taxes as $tax) {
                $inserted_taxes->push(LegalEntityTax::firstOrCreate([
                    'legal_entity_id' => $sdosmr_legal_entity->id,
                    'tax_id' => $tax->id,
                ]));
            }
            $this->info('Assigned taxes to SDOSMR: ' . json_encode($inserted_taxes));
        }

    }

    private function inventoryLocation()
    {
        $this->info('Seeding inventory location');
        $minamas_business_unit = BusinessUnit::where('name', 'Minamas')->firstOrFail();

        $sdosmr_legal_entity = LegalEntity::firstOrCreate(
            ['code' => 'SMR'],
            [
                'business_unit_id' => $minamas_business_unit->id,
                'name' => 'PT. SIME DARBY OILS SEI MANGKEI REFINERY',
                'name_cn' => 'PT. SIME DARBY OILS SEI MANGKEI REFINERY',
                'is_active' => true,
                'currency' => 'IDR',
                'address' => 'JL. MH. THAMRIN KAV. 28-30, THE PLAZA OFFICE TOWER LT. 36 RT. 009 RW. 005, GONDANGDIA MENTENG JAKARTA PUSAT - DKI JAKARTA RAYA - 10350',
                'billing_address' => 'JL. MH. THAMRIN KAV. 28-30, THE PLAZA OFFICE TOWER LT. 36 RT. 009 RW. 005, GONDANGDIA MENTENG JAKARTA PUSAT - DKI JAKARTA RAYA - 10350',
                'country' => 'ID',
                'billing_country' => 'ID',
                'is_gst' => false,
                'is_do_required' => false,
                'is_minamas' => true,
                'can_include_tax_in_price' => true,
                'contract_number_format' => 'MINAMAS_DOWNSTREAM',
                'billing_number_format' => 'MINAMAS_DOWNSTREAM',
                'timezone' => 'Asia/Jakarta',
            ]
        );

        $jakarta_region = Region::where('name', 'Jakarta')->firstOrFail();

        if ($this->isActual) {
            $inventory_location = InventoryLocation::updateOrCreate(
                ['code' => 'SDOSMR'],
                [
                    'counterparty_id' => null,
                    'short_name' => 'SDOSMR',
                    'name' => 'SDOSMR',
                    'address' => 'JL. MH. THAMRIN KAV. 28-30, THE PLAZA OFFICE TOWER LT. 36 RT. 009 RW. 005, GONDANGDIA MENTENG JAKARTA PUSAT - DKI JAKARTA RAYA - 10350',
                    'region' => $jakarta_region->name,
                    'state' => 'NA',
                    'country' => 'ID',
                    'is_tender_point' => false,
                    'is_bulking' => false,
                    'is_minamas' => true,
                    'legal_entity_id' => $sdosmr_legal_entity->id,
                ]
            );

            $this->info('Created inventory location for SDOSMR');

            $sw_external_system = ExternalSystem::where('code', 'SW_ID')->firstOrFail();
            $sap_external_system = ExternalSystem::where('code', 'SAP_ID')->firstOrFail();
            IntegrationReference::firstOrCreate([
                'integratable_type' => InventoryLocation::class,
                'integratable_id' => $inventory_location->id,
                'external_system_id' => $sw_external_system->id,
                'type' => 'code',
                'reference_no' => 'SMR'
            ]);

            IntegrationReference::firstOrCreate([
                'integratable_type' => InventoryLocation::class,
                'integratable_id' => $inventory_location->id,
                'external_system_id' => $sap_external_system->id,
                'type' => 'code',
                'reference_no' => '9908/5003002755'
            ]);

            $this->info('Added integration reference to SDOSMR inventory location');
        }

    }

    private function legalEntity()
    {
        $this->info('Seeding SDOSMR legal entity');
        $minamas_business_unit = BusinessUnit::where('name', 'Minamas')->firstOrFail();

        if ($this->isActual) {
            $sdosmr_legal_entity = LegalEntity::updateOrCreate(
                ['code' => 'SMR'],
                [
                    'business_unit_id' => $minamas_business_unit->id,
                    'name' => 'PT. SIME DARBY OILS SEI MANGKEI REFINERY',
                    'name_cn' => 'PT. SIME DARBY OILS SEI MANGKEI REFINERY',
                    'is_active' => true,
                    'currency' => 'IDR',
                    'address' => 'JL. MH. THAMRIN KAV. 28-30, THE PLAZA OFFICE TOWER LT. 36 RT. 009 RW. 005, GONDANGDIA MENTENG JAKARTA PUSAT - DKI JAKARTA RAYA - 10350',
                    'billing_address' => 'JL. MH. THAMRIN KAV. 28-30, THE PLAZA OFFICE TOWER LT. 36 RT. 009 RW. 005, GONDANGDIA MENTENG JAKARTA PUSAT - DKI JAKARTA RAYA - 10350',
                    'country' => 'ID',
                    'billing_country' => 'ID',
                    'is_gst' => false,
                    'is_do_required' => false,
                    'is_minamas' => true,
                    'can_include_tax_in_price' => true,
                    'contract_number_format' => 'MINAMAS_DOWNSTREAM',
                    'billing_number_format' => 'MINAMAS_DOWNSTREAM',
                    'timezone' => 'Asia/Jakarta',
                ]
            );

            $this->info('Created SDOSMR legal entity');
        }
    }

}
