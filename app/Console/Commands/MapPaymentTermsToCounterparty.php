<?php

namespace App\Console\Commands;

use App\Models\PaymentTerm;
use Illuminate\Console\Command;
use Rap2hpoutre\FastExcel\FastExcel;

class MapPaymentTermsToCounterparty extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment-terms:map-counterparty';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Map Payment Terms to counterparty based on the array given';



    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }



    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $paymentTermsMapping = (new FastExcel)->import(storage_path('seeder/payment-terms-counterparty-mapping.xlsx'), function ($line) {

            return [$line['payment_terms_id'], $line['counterparty_id']];
        });

        $decoratedArr = [];

        foreach ($paymentTermsMapping as $mapping) {

            if (empty($decoratedArr[$mapping[0]])) {
                $decoratedArr[$mapping[0]] = [];
            }

            $decoratedArr[$mapping[0]][] = $mapping[1];
        }

        $paymentTerms = PaymentTerm::with('counterparties')
            ->findOrFail(array_keys($decoratedArr));

        foreach ($decoratedArr as $paymentTermId => $counterparties) {

            $paymentTerm = $paymentTerms->find($paymentTermId);
            $existingCounterpartyIds = count($paymentTerm->counterparties) > 0 ?
                $paymentTerm->counterparties->pluck('id')->all() :
                [];

            foreach ($counterparties as $counterpartyId) {
                if (!in_array($counterpartyId, $existingCounterpartyIds)) {
                    $paymentTerm->counterparties()->attach($counterpartyId);
                }
            }
        }
    }
}
