<?php

namespace App\Console\Commands;

use App\Services\Integration\Lct\LctDataExport;
use App\Services\Integration\Lct\LctDataExportService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ExportLctPlatformData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lct:export-data { --upload : Upload to FTP folder }
                                            { --limit-le=* : Limit to legal entity provided (multiple). Eg: --limit-le=T --limit-le=W }
                                            { --limit-pc-in-le=* : Limit to exclude sibling profit centers not specified in this option under the same legal entity provided above. Eg: Legal Entity A has A1 and A2 profit centers. --limit-pc-in-le=A1 will cancel out A2. It will not affect other legal entities in --limit-le }
                                            { --limit-pc=* : Limit to profit centers provided (multiple). Eg: --limit-pc=JC --limit-pc=AY }
                                            { --status=all : Limit the export to certain status of the contract. Default => all }
                                            { --transaction-type=all : Limit the transaction type. [Options: S or P or all] }
                                            { --from= : Set from when, else will be since beginning }
                                            { --until=now : Set until when, default today }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send GTM physical contract data to LCT data platform';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $should_upload = $this->option('upload');

        $limit_profit_centers = $this->option('limit-pc');

        $limit_legal_entities = $this->option('limit-le');

        $limit_profit_centers_in_legal_entity = $this->option('limit-pc-in-le');

        $transaction_type = $this->option('transaction-type');

        $status = strtolower($this->option('status'));

        $from = $this->option('from');

        $until = $this->option('until');

        $filename = 'SPOT_' . ( $status != 'all' ? "$status" . '_' : '' ) . Carbon::now('Asia/Kuala_Lumpur')->format('YmdHis') . '.csv';

        if ($limit_legal_entities && $limit_profit_centers) {
            $this->error('Cannot use --limit-pc and --limit-le at the same time. run { php artisan help lct:export-data } to find out more');
            exit(1);
        }

        if ( empty($limit_profit_centers) && empty($limit_legal_entities) ){

            // limit to malaysia only by default
            $limit_profit_centers = [
                'MT',
                'MS',
                'CE',
                'NJ',
                'OTC',
                'GM',
                'JC',
                'KG',
                'KB',
                'JR',
                'SD',
                'NK',
                'NR',
                'AK',
                'AR',
                'AP',
                'AY',
                'SM',
                'SW',
                'SK',
                'WM',
                'SB',
                'KE',
                'WK',
                'WR',
                'DL',
                'TL',
                'DT',
                'TR',
                'TH',
                'OF',
                'LR',
                'DS',   // SDOTPL
                'TS',   // SDOTPL
                'CH',  //SDOC
            ];
        }

        $options = [
            'upload' => $should_upload ?? false,
            'filename' => $filename,
            'limit_profit_centers' => $limit_profit_centers,
            'status' => $status,
            'limit_legal_entities' => $limit_legal_entities,
            'limit_profit_centers_in_legal_entity' => $limit_profit_centers_in_legal_entity,
            'transaction_type' => $transaction_type,
            'from' => $from,
            'until' => $until,
        ];

        $this->line('Begin generate physical contract data for GTM | Filename: ' . $filename );
        $this->line('Options: ' . collect($options)->map(function($v, $k) {
            if ( $k == 'upload' && $v == false ){
                $v = '0';
            }
            return $k . " : " . (is_array($v) ? implode(", ", $v) : $v);
        })->flatten()->join(' | '));

        $lctDataExportService = new LctDataExportService();

        $lctDataExportService->setOptions($options);

        $this->line('Start process | Date: ' . Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'));
        $lctDataExportService->generate();
        $this->info('Process ends | Date: ' . Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'));
        $this->line('Additional information please check integration_logs table or storage/logs/integration-log-' . Carbon::now()->format('Ymd') . '.log');

    }
}
