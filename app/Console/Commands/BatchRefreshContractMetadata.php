<?php

namespace App\Console\Commands;

use App\Jobs\ProcessOutboundBusinessScenario;
use App\Models\Allocation;
use App\Models\Contract;
use App\Models\ContractStatus;
use App\Models\Counterparty;
use App\Models\InventoryLocation;
use App\Models\LegalEntity;
use App\Models\PhysicalContract;
use App\Repositories\AllocationRepository;
use App\Services\GeneralContractService;
use App\Services\IntegrationService;
use App\Services\PhysicalContractService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BatchRefreshContractMetadata extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'metadata:batch-refresh {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Batch refresh physical contract metadata';

    private $contractService;

    private $physicalContractService;

    private $allocationRepository;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->contractService = app(GeneralContractService::class);
        $this->physicalContractService = app()->make(PhysicalContractService::class);
        $this->allocationRepository = app(AllocationRepository::class);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Running SDO Restructuring contract migration script..');

        $counterparties = Counterparty::where('is_active', true)
            ->where('is_rubber_integration', false)
            ->where('is_minamas_integration', false)
            ->get()
            ->mapWithKeys(function($item) {
                return [$item->id => $item];
            });

        $legal_entities = LegalEntity::get()->mapWithKeys(function($item) {
            return [$item->id => $item];
        });

        // get excel file
        $file = fopen(storage_path('batch_refresh_contract_metadata.csv'), 'r');

        $list = [];

        $line_count = 0;

        $errors = [];

        while (($line = fgetcsv($file)) !== FALSE) {

            if ( $line_count == 0 ){
                $line_count++;
                continue;
            }

            if ( strlen($line[0]) == 0 ){
                $line_count++;
                continue;
            }

            $line_count++;

            $data = [
                'contract_number' => trim($line[0]),
                'contract_model' => null
            ];

            // Validate that it's a real contract
            $contract = PhysicalContract::where('contract_number', $data['contract_number'])
                ->whereIn('status', ContractStatus::STATUSES_NOT_DRAFT_AND_VOIDED)
                ->first();

            if ( $contract == null ){
                $errors[] = 'Invalid Contract number ' . $data['contract_number'] . ' or contract status not confirmed.';
            }

            if ( !isset($counterparties[$contract->counterparty_id]) ){
                $errors[] = 'Counterparty ID ' . $contract->counterparty_id . ' not found in master data for contract ' . $data['contract_number'];
            }
            if ( !isset($counterparties[$contract->profit_center_id]) ){
                $errors[] = 'Profit Center ID ' . $contract->profit_center_id . ' not found in master data for contract ' . $data['contract_number'];
            }
            if ( !isset($legal_entities[$contract->legal_entity_id]) ){
                $errors[] = 'Legal Entity ID ' . $contract->legal_entity_id . ' not found in master data for contract ' . $data['contract_number'];
            }

            if ( count($errors) > 0 ){
                continue;
            }

            $data['contract_model'] = $contract;

            $this->info("Found {$data['contract_number']}");

            $list[] = $data;

        }

        fclose($file);

        if ( count($errors) > 0 ){
            throw new \Exception(join("\n", $errors));
        }

        if ( $this->option('actual') != null ){

            // foreach line
            Auth::loginUsingId(1);

            foreach ( $list as $index => $l ){
                $this->info("Processing " . $l['contract_number'] . " refresh metadata");

                $l['contract_model']->legal_entity_name = $legal_entities[$l['contract_model']->legal_entity_id]->name;
                $l['contract_model']->counterparty_name = $counterparties[$l['contract_model']->counterparty_id]->name;
                $l['contract_model']->profit_center_name = $counterparties[$l['contract_model']->profit_center_id]->name;

                $this->physicalContractService->refreshMetadata($l['contract_model']);
            }

            $this->info("DONE!");

        }else{
            $this->info("End dry run");
        }

    }

}
