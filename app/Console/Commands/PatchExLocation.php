<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Counterparty;
use App\Models\ContractStatus;
use Illuminate\Console\Command;
use App\Models\PhysicalContract;
use Illuminate\Support\Facades\DB;
use App\Services\AllocationService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use App\Services\PhysicalContractService;
use Illuminate\Database\Schema\Blueprint;

class PatchExLocation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:exlocation {profit_center_code}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch old contracts inventory location';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $dispatcher = PhysicalContract::getEventDispatcher();
        PhysicalContract::unsetEventDispatcher();

        $profit_center_codes = $this->argument('profit_center_code');

        $profit_center_codes = explode(',', $profit_center_codes);
        $profit_center_codes = array_map('trim', $profit_center_codes);
        $profit_center_codes = array_map('strtoupper', $profit_center_codes);

        $profit_center_ids = Counterparty::whereIn('code', $profit_center_codes)->pluck('id');

        if ( count($profit_center_ids) === 0 ) {
            throw new \Exception('No profit center found');
        }

        $updatedCount = 0;

        $allocationService = app()->make(AllocationService::class);
        $physicalContractService = app()->make(PhysicalContractService::class);

        DB::transaction(function() use (&$updatedCount, $profit_center_ids, $allocationService, $physicalContractService){
            PhysicalContract::with(['allocations', 'allocations.mill'])
                ->whereNotIn('status', [ContractStatus::TYPE_DRAFT, ContractStatus::TYPE_POSTED])
                ->whereIn('profit_center_id', $profit_center_ids)
                ->whereNull('inventory_location_id')
                ->chunkById(300, function ($items) use (&$updatedCount, $allocationService, $physicalContractService) {
                    foreach ($items as $item) {

                        $this->info("Patching contract {$item->contract_number}");
                        $allocations = $allocationService->getActiveAllocation($item);

                        if(count($allocations) === 1 ){
                            $allocation = $allocations->first();
                        }else{
                            $this->info("Not exactly 1 allocation, skip.");
                            continue;
                        }

                        if($allocation && $allocation->inventory_location_id ){
                            $this->info("Updating contract {$item->contract_number} inventory location to {$allocation->inventory_location_id}");
                            $item->update(['inventory_location_id' => $allocation->inventory_location_id]);
                            $updatedCount++;
                        }
                    }
                });
        });

        PhysicalContract::setEventDispatcher($dispatcher);
        $this->info("Total updated physical contracts: {$updatedCount}\n");
    }
}
