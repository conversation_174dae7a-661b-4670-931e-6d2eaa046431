<?php

namespace App\Console\Commands;

use App\Models\QueueJobSession;
use Carbon\Carbon;
use Illuminate\Console\Command;

class AutoCloseoutSessionHousekeeping extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'housekeeping:auto-closeout-session';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'delete sessions older than 3 months';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        QueueJobSession::whereDate('created_at', '<', Carbon::now()->subMonths(3))->delete();
    }
}
