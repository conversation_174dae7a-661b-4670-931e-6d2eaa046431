<?php

namespace App\Console\Commands;

use App\Models\LegalEntity;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PatchLegalEntityPrintoutLogo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:printout-logo {legal_entity_code?} {logo?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch legal entity printout logo';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $updatedCount = 0;

        DB::transaction(function() use (&$updatedCount){

            $legal_entity_code = $this->argument('legal_entity_code');

            if($legal_entity_code){
                $legal_entity_code = explode(',', $legal_entity_code);
                $legal_entity_code = array_map('trim', $legal_entity_code);
                $legal_entities = LegalEntity::whereIn('code',$legal_entity_code)->get();
            }else{
                $legal_entities = LegalEntity::all();
            }

            foreach($legal_entities as $legal_entity){

                $printout_logo = $this->argument('logo') ? $this->argument('logo') : $this->getLogo($legal_entity);

                $legal_entity->update(['printout_logo' => $printout_logo]);
                $updatedCount++;
            }
        });

        $this->info("Total updated legal entities: {$updatedCount}\n");
    }

    public function getLogo($legal_entity)
    {
        if (in_array(strtoupper($legal_entity->code), ['P', 'W', 'C', 'K', 'B', 'G'])) {
            return 'SDP';
        } else if ($legal_entity->is_png ?? false) {
            return 'PNG';
        } else {
            return 'SDO';
        }
    }
}
