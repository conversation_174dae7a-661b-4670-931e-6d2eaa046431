<?php

namespace App\Console\Commands\Coru;

use App\Helpers\ConfigHelper;
use App\Models\Config;
use App\Services\Coru\ContractService;
use Illuminate\Console\Command;

class ExpiringLtaContracts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'coru:expiring-lta-contracts { --trader-email= : Specific trader to send to }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a list of LTA contracts that are about to expire to notify trader';

    /** @var \App\Services\Coru\ContractService */
    private $contractService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(ContractService $contractService)
    {
        parent::__construct();
        $this->contractService = $contractService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $filter = [];

        $email = $this->option('trader-email');

        if ($email) {
            $filter['trader_email'] = $email;
        }

        // Get config for expiring days
        $expiring_days_config = (int) ConfigHelper::get(Config::CONTROLS_LTA_EXPIRY_ALERT_DAYS_BEFORE);

        $this->info('Send email notification to traders with LTA expiring within ' . $expiring_days_config . ' days');
        $this->line('####### START #########');

        $results = $this->contractService->runExpiringLtaContractsEmail($filter);

        if (count($results) == 0) {
            $this->info('no contracts to send');
            exit(0);
        }

        foreach ($results as $i => $row) {
            $index = $i + 1;
            $this->info("[$index] Email : {$row['email']} | Total Expiring Contracts {$row['contracts_count']}");
        }

        $this->line('####### END #########');
        exit(0);
    }
}
