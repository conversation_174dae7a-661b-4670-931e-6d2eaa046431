<?php

namespace App\Console\Commands\Coru;

use App\Services\Coru\ContractService;
use Illuminate\Console\Command;

class UnconfirmedContracts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'coru:unconfirmed-contracts { --email= : only run for the said user email }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a list of contracts that are posted but not confirmed within a single day to notify user';

    /** @var \App\Services\Coru\ContractService */
    private $contractService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(ContractService $contractService)
    {
        parent::__construct();
        $this->contractService = $contractService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $filter = [];

        $email = $this->option('email');

        if ($email) {
            $filter['email'] = $email;
        }

        $this->info('Send email notification to users with unconfirmed contracts within a day after posted');
        $this->line('####### START #########');

        $results = $this->contractService->runUnconfirmedContractsEmail($filter);

        if (count($results) == 0) {
            $this->info('no contracts to send');
            exit(0);
        }

        foreach ($results as $i => $row) {
            $index = $i + 1;
            $this->info("[$index] Email : {$row['email']} | Total Unconfirmed Contracts {$row['contracts_count']}");
        }

        $this->line('####### END #########');
        exit(0);
    }

}
