<?php

namespace App\Console\Commands\Coru;

use App\Models\ContractStatus;
use App\Services\Coru\ContractService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class OutstandingContracts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'coru:outstanding-contracts { --trader-email= : only run for the said trader email }
                                                       { --skip-email : skip sending email out, just generate report}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a list of contracts that are outstanding, then email notify trader';

    /** @var \App\Services\Coru\ContractService */
    private $contractService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(ContractService $contractService)
    {
        parent::__construct();
        $this->contractService = $contractService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $filter = [];

        $trader_email = $this->option('trader-email');
        $skip_email = $this->option('skip-email');

        if ($trader_email) {
            $filter['trader_email'] = $trader_email;
        }

        $filter['shipment_month'] = Carbon::now()->startOfMonth();
        $filter['status'] = ContractStatus::TYPE_CONFIRMED;

        $this->info('Send email notification to traders with outstanding contracts');
        $this->line('####### START #########');

        $results = $this->contractService->runCoruOutstandingContract($filter, $skip_email);

        if ($skip_email) {
            $this->line('####### SKIPPING EMAIL - DRY RUN #########');
        }

        if (empty($results)) {
            $this->warn('####### END - No email to send #######');
            exit(0);
        }

        foreach ($results['email_count'] as $i => $row) {
            $index = $i + 1;
            $this->info("[$index] Email : {$row['email']} | Total Outstanding Contracts {$row['contracts_count']}");
        }

        $this->line('####### END #########');
        exit(0);
    }
}
