<?php

namespace App\Console\Commands\Coru;

use App\Helpers\NumberHelper;
use App\Interfaces\Contractable;
use App\Models\ContractStatus;
use App\Models\ContractType;
use App\Models\PhysicalContract;
use App\Models\PhysicalLoad;
use App\Models\RubberContract;
use App\Models\User;
use App\Repositories\PhysicalLoadRepository;
use App\Services\EmailService;
use App\Services\PhysicalContractService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use PharIo\Manifest\Email;

class CheckContractWithLoadsNotInvoiced extends Command
{
    private $threshold_hours = 24;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'coru:loads-not-invoiced {x_hours_before}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for contracts that have loads with status Confirmed & FulFilled that has not been invoiced pass X hours (Physical & Rubber only)';

    /** @var \App\Services\EmailService */
    protected $emailService;

    /** @var \App\Repositories\PhysicalLoadRepository  */
    private $physicalLoadRepository;

    private $x_hours_before = 24;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle(PhysicalLoadRepository $physicalLoadRepository)
    {

        $this->physicalLoadRepository = $physicalLoadRepository;

        $this->x_hours_before = $this->argument('x_hours_before');

        $this->info('Running email notification to traders with unbilled loads ' . $this->x_hours_before . ' hours before now');

        $this->sendContractsWithLoadsNotInvoicedEmail();

        $this->info("Finished");
    }

    public function sendContractsWithLoadsNotInvoicedEmail()
    {

        foreach (User::where('is_active', true)->where('is_trader', true)->cursor() as $trader) {

            $this->line('Processing Physical contract loads for Trader: ' . $trader->name . ' (id: ' . $trader->id . ')');

            $startTime = $this->logMemory();

            $loads = $this->getUnbilledLoadsForContractsBelongsTo($trader);

            $this->intervalLogMemory($startTime);

            $this->line('Retrieved total number of loads: ' . $loads->count());

            if ( count($loads) == 0 ){
                $this->line("No outstanding loads to be billed.");
                continue;
            }

            $this->line('Composing email for trader');

            $view = View::make('mail._contract_with_loads_not_billed_email', [
                'loads' => $loads,
                'trader' => $trader,
                'threshold' => $this->threshold_hours
            ]);

            $this->info('Sending email to trader at: ' . $trader->email);

            Log::info('Sending bill load notification to ' . $trader->email);

            $this->emailService->sendEmail(
                [$trader->email], //might need to change
                "Physical Contract Loads unbilled after {$this->threshold_hours} hours for {$trader->name}",
                $view->render()
            );

            $this->intervalLogMemory($startTime);
        }

    }

    private function getUnbilledLoadsForContractsBelongsTo($trader)
    {
        return PhysicalLoad::where('is_void', false)
            ->whereDoesntHave('activeInvoiceLineItems')
            ->where('fulfillment_type', PhysicalLoad::TYPE_DISPATCH)        // only take dispatch (sales) because usually users dont bill purchase contracts.
            ->whereRaw('GETDATE() > DATEADD(HOUR, ' . $this->threshold_hours . ', reference_datetime)')
            ->where('reference_datetime', '>=', Carbon::now('UTC')->subMonths(3)->toDateTimeString())      // restrict to only 3 months max so it doesn't show all the old data
            ->where('contractable_type', PhysicalContract::class)
            ->whereIn('contractable_id', function($query) use ($trader){
                $query->select('id')
                    ->from('contracts_physical')
                    ->where('status', ContractStatus::TYPE_CONFIRMED)
                    ->where('trader_user_id', $trader->id);
            })
            ->whereDoesntHave('loadable')
            ->with('contractable')
            ->get();

    }


    private function logMemory()
    {
        if (config('app.env') == 'development') {
            // check memory iteration usage
            $this->line('Memory use: ' . NumberHelper::convertByteSize(memory_get_usage(true)));
            return $start = microtime(true);
        }
    }

    private function intervalLogMemory($start)
    {
        if (config('app.env') == 'development') {
            // check memory iteration usage
            $this->line('Memory use: ' . NumberHelper::convertByteSize(memory_get_usage(true)));
            $this->line(microtime(true) - $start . ' seconds passed since job starts');
        }
    }
}
