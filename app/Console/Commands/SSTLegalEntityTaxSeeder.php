<?php

namespace App\Console\Commands;

use App\Models\LegalEntity;
use App\Models\LegalEntityTax;
use App\Models\Tax;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SSTLegalEntityTaxSeeder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sst:legal-entity-tax {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seeder to update master_tax_mapping for SST phase 2';

    protected $isActual;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->isActual = false;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual');

        $this->info('Seeding legal entity tax mapping');
        $file = storage_path('csv-source/sst_tax_mapping.csv');

        if(file_exists($file)){
            $row = 0;

            $tax_mapping_csv = [];

            if (($handle = fopen($file, "r")) !== false) {
                while (($data = fgetcsv($handle, null, ',')) !== false) {
                    $row ++;
                    // skip header row
                    if($row == 1){
                        continue;
                    }
                    // $data['0'] = legal entity code
                    // $data['1'] = tax code

                    $legal_entity_code = trim($data[0]);
                    $tax_code = trim($data[1]);

                    $tax_mapping_csv[$legal_entity_code][] = $tax_code;
                }
                fclose($handle);
            }

            DB::transaction(function() use ($tax_mapping_csv) {
                foreach ($tax_mapping_csv as $legal_entity_code_csv => $tax_codes_csv) {
                    $legal_entity = LegalEntity::where('code', $legal_entity_code_csv)->first();

                    if(empty($legal_entity)){
                        $this->error("Legal Entity {$legal_entity_code_csv} not found.");
                        continue;
                    }

                    $taxes = Tax::whereIn('code', $tax_codes_csv)->get()->keyBy('code');

                    $this->info("Total tax to be inserted (Legal Entity {$legal_entity_code_csv}): " . count($tax_codes_csv));

                    $inserted_taxes = collect([]);
                    foreach ($tax_codes_csv as $tax_code_csv) {
                        if (!isset($taxes[$tax_code_csv])) {
                            $this->error("Tax {$tax_code_csv} not found. (Legal Entity {$legal_entity_code_csv})");
                            continue;
                        }

                        if ( $this->isActual ) {
                            $inserted_taxes->push(LegalEntityTax::firstOrCreate([
                                'legal_entity_id' => $legal_entity->id,
                                'tax_id' => $taxes[$tax_code_csv]->id,
                            ]));
                        }
                    }

                    $this->info("Assigned ".count($inserted_taxes)." taxes to {$legal_entity_code_csv}");
                }
            });
        }else{
            $this->error('File not found');
        }
    }
}
