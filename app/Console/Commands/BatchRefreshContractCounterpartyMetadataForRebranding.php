<?php

namespace App\Console\Commands;

use App\Models\ContractStatus;
use App\Models\Counterparty;
use App\Models\PhysicalContract;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BatchRefreshContractCounterpartyMetadataForRebranding extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'metadata:batch-refresh-counterparty-rebranding {--counterparty-id=} {--contract-number=} {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Batch refresh physical contract's counterparty metadata for rebranding exercise";


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Auth::loginUsingId(1);

        $contract_number = $this->option('contract-number');
        $counterparty = Counterparty::find($this->option('counterparty-id'));

        if ( $counterparty === null ) {
            throw new \Exception('Counterparty not found.');
        }

        $this->info('Running Sime Darby Rebranding contract metadata patching script for counterparty ' . $counterparty->long_name);

        // only patch draft, posted, confirmed contracts for the counterparty
        $contracts = PhysicalContract::whereIn('status', [ContractStatus::TYPE_DRAFT, ContractStatus::TYPE_POSTED, ContractStatus::TYPE_CONFIRMED])
            ->where('counterparty_id', $counterparty->id)
            ->orderBy('id', 'DESC');

        if ( $contract_number !== null ) {
            $contracts->where('contract_number', $contract_number);
        }

        $contracts = $contracts->get();

        $this->info(count($contracts) . ' contracts to be patched');

        sleep(5);

        $updated_contract_count = 0;

        DB::transaction(function() use (&$updated_contract_count, $contracts, $counterparty){
            foreach ( $contracts as $contract ) {

                $this->info('Patching contract ' . $contract->contract_number);

                if ( $this->option('actual') ) {
                    $this->updateCounterpartyMetadata($contract, $counterparty);
                }

                $this->info('Contract updated.');

                $contract->save();
                $updated_contract_count++;
            }
        });

        $this->info("Successfully patched {$updated_contract_count} contracts");
    }

    private function updateCounterpartyMetadata(&$contract, $counterparty) {

        $metadata = json_decode($contract->metadata, true);

        $contract->counterparty_name = $counterparty->name;

        $metadata['counterparty']['name'] = $counterparty->name;
        $metadata['counterparty']['long_name'] = $counterparty->long_name;
        $metadata['counterparty']['address'] = $counterparty->address;
        $metadata['counterparty']['phone'] = $counterparty->phone;
        $metadata['counterparty']['website'] = $counterparty->website;
        $metadata['counterparty']['fax'] = $counterparty->fax;
        $metadata['counterparty']['company_registration_number'] = $counterparty->company_registration_number;
        $metadata['counterparty']['tax_registration_number'] = $counterparty->tax_registration_number;
        $metadata['counterparty']['tin_no'] = $counterparty->tin_no;

        $contract->metadata = json_encode($metadata);

    }

}
