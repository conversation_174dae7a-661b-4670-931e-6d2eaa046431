<?php

namespace App\Console\Commands;

use App\Models\PhysicalContract;
use App\Services\SplitContractService;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class BatchAsyncSplit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'batch:async {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Batch async split contracts';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $splitContractService = app(SplitContractService::class);

        $this->info('Running batch async split..');

        // get excel file
        $file = fopen(storage_path('batch_async_split.csv'), 'r');
        $list = [];
        $line_count = 0;
        $errors = [];

        while (($line = fgetcsv($file)) !== FALSE) {

            if ( $line_count == 0 ){
                $line_count++;
                continue;
            }

            if ( strlen($line[0]) == 0 ){
                $line_count++;
                continue;
            }

            $line_count++;

            $contract_number = trim($line[0]);



            // Validate that it's a real contract
            $contract = PhysicalContract::where('contract_number', $contract_number)->first();

            if ( $contract == null ){
                $errors[] = $contract_number . ' - Invalid contract number';
                continue;
            }

            try {
                $validate_contract = $splitContractService
                    ->setContract($contract)
                    ->validateAsyncSplit();
            } catch (\Exception $ex) {
                $errors[] = "{$contract->contract_number} - " . $ex->getMessage();
                continue;
            }

            if ( count($errors) > 0 ){
                continue;
            }

            $list[] = $contract;
        }

        fclose($file);

        if ( count($errors) > 0 ){
            throw new \Exception(join("\n", $errors));
        }

        if ( $this->option('actual') != null ) {
            Auth::loginUsingId(1);

            foreach ($list as $index => $contract) {
                try {
                    $this->info("Processing " . $contract->contract_number . " to async split ");

                    $contract_numbers = $splitContractService
                        ->setContract($contract)
                        ->asyncSplit();

                    $this->info("Completed async split for contract " . $contract->contract_number);

                    $list[$index]['child_contract_number'] = $contract_numbers[1];
                } catch (\Exception $ex) {
                    // catch error such as price index entries not found which is not validated by validateAsyncSplit
                    $this->error("Exception thrown when processing " . $contract->contract_number);
                    $list[$index]['error'] = $ex->getMessage();
                }
            }

            $this->info("DONE!");
            $this->writeToOutput($list);

        } else {
            $this->info("End dry run");
        }

    }

    public function writeToOutput($list) {

        $filename = storage_path('batch_async_split_output_' . Carbon::now()->timestamp . '.csv');
        $this->info("Writing output to file " . $filename);

        $fp = fopen($filename, 'w');

        fputcsv($fp, ['contract_number', 'child_contract_number', 'error']);

        foreach ($list as $item) {
            fputcsv($fp, [$item['contract_number'], $item['child_contract_number'] ?? '', $item['error'] ?? '']);
        }

        fclose($fp);

    }
}
