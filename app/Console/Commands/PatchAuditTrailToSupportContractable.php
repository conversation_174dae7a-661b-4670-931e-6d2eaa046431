<?php

namespace App\Console\Commands;

use App\Models\AuditTrail;
use App\Models\ContractType;
use Illuminate\Console\Command;

class PatchAuditTrailToSupportContractable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:support-contractable';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch AuditTrail old data where contract uses auditable to use contractable';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Starting..');

        try {

            $oldStructureAudits = AuditTrail::whereIn('auditable_type', array_keys(ContractType::MODEL_TO_TYPE))
                ->whereNotNull('auditable_id')
                ->whereNotNull('auditable_type')
                ->whereNull('contractable_type')
                ->whereNull('contractable_id')
                ->get();

            if ($oldStructureAudits->isNotEmpty()) {
                foreach ($oldStructureAudits as $audit) {
                    $audit->contractable_type = $audit->auditable_type;
                    $audit->contractable_id = $audit->auditable_id;
                    $audit->save();
                }
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->info('Patch Successful');
    }
}
