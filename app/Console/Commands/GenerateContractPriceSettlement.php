<?php

namespace App\Console\Commands;

use App\Helpers\CreditDebitNoteHelper;
use App\Models\BillingDocument;
use App\Models\ContractPriceSettlement;
use App\Models\ContractStatus;
use App\Models\PhysicalContract;
use App\Models\PricingType;
use App\Models\RubberContract;
use App\Services\ContractPriceFixingService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;

class GenerateContractPriceSettlement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:contract-price-settlement  { --provisional : Only run for provisional contracts }
                                                                { --mpob : Only run for MPOB contracts }
                                                                { --mrb : Only run for MRB contracts } ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check if settlement is required, and insert into settlement table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        if (!Auth::check()) {
            Auth::loginUsingId(1);
        }

        $all = !$this->option('provisional') && !$this->option('mpob') && !$this->option('mrb');

        $this->line('Begin generating entries that requires price fixing settlement');

        ContractPriceSettlement::truncate();

        if ($all || $this->option('provisional')) {
            $this->info('------- Provisional Pricing ---------');
            $this->generateProvisionalPricingEntries();
        }

        if ($all || $this->option('mpob')) {
            $this->info('------- MPOB Pricing ---------');
            $this->generateMPOBPricingEntries();
        }

        if ($all || $this->option('mrb')) {
            $this->info('------- MRB Pricing ---------');
            $this->generateMRBPricingEntries();
        }
        
        $this->line('Process End');
    }

    private function generateProvisionalPricingEntries()
    {
        // only those price fixings where is_pending is false we check
        $contractQuery = PhysicalContract::status([ContractStatus::TYPE_CONFIRMED, ContractStatus::TYPE_FULFILLED])
                    ->pricingType(PricingType::TYPE_PROVISIONAL)
                    ->where('contract_date', '>', Carbon::now()->subYears(1)->startOfYear()->format('Y-m-d'))
                    ->whereHas('priceFixings', function($query){
                        return $query->where('is_pending', false);
                    })
                    ->whereDoesntHave('billingDocuments', function($q) {
                        $q->where('sub_type', BillingDocument::SUB_TYPE_PRICE_FIXING)
                          ->where('status', '<>', BillingDocument::INVOICE_STATUS_VOIDED);
                    });

        $generated = 0;
        $not_generated = 0;

        foreach($contractQuery->cursor() as $contract) {

                // If no settlement is required, throw error
                if ( $this->canPriceSettle($contract) ) {
                    $this->line('Eligible Provisional contract: ' . $contract->contract_number);
                    $generated++;
                    ContractPriceFixingService::createContractInPriceFixingSettlement($contract);
                } else {
                    $not_generated++;
                }
            
        }

        $this->info('Total Provisional contract that can do price settlement: '. $generated);
        $this->info('Total Provisional contracts not eligible for price settlement: '. $not_generated);

    }

    private function generateMPOBPricingEntries()
    {
        // only those price fixings where is_pending is false we check
        $contractQuery = PhysicalContract::status([ContractStatus::TYPE_CONFIRMED, ContractStatus::TYPE_FULFILLED])
                    ->pricingType(PricingType::TYPE_MPOB)
                    ->where('contract_date', '>', Carbon::now()->subYears(1)->startOfYear()->format('Y-m-d'))
                    ->whereHas('priceFixings', function($query){
                        return $query->where('is_pending', false);
                    })
                    ->whereDoesntHave('billingDocuments', function($q) {
                        $q->where('sub_type', BillingDocument::SUB_TYPE_PRICE_FIXING)
                          ->where('status', '<>', BillingDocument::INVOICE_STATUS_VOIDED);
                    });

        $generated = 0;
        $not_generated = 0;

        foreach($contractQuery->cursor() as $contract) {

                if ( $this->canPriceSettle($contract) ) {
                    $this->line('Eligible MPOB contract: ' . $contract->contract_number);
                    $generated++;
                    ContractPriceFixingService::createContractInPriceFixingSettlement($contract);
                } else {
                    $not_generated++;
                }
            
        }

        $this->info('Total MPOB contracts that can do price settlement: '. $generated);
        $this->info('Total MPOB contracts not eligible for price settlement: '. $not_generated);

    }

    private function generateMRBPricingEntries()
    {
        // only those price fixings where is_pending is false we check
        $contractQuery = RubberContract::status([ContractStatus::TYPE_CONFIRMED, ContractStatus::TYPE_FULFILLED])
                    ->pricingType(PricingType::TYPE_MRB)
                    ->where('contract_date', '>', Carbon::now()->subYears(1)->startOfYear()->format('Y-m-d'))
                    ->whereHas('priceFixings', function($query){
                        return $query->where('is_pending', false);
                    })
                    ->whereDoesntHave('billingDocuments', function($q) {
                        $q->where('sub_type', BillingDocument::SUB_TYPE_PRICE_FIXING)
                          ->where('status', '<>', BillingDocument::INVOICE_STATUS_VOIDED);
                    });

        $generated = 0;
        $not_generated = 0;

        foreach($contractQuery->cursor() as $contract) {

                if ( $this->canPriceSettle($contract) ) {
                    $this->line('Eligible MRB contract: ' . $contract->contract_number);
                    $generated++;
                    ContractPriceFixingService::createContractInPriceFixingSettlement($contract);
                } else {
                    $not_generated++;
                }
            
        }

        $this->info('Total MRB contracts that can do price settlement: '. $generated);
        $this->info('Total MRB contracts not eligible for price settlement: '. $not_generated);

    }

    private function canPriceSettle($contract)
    {
        try{

            $price_adjustment_data = CreditDebitNoteHelper::getLandPriceAdjusmentViewData($contract);
            return CreditDebitNoteHelper::needPriceSettlement($price_adjustment_data);

        } catch(\Exception $e) {
            $this->warn('Error thrown during price settle checking | Contract: ' . $contract->contract_number . ' ' . $e->getMessage());
            return false;
        }
    }
}
