<?php

namespace App\Console\Commands;

use App\Models\ContractStatus;
use App\Models\Counterparty;
use App\Models\FuturesContract;
use App\Repositories\AllocationRepository;
use App\Services\FuturesContractService;
use App\Services\GeneralContractService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SdoRestructuringFuturesContractsMigration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'futures-contract:sdor-migrate {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Copy contract into new profit center/legal entity, and void current contract.';

    private $contractService;

    private $futuresContractService;

    private $allocationRepository;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->contractService = app(GeneralContractService::class);
        $this->futuresContractService = app()->make(FuturesContractService::class);
        $this->allocationRepository = app(AllocationRepository::class);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Running SDO Restructuring futures contract migration script..');

        // get excel file
        $file = fopen(storage_path('sdo_restructuring_futures_contract_migration.csv'), 'r');

        $list = [];

        $profit_centers = Counterparty::with('legalEntity')->where('is_internal', true)
            ->where('is_active', true)
            ->where('is_rubber_integration', false)
            ->where('is_minamas_integration', false)
            ->get()
            ->mapWithKeys(function($item) {
                return [$item->code => $item];
            });

        $valid_pc_codes = $profit_centers->keys()->unique()->values()->toArray();

        $line_count = 0;

        $errors = [];

        $traders = [];

        while (($line = fgetcsv($file)) !== FALSE) {

            if ( $line_count == 0 ){
                $line_count++;
                continue;
            }

            if ( strlen($line[0]) == 0 ){
                $line_count++;
                continue;
            }

            $line_count++;

            $data = [
                'contract_number' => trim($line[0]),
                'new_profit_center_code' => trim($line[1]),
                'new_counterparty_code' => trim($line[2]),
                'contract_model' => null
            ];

            if ( !empty($data['new_profit_center_code']) && !in_array($data['new_profit_center_code'], $valid_pc_codes) ){
                $errors[] = 'Invalid Profit Center code ' . $data['new_profit_center_code'] . ' for contract ' . $data['contract_number'];
            }

            // only able to use internal counterparty (profit center)
            if ( !empty($data['new_counterparty_code']) && !in_array($data['new_counterparty_code'], $valid_pc_codes) ){
                $errors[] = 'Invalid Counterparty code ' . $data['new_counterparty_code'] . ' for contract ' . $data['contract_number'];
            }

            if (empty($data['new_profit_center_code']) && empty($data['new_counterparty_code'])) {
                $errors[] = 'Both Profit Center and Counterparty code are empty for contract ' . $data['contract_number'];
            }

            // Validate that it's a real contract
            $contract = FuturesContract::with('trader')->where('contract_number', $data['contract_number'])
                ->where('status', ContractStatus::TYPE_CONFIRMED)
                ->first();

            if ( $contract == null ){
                $errors[] = 'Invalid Contract number ' . $data['contract_number'] . ' or contract status not confirmed.';
            }

            if ( count($errors) > 0 ){
                continue;
            }

            $data['contract_model'] = $contract;
            $data['new_profit_center_model'] = !empty($data['new_profit_center_code']) ? $profit_centers[$data['new_profit_center_code']] : null;
            $data['new_counterparty_model'] = !empty($data['new_counterparty_code']) ? $profit_centers[$data['new_counterparty_code']] : null;

            $this->info("Found {$data['contract_number']}, New PC - {$data['new_profit_center_code']}, New CP - {$data['new_counterparty_code']}");

            if ( !in_array($contract->trader->email, $traders) ){
                $traders[] = $contract->trader->email;
            }

            $list[] = $data;

        }

        $this->info("Ensure following traders have access to SDOC profit center before proceed.");
        foreach ( $traders as $trader ){
            $this->info("Traders found: " . $trader);
        }

        fclose($file);

        if ( count($errors) > 0 ){
            throw new \Exception(join("\n", $errors));
        }

        if ( $this->option('actual') != null ){

            // foreach line
            Auth::loginUsingId(1);

            foreach ( $list as $index => $l ){

                $this->info("Processing " . $l['contract_number'] . " move to profit center " . $l['new_profit_center_code']);

                // copy contract as draft first..
                DB::transaction(function() use ($l, $index, &$list){

                    try{

                        $errors = [];

                        $this->info("Copying contract..");

                        $contract_number = $this->futuresContractService->copyContract($l['contract_model'], 1, ContractStatus::TYPE_DRAFT)[1];

                        Log::info($l['contract_number'] . " => Contract copied into new contract " . $contract_number);
                        $this->info($l['contract_number'] . " => Contract copied into new contract " . $contract_number);

                        $new_contract = FuturesContract::where('contract_number', $contract_number)->first();

                        if (!empty($l['new_profit_center_model'])) {
                            $this->info("Updating contract profit center to {$l['new_profit_center_model']->name} and legal entity for " . $new_contract->contract_number);

                            $new_contract->profit_center_id = $l['new_profit_center_model']->id;
                            $new_contract->legal_entity_id = $l['new_profit_center_model']->legalEntity->id;
                            $new_contract->profit_center_name = $l['new_profit_center_model']->name;
                            $new_contract->legal_entity_name = $l['new_profit_center_model']->legalEntity->name;
                        }

                        if (!empty($l['new_counterparty_model'])) {
                            $this->info("Updating contract counterparty to {$l['new_counterparty_model']->name} for " . $new_contract->contract_number);

                            $new_contract->counterparty_id = $l['new_counterparty_model']->id;
                            $new_contract->counterparty_name = $l['new_counterparty_model']->name;
                        }

                        if (!empty($new_contract->remarks)) {
                            $new_contract->remarks = $new_contract->remarks . "\nThis is a novated contract from {$l['contract_number']}.";
                        } else {
                            $new_contract->remarks = "This is a novated contract from {$l['contract_number']}.";
                        }

                        // contract date remained
                        $new_contract->contract_date = $l['contract_model']->contract_date;

                        // Broker ref remained
                        $new_contract->broker_reference = $l['contract_model']->broker_reference;

                        $new_contract->save();

                        Log::info("Refreshing contract metadata for " . $new_contract->contract_number);
                        $this->info("Refreshing contract metadata for " . $new_contract->contract_number);

                        $this->futuresContractService->refreshMetadata($new_contract);

                        Log::info("Updating contract status to POSTED " . $new_contract->contract_number);
                        $this->info("Updating contract status to POSTED " . $new_contract->contract_number);

                        $this->futuresContractService->changeStatus($new_contract, ContractStatus::TYPE_POSTED, $errors);

                        if ( count($errors) > 0 ){
                            $this->error("Error changing contract status to posted");
                            Log::error($errors);
                            throw new \Exception("Error changing contract status to posted");
                        }

                        $new_contract->refresh();

                        // Update mapping
                        $list[$index]['new_contract_number'] = $new_contract->contract_number;

                        Log::info("Updating contract status to CONFIRMED " . $new_contract->contract_number);
                        $this->info("Updating contract status to CONFIRMED " . $new_contract->contract_number);

                        $this->futuresContractService->changeStatus($new_contract, ContractStatus::TYPE_CONFIRMED, $errors);

                        if ( count($errors) > 0 ){
                            $this->error("Error changing contract status to confirmed");
                            Log::error($errors);
                            throw new \Exception("Error changing contract status to confirmed");
                        }

                        // Void existing contract
                        Log::info("Voiding old contract " . $l['contract_number']);
                        $this->info("Voiding old contract " . $l['contract_number']);

                        if (!empty($l['contract_model']->remarks)) {
                            $l['contract_model']->remarks = $l['contract_model']->remarks . "\nThis contract is novated to {$new_contract->contract_number}.";
                        } else {
                            $l['contract_model']->remarks = "This contract is novated to {$new_contract->contract_number}.";
                        }

                        $this->futuresContractService->changeStatus($l['contract_model'], ContractStatus::TYPE_VOIDED, $errors, ['remarks' => 'SDO Restructuring Contract Migration']);

                        if ( count($errors) > 0 ){
                            $this->error("Error changing contract status to voided");
                            Log::error($errors);
                            throw new \Exception("Error changing contract status to voided");
                        }

                        Log::info("Completed for contract " . $new_contract->contract_number);
                        $this->info("Completed for contract " . $new_contract->contract_number);
                    }catch(\Exception $e){

                        $list = collect($list);
                        $this->writeToOutput($list);

                        throw $e;
                    }

                });

            }

            $list = collect($list);
            $this->writeToOutput($list);

            $this->info("DONE!");

        }else{
            $this->info("End dry run");
        }

    }

    public function writeToOutput($list) {

        $filename = storage_path('sdo_restructuring_futures_output_' . Carbon::now()->timestamp . '.csv');
        $this->info("Writing output to file " . $filename);

        $fp = fopen($filename, 'w');

        fputcsv($fp, ['contract_number', 'new_contract_number']);

        foreach ($list as $item) {
            fputcsv($fp, [$item['contract_number'], $item['new_contract_number'] ?? 'N/A']);
        }

        fclose($fp);

    }
}
