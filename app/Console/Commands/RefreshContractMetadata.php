<?php

namespace App\Console\Commands;

use App\Models\PhysicalContract;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class RefreshContractMetadata extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'metadata:refresh {--contract-number=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reloads contract metadata';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $contract_number = $this->option('contract-number');
        Auth::loginUsingId(1);

        if ( $contract_number == null ){
            $this->error('Contract number is required.');
            die();
        }

        $contract = PhysicalContract::with(['legalEntity'])->where('contract_number', $contract_number)->first();

        if ( $contract == null ){
            $this->error('Physical contract with that contract number is not found.');
            die();
        }

        $this->info('Patching metadata for contract..');

        $metadata = json_decode($contract->metadata, true);

        $this->info('Patching legal entity..');
        $this->updateLegalEntity($contract, $metadata);

        $contract->metadata = json_encode($metadata);
        $contract->save();

        $this->info('Contract updated.');

    }

    private function updateLegalEntity($contract, &$metadata) {

        $le = $contract->legalEntity;

        $metadata['legal_entity']['mpob_license_number'] = $le->mpob_license_number;
        $metadata['legal_entity']['company_registration_number'] = $le->company_registration_number;
        $metadata['legal_entity']['address'] = $le->address;
        $metadata['legal_entity']['billing_address'] = $le->billing_address;
        $metadata['legal_entity']['tax_registration_number'] = $le->tax_registration_number;
        $metadata['legal_entity']['phone_1'] = $le->phone_1;
        $metadata['legal_entity']['phone_2'] = $le->phone_2;
        $metadata['legal_entity']['fax_1'] = $le->fax_1;
        $metadata['legal_entity']['fax_2'] = $le->fax_2;

    }

}
