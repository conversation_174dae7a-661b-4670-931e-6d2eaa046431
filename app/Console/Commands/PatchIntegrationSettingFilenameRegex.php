<?php

namespace App\Console\Commands;

use App\Models\LegalEntity;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\IntegrationSettings;

class PatchIntegrationSettingFilenameRegex extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:filename-regex';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch Integration Setting Filename Regex for GTM downstream billing and contract';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $updatedCount = 0;

        $list = [
            IntegrationSettings::OMT_Contract => "/OMT_((CTMR)|(CTMJ)|(CTMA)|(CTMK))_Contract(.*)\.xml/",
            IntegrationSettings::GTM_DOWNSTREAM_BILLING => "/OMT_((CTMR)|(CTMJ)|(CTMA)|(CTMK))_Finance(.*)\.xml/",
        ];

        DB::transaction(function() use (&$updatedCount, $list){

            foreach($list as $trigger_point => $filename_regex){

                $integration_setting = IntegrationSettings::where('trigger_point', $trigger_point)->first();
                if(!$integration_setting){
                    $this->error("Integration setting for {$trigger_point} not found.");
                    continue;
                }

                $integration_setting->update([
                    'filename_regex' => $filename_regex
                ]);
                $updatedCount++;
            }
        });

        $this->info("Total updated integration setting: {$updatedCount}\n");
    }
}
