<?php

namespace App\Console\Commands;

use App\Models\ContractStatus;
use App\Models\PhysicalContract;
use App\Models\PriceIndex;
use App\Models\PriceIndexEntry;
use App\Models\PricingType;
use App\Models\RubberContract;
use App\Models\User;
use App\Services\ContractPriceFixingService;
use App\Services\PriceIndexEntryService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;

/**
 * Class GenerateContractPricing
 * @package App\Console\Commands
 */
class GenerateContractPricing extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:contract-pricing {--now= }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync contract pricing from price index';

    /** @var PriceIndexEntryService  */
    protected $priceIndexEntryService;
    /** @var ContractPriceFixingService  */
    protected $contractPriceFixingService;

    protected $now;

    /**
     * GenerateMPOBContractPricing constructor.
     * @param PriceIndexEntryService $priceIndexEntryService
     * @param ContractPriceFixingService $contractPriceFixingService
     */
    public function __construct(PriceIndexEntryService $priceIndexEntryService, ContractPriceFixingService $contractPriceFixingService)
    {
        parent::__construct();
        $this->priceIndexEntryService = $priceIndexEntryService;
        $this->contractPriceFixingService = $contractPriceFixingService;
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {
        // make sure first user is system default user
        $user = User::first();
        Auth::onceUsingId($user->id);

        if (!$user) {
            throw new \Exception('No user available for generating contract pricing');
        }

        $this->info('Login as user: ' . $user->email);

        if ($this->option('now')) {
            $this->now = Carbon::parse($this->option('now'), 'UTC');
        } else {
            $this->now = Carbon::now('UTC');
        }

        $this->processPendingBasePrice();
        $this->info('----------------');
        $this->processMPOBPhysicalContract();
        $this->info('----------------');
        $this->processMRBRubberContract();
    }

    /**
     * @throws \Exception
     */
    protected function processPendingBasePrice()
    {
        $queries = [
            'Physical Contract' => PhysicalContract::query()->pricingType(PricingType::TYPE_MPOB),
            'Rubber Contract' => RubberContract::query()->pricingType(PricingType::TYPE_MRB),
        ];
        foreach ($queries as $name => $query) {
            $contracts = $query->with('priceIndex')
                ->where(function ($q) {
                    return $q->whereNull('is_base_price_pending_update')->orWhere('is_base_price_pending_update', true);
                })
                ->where(function ($q) {
                    return $q->whereNull('fixed_price')->orWhere('fixed_price', 0);
                })
                ->status([ContractStatus::TYPE_CONFIRMED])
                ->get();

            $this->info($name . ' base price to be updated: ' . $contracts->count() . ' contracts');

            // get all price index entry
            $query = PriceIndexEntry::query();
            $query->where('indexable_type', PriceIndex::class);
            $query->whereIn('indexable_id', $contracts->pluck('price_index_id')->unique());
            $query->orderByDesc('delivery_end_date');

            // group by indexable_id and key by delivery period
            $price_index_entries_group = $query->get()->groupBy('indexable_id')->map(function ($price_index_entry_group) {
                return $price_index_entry_group->keyBy(function ($price_index_entry) {
                    return Carbon::parse($price_index_entry->delivery_start_date)->toDateTimeString() . '_' . Carbon::parse($price_index_entry->delivery_end_date)->toDateTimeString();
                });
            });

            $total_updated = 0;
            foreach ($contracts as $contract) {
                if (!$contract->priceIndex) {
                    continue;
                }
                if (!isset($price_index_entries_group[$contract->price_index_id])) {
                    continue;
                }
                $this->info(sprintf('%s processing', $contract->contract_number));

                $shipment_month = Carbon::parse($contract->shipment_month, $contract->trader->timezone);
                $price_index_entry_from = $shipment_month->copy()->subMonth()->startOfMonth()->tz('UTC');
                $price_index_entry_to = $shipment_month->copy()->subMonth()->endOfMonth()->tz('UTC');

                // get price index entries - same or before date
                $price_index_entries = $price_index_entries_group[$contract->price_index_id]->filter(function ($price_index_entry) use ($price_index_entry_from) {
                    return $price_index_entry_from->isAfter($price_index_entry->delivery_start_date) || $price_index_entry_from->isSameDay($price_index_entry->delivery_start_date);
                });

                // always get last available full month price
                $key = $price_index_entry_from . '_' . $price_index_entry_to;

                if (isset($price_index_entries[$key])) {
                    $price_index_entry = $price_index_entries[$key];
                    $is_base_price_pending_update = false;
                } else {
                    $price_index_entry = $price_index_entries->first();
                    $is_base_price_pending_update = true;
                }

                if (!$price_index_entry) {
                    $this->info(sprintf('%s Price Index has no price entry', $contract->priceIndex->code));
                    continue;
                }

                if ($price_index_entry['settle'] <= 0) {
                    $this->info(sprintf('%s Price Index has no price entry for %s - %s', $contract->priceIndex->code, $price_index_entry['delivery_start_date'], $price_index_entry['delivery_end_date']));
                    continue;
                }

                $contract->base_price = $price_index_entry->settle;

                if ( $contract->getClass() == PhysicalContract::class ){
                    $contract->base_price_with_tax = $price_index_entry->settle;
                    $contract->final_base_price = $price_index_entry->settle + $contract->premium - $contract->discount;
                    $contract->final_base_price_with_tax = $price_index_entry->settle + $contract->premium - $contract->discount;
                }

                $contract->is_base_price_pending_update = $is_base_price_pending_update;
                $contract->save();
                $total_updated += 1;
            }

            $this->info('Number of contracts updated with new base price: ' . $total_updated);
            $this->info('----------------');
        }
    }

    /**
     * Process MPOB physical contract
     */
    protected function processMPOBPhysicalContract()
    {
        $contracts = PhysicalContract::with(['priceIndex', 'priceIndex.entries'])
            ->with(['priceFixings' => function ($query) {
                return $query->isPending(true);
            }])
            ->status([ContractStatus::TYPE_CONFIRMED, ContractStatus::TYPE_FULFILLED])
            ->pricingStatus(PricingType::STATUS_UNPRICED)
            ->pricingType(PricingType::TYPE_MPOB)
            ->get();

        $this->info('MPOB - Physical contract total: ' . $contracts->count());
        foreach ($contracts as $contract) {
            if (!$contract->priceIndex) {
                continue;
            }
            if ($contract->priceIndex->entries->count() <= 0) {
                $this->info(sprintf('%s - %s Price Index has no price entry', $contract->getContractNumber(), $contract->priceIndex->code));
                continue;
            }

            $price_index_entries = $contract->priceIndex->entries->keyBy(function ($price_index_entry) {
                return Carbon::parse($price_index_entry->delivery_start_date)->toDateTimeString() . '_' . Carbon::parse($price_index_entry->delivery_end_date)->toDateTimeString();
            });

            try {
                $from_date = Carbon::parse($contract->shipment_month, $contract->trader->timezone)->startOfMonth()->tz('UTC');
                $to_date = Carbon::parse($contract->shipment_month, $contract->trader->timezone)->endOfMonth()->tz('UTC');
                $this->info(sprintf('%s processing', $contract->contract_number));

                // 2020-03-20: insert exact month price index entry only
                $key = $from_date . '_' . $to_date;
                $price_index_entry = $price_index_entries[$key] ?? null;

                if (!$price_index_entry) {
                    $this->warn("[WARN] " . sprintf('%s Price not available yet for %s', $contract->priceIndex->code, $contract->shipment_month));
                    // delete is any price fixings existed
                    $contract->priceFixings()->delete();
                    continue;
                }

                if ($price_index_entry['settle'] <= 0) {
                    $this->warn("[WARN] " . sprintf('%s Price Index has no price entry for %s - %s', $contract->priceIndex->code, $price_index_entry['delivery_start_date'], $price_index_entry['delivery_end_date']));
                    continue;
                }

                $errors = [];
                $input = [
                    'contract' => $contract,
                    'quantity' => $contract->unpriced_quantity,
                    'price_index_id' => $contract->price_index_id,
                    'price_index_start' => $price_index_entry['delivery_start_date'],
                    'price_index_end' => $price_index_entry['delivery_end_date'],
                    'fixed_price' => $price_index_entry['settle'],
                    'price_index_price' => $price_index_entry['settle'],
                    'is_system_generated' => true,
                    'is_pending' => true,
                    'pricing_date' => Carbon::now(),
                ];

                // if contract already have price fixing entry and still pending
                // update the existing price fixing entry
                if ($contract->priceFixings->count() >= 1) {
                    // technically only have 1 entry for pending
                    $input['contract_price_fixing'] = $contract->priceFixings->first();
                }

                $this->contractPriceFixingService->generateContractPriceFixing($input, $errors);

                if (!empty($errors)) {
                    throw new \Exception(implode(',', $errors));
                }

            } catch (\Exception $e) {
                $this->error($e->getMessage());
                continue;
            }
        }
    }

    /**
     * Process MRB physical contract
     */
    protected function processMRBRubberContract()
    {
        $contracts = RubberContract::with('priceIndex')
            ->with(['priceFixings' => function ($query) {
                return $query->isPending(true);
            }])
            ->status([ContractStatus::TYPE_CONFIRMED, ContractStatus::TYPE_FULFILLED])
            ->pricingStatus(PricingType::STATUS_UNPRICED)
            ->pricingType(PricingType::TYPE_MRB)
            ->get();

        $this->info('MRB - Rubber contract total: ' . $contracts->count());
        foreach ($contracts as $contract) {
            if (!$contract->priceIndex) {
                continue;
            }
            if ($contract->priceIndex->entries->count() <= 0) {
                $this->info(sprintf('%s - %s Price Index has no price entry', $contract->getContractNumber(), $contract->priceIndex->code));
                continue;
            }

            $price_index_entries = $contract->priceIndex->entries->keyBy(function ($price_index_entry) {
                return Carbon::parse($price_index_entry->delivery_start_date)->toDateTimeString() . '_' . Carbon::parse($price_index_entry->delivery_end_date)->toDateTimeString();
            });

            try {
                // MRB is always use the last month before shipment month
                $from_date = Carbon::parse($contract->shipment_month, $contract->trader->timezone)->subMonths(1)->startOfMonth()->tz('UTC');
                $to_date = Carbon::parse($contract->shipment_month, $contract->trader->timezone)->subMonths(1)->endOfMonth()->tz('UTC');

                // 2020-03-20: get exact month price index entry only
                $key = $from_date . '_' . $to_date;
                $price_index_entry = $price_index_entries[$key] ?? null;
                $this->info(sprintf('%s processing', $contract->contract_number));

                if (!$price_index_entry) {
                    $this->info(sprintf('%s Price not available yet for %s', $contract->priceIndex->code, $from_date->tz($contract->trader->timezone)->format('M Y')));
                    // delete is any price fixings existed
                    $contract->priceFixings()->delete();
                    continue;
                }

                if ($price_index_entry['settle'] <= 0) {
                    $this->info(sprintf('%s Price Index has no price entry for %s - %s', $contract->priceIndex->code, $price_index_entry['delivery_start_date'], $price_index_entry['delivery_end_date']));
                    continue;
                }

                $errors = [];
                $input = [
                    'contract' => $contract,
                    'quantity' => $contract->unpriced_quantity,
                    'price_index_id' => $contract->price_index_id,
                    'price_index_start' => $price_index_entry['delivery_start_date'],
                    'price_index_end' => $price_index_entry['delivery_end_date'],
                    'fixed_price' => $price_index_entry['settle'],
                    'price_index_price' => $price_index_entry['settle'],
                    'is_system_generated' => true,
                    'is_pending' => true,
                    'pricing_date' => Carbon::now(),
                ];

                // if contract already have price fixing entry and still pending
                // update the existing price fixing entry
                if ($contract->priceFixings->count() >= 1) {
                    // technically only have 1 entry for pending
                    $input['contract_price_fixing'] = $contract->priceFixings->first();
                }

                $this->contractPriceFixingService->generateContractPriceFixing($input, $errors);

                if (!empty($errors)) {
                    throw new \Exception(implode(',', $errors));
                }

            } catch (\Exception $e) {
                $this->error($e->getMessage());
                continue;
            }
        }
    }
}
