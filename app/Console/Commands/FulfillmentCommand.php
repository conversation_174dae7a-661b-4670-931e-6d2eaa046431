<?php

namespace App\Console\Commands;

use App\Models\ContractType;
use App\Services\FulfillmentService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class FulfillmentCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fulfillment:run { --contract-number= : Contract Number to fulfill }
                                            { --physical : Only run for Physical Contracts }
                                            { --rubber : Only run for Rubber Contracts }
                                            { --stock-transfer : Only run for Stock Tranfer Contract }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check condition of contracts and fulfill the contract accordingly';

    /**
     *
     * @var \App\Services\FulfillmentService
     */
    private $fulfillmentService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(FulfillmentService $fulfillmentService)
    {
        parent::__construct();
        $this->fulfillmentService = $fulfillmentService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        // * Console requires login, else somewhere check user will fail
        Auth::loginUsingId(1);

        $contractNumber = $this->option('contract-number');
        $physicalOnly = $this->option('physical');
        $rubberOnly = $this->option('rubber');
        $stockTransferOnly = $this->option('stock-transfer');

        if ($physicalOnly && $rubberOnly) {
            $this->error('Flag for --physical and --rubber can only be selected EITHER one');
        }

        $onlyType = null;

        if ($physicalOnly) {
            $onlyType = ContractType::TYPE_PHYSICAL;
        } elseif ($rubberOnly) {
            $onlyType = ContractType::TYPE_PHYSICAL_RUBBER;
        } elseif ($stockTransferOnly) {
            $onlyType = ContractType::TYPE_STOCK_TRANSFER;
        }

        $this->info("Running auto fulfill contracts");

        $statuses = $this->fulfillmentService->fulfillContracts($contractNumber, $onlyType);

        $this->info("Completed!");

        if (!empty($statuses['success'])) {
            foreach ($statuses['success'] as $success_contract) {
                $this->info('Contract ' . $success_contract['contract_number'] . ' has been auto-fulfilled');
                Log::info('Contract ' . $success_contract['contract_number'] . ' has been auto-fulfilled');
            }
            $this->line('----------');
        }

        if (!empty($statuses['error'])) {
            foreach ($statuses['error'] as $error_contract) {
                $this->error('Contract of ' . $error_contract['contract_number'] . ' has failed to update with error: ' . $error_contract['message']);
            }
            $this->line('----------');
        }

    }
}
