<?php

namespace App\Console\Commands;

use App\Models\BillingDocument;
use App\Models\CertificateTradeContract;
use App\Models\ContractExternal;
use App\Models\ContractNumber;
use App\Models\ContractStatus;
use App\Models\ContractString;
use App\Models\ContractType;
use App\Models\Counterparty;
use App\Models\DeliveryOrder;
use App\Models\FuturesContract;
use App\Models\LegalEntity;
use App\Models\LongTermAgreementContract;
use App\Models\MonthlyContract;
use App\Models\OptionsContract;
use App\Models\PhysicalContract;
use App\Models\RubberContract;
use App\Models\StockTransferContract;
use App\Models\StockTransferTicketHeader;
use App\Models\VesselNomination;
use App\Services\FileManagementService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CalculateMasterContractNumber extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:running-number';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate and recalculate running number for all contracts and billing documents';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
//        $this->processPhysicalContract();
        $this->processPngPhysicalContract();

        /*$this->processPhysicalContract();
        $this->processMinamasDownstreamPhysicalContract();
        $this->processMinamasUpstreamPhysicalContract();
        $this->processFuturesContract();
        $this->processFfbLta();
        $this->processFfbMonthly();
        $this->processStockTransferHeader();
        $this->processStockTransfer();
        $this->processRubberContract();
        $this->processStrings();
        $this->processVesselNomination();
        $this->processDeliveryOrders();
        $this->processStringExternalContract();
        $this->processBillingDocuments();
        $this->processBillingDocumentsRubber();


        // Process all drafts
        $this->processDrafts();*/

        // Cert trade no need contract number


    }

    private function processDrafts() {

        $this->info("Processing Drafts..");

        $all_drafts = collect();

        $all_drafts = $all_drafts->merge(PhysicalContract::select(DB::raw('contract_number'))
            ->where('status', ContractStatus::TYPE_DRAFT)
            ->orderBy('id', 'DESC')
            ->get());

        $all_drafts = $all_drafts->merge(RubberContract::select(DB::raw('contract_number'))
            ->where('status', ContractStatus::TYPE_DRAFT)
            ->orderBy('id', 'DESC')
            ->get());

        $all_drafts = $all_drafts->merge(FuturesContract::select(DB::raw('contract_number'))
            ->where('status', ContractStatus::TYPE_DRAFT)
            ->orderBy('id', 'DESC')
            ->get());

        $all_drafts = $all_drafts->merge(StockTransferTicketHeader::select(DB::raw('reference_no AS contract_number'))
            ->where('status', ContractStatus::TYPE_DRAFT)
            ->orderBy('id', 'DESC')
            ->get());

        $all_drafts = $all_drafts->merge(LongTermAgreementContract::select(DB::raw('contract_number'))
            ->where('status', ContractStatus::TYPE_DRAFT)
            ->orderBy('id', 'DESC')
            ->get());

        $all_drafts = $all_drafts->merge(ContractString::select(DB::raw('code AS contract_number'))
            ->where('status', ContractStatus::TYPE_DRAFT)
            ->orderBy('id', 'DESC')
            ->get());

        $highest_number = 1;

        foreach ( $all_drafts as $contract ){

            if ( substr($contract->contract_number, 0, 5) != 'DRAFT' ){
                continue;
            }

            $running_number = $this->getBillingDocumentRunningNumber($contract->contract_number);

            if ( ($running_number + 1) > $highest_number ){
                $highest_number = ($running_number + 1);
            }

        }

        $inserts = collect();

        // Insert into master_contract_numbers
        $inserts->push([
            'contract_type_id' => null,
            'profit_center_id' => null,
            'shipment_month' => null,
            'shipment_year' => null,
            'product_id' => null,
            'legal_entity_id' => null,
            'is_draft' => 1,
            'prefix' => 'DRAFT-',
            'next_number' => $highest_number,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        $this->info("Inserting into master_contract_numbers table");
        ContractNumber::where('is_draft', 1)->delete();
        ContractNumber::insert($inserts->toArray());

        $this->info("Completed.");

    }

    private function processStringExternalContract() {

        $this->info("Processing String External Contract..");

        $contract_external = ContractExternal::where('contract_number', 'LIKE', 'EXT/%')->orderBy('id', 'DESC')->first();

        $contract_type = ContractType::where('code', 'string-external')->first();

        if ( $contract_external != null ){

            $running_number = (int) substr($contract_external->contract_number, 4);

            $this->info("Inserting into master_contract_numbers table");

            DB::transaction(function() use ($contract_type, $running_number){

                ContractNumber::where('contract_type_id', $contract_type->id)->delete();

                ContractNumber::insert([
                    'contract_type_id' => $contract_type->id,
                    'profit_center_id' => null,
                    'legal_entity_id' => null,
                    'shipment_month' => null,
                    'shipment_year' => null,
                    'product_id' => null,
                    'is_draft' => 0,
                    'prefix' => null,
                    'next_number' => ($running_number + 1),
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);

            });

            $this->info("Completed.");

        }

    }

    private function processDeliveryOrders() {

        $this->info("Processing Delivery Order..");
        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', 'delivery-order')->first();

        DeliveryOrder::select(DB::raw('reference_number, contractable_type, contractable_id'))
            ->with('contractable')
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getDeliveryOrderRunningNumber($contract->reference_number);

                    $legal_entity_id = $contract->contractable->legal_entity_id;
                    $product_id = $contract->contractable->product_id;

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$legal_entity_id]) ){
                        $summary[$contract_type->id][$legal_entity_id] = [];
                    }

                    if ( !isset($summary[$contract_type->id][$legal_entity_id][$product_id] ) ){
                        $summary[$contract_type->id][$legal_entity_id][$product_id] = ($running_number + 1);
                    }else{

                        if ( ($running_number + 1) > $summary[$contract_type->id][$legal_entity_id][$product_id] ){
                            $summary[$contract_type->id][$legal_entity_id][$product_id] = ($running_number + 1);
                        }
                    }

                }

            });

        Log::info('== Delivery Order ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $legal_entity_ids ){
            foreach ( $legal_entity_ids as $legal_entity_id => $product_ids ){
                foreach ( $product_ids as $product_id => $running_number ){

                    $inserts->push([
                        'contract_type_id' => $contract_type_id,
                        'profit_center_id' => null,
                        'legal_entity_id' => $legal_entity_id,
                        'shipment_month' => null,
                        'shipment_year' => null,
                        'product_id' => $product_id,
                        'is_draft' => 0,
                        'prefix' => null,
                        'next_number' => $running_number,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]);
                }


            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts){

            ContractNumber::where('contract_type_id', $contract_type->id)->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");


    }

    private function processBillingDocuments() {

        $this->info("Processing Billing Documents..");

        $minamas_upstream_legal_entities = [15,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41];
        $minamas_downstream_legal_entities = [16,17];

        $summary = [];
        $minamas_summary = [];

        foreach ( array_keys(BillingDocument::INVOICE_NAME_MAPPING) as $doctype ){

            $this->info("Processing " . $doctype);

            // Get physical contract type
            $contract_type = ContractType::where('code', $doctype)->first();

            if ( $contract_type == null ){
                continue;
            }

            $overwrite_contract_type_minamas = $contract_type;
            $overwrite_contract_type_gtm = $contract_type;

            // Proforma invoice and Advance invoice use invoice running number
            if ( $contract_type->code == BillingDocument::TYPE_PROFORMA_INVOICE
                || $contract_type->code == BillingDocument::TYPE_ADVANCE_INVOICE
            ){
                $overwrite_contract_type_minamas = ContractType::where('code', BillingDocument::TYPE_NORMAL_INVOICE)->first();
            }
            else if( $contract_type->code == BillingDocument::TYPE_INITIAL_INVOICE || $contract_type->code == BillingDocument::TYPE_CUSTOMS_INVOICE) {
                $overwrite_contract_type_gtm = ContractType::where('code', BillingDocument::TYPE_NORMAL_INVOICE)->first();
            }
            else if ($contract_type->code == BillingDocument::TYPE_ADVANCE_CREDIT_NOTE) {
                $overwrite_contract_type_minamas = ContractType::where('code', BillingDocument::TYPE_CREDIT_NOTE)->first();
                $overwrite_contract_type_gtm = $overwrite_contract_type_minamas;
            }
            else if ($contract_type->code == BillingDocument::TYPE_ADVANCE_PAYMENT_VOUCHER) {
                $overwrite_contract_type_minamas = ContractType::where('code', BillingDocument::TYPE_PAYMENT_VOUCHER)->first();
                $overwrite_contract_type_gtm = $overwrite_contract_type_minamas;
            }
            else if ($contract_type->code == BillingDocument::TYPE_INITIAL_VOUCHER) {
                $overwrite_contract_type_gtm = ContractType::where('code', BillingDocument::TYPE_PAYMENT_VOUCHER)->first();
            }


            BillingDocument::select(DB::raw('reference_number'), 'contractable_type', 'contractable_id')
                ->where('type', $doctype)
                ->where('contractable_type', '!=', RubberContract::class)
                ->with('contractable')
                ->orderBy('id', 'DESC')
                ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary, &$minamas_summary, $minamas_upstream_legal_entities,
                    $minamas_downstream_legal_entities, $overwrite_contract_type_minamas, $overwrite_contract_type_gtm){

                    $this->info("Processing chunk..");

                    foreach ( $contracts as $contract ){

                        $legal_entity_id = isset($contract->contractable) ? $contract->contractable->legal_entity_id : 0;
                        $running_number = $this->getBillingDocumentRunningNumber($contract->reference_number);

                        // if is minmas upstream, need to check is_international
                        if ( in_array($legal_entity_id, $minamas_upstream_legal_entities)  ){

                            $is_international = substr($contract->reference_number, 0, 2) == 'E/' ? 1 : 0;

                            if ( !isset($minamas_summary[$overwrite_contract_type_minamas->id]) ){
                                $minamas_summary[$overwrite_contract_type_minamas->id] = [];
                            }

                            if ( !isset($minamas_summary[$overwrite_contract_type_minamas->id][$legal_entity_id]) ){
                                $minamas_summary[$overwrite_contract_type_minamas->id][$legal_entity_id] = [];
                            }

                            if ( !isset($minamas_summary[$overwrite_contract_type_minamas->id][$legal_entity_id][$is_international] ) ){
                                $minamas_summary[$overwrite_contract_type_minamas->id][$legal_entity_id][$is_international] = ($running_number + 1);
                            }else{

                                if ( ($running_number + 1) > $minamas_summary[$overwrite_contract_type_minamas->id][$legal_entity_id][$is_international] ){
                                    $minamas_summary[$overwrite_contract_type_minamas->id][$legal_entity_id][$is_international] = ($running_number + 1);
                                }
                            }


                        }else{

                            $is_internal = isset($contract->contractable) ? (int) $contract->contractable->is_internal : 0;

                            // all minamas downstream has no is_internal
                            if ( in_array($legal_entity_id, $minamas_downstream_legal_entities) ){
                                $is_internal = 0;

                                if ( !isset($summary[$overwrite_contract_type_minamas->id]) ){
                                    $summary[$overwrite_contract_type_minamas->id] = [];
                                }
                                if ( !isset($summary[$overwrite_contract_type_minamas->id][$legal_entity_id]) ){
                                    $summary[$overwrite_contract_type_minamas->id][$legal_entity_id] = [];
                                }

                                if ( !isset($summary[$overwrite_contract_type_minamas->id][$legal_entity_id][$is_internal]) ){
                                    $summary[$overwrite_contract_type_minamas->id][$legal_entity_id][$is_internal] = ($running_number + 1);
                                }
                                else{
                                    if ( ($running_number + 1) > $summary[$overwrite_contract_type_minamas->id][$legal_entity_id][$is_internal] ){
                                        $summary[$overwrite_contract_type_minamas->id][$legal_entity_id][$is_internal] = ($running_number + 1);
                                    }
                                }

                            }else{

                                //GTM
                                // only invoice got is_internal
                                if ( $overwrite_contract_type_gtm->id != 13 ){
                                    $is_internal = 0;
                                }

                                if ( !isset($summary[$overwrite_contract_type_gtm->id]) ){
                                    $summary[$overwrite_contract_type_gtm->id] = [];
                                }

                                if ( !isset($summary[$overwrite_contract_type_gtm->id][$legal_entity_id]) ){
                                    $summary[$overwrite_contract_type_gtm->id][$legal_entity_id] = [];
                                }

                                if ( !isset($summary[$overwrite_contract_type_gtm->id][$legal_entity_id][$is_internal]) ){
                                    $summary[$overwrite_contract_type_gtm->id][$legal_entity_id][$is_internal] = ($running_number + 1);
                                }
                                else{
                                    if ( ($running_number + 1) > $summary[$overwrite_contract_type_gtm->id][$legal_entity_id][$is_internal] ){
                                        $summary[$overwrite_contract_type_gtm->id][$legal_entity_id][$is_internal] = ($running_number + 1);
                                    }
                                }

                            }

                        }


                    }

                });

        }


        Log::info("== {$doctype} ==");
        Log::info(json_encode($summary));
        Log::info(json_encode($minamas_summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $legal_entity_ids ){
            foreach ( $legal_entity_ids as $legal_entity_id => $is_internals ){
                foreach ( $is_internals as $is_internal => $running_number ){

                    if ( $is_internal == 0 ){
                        $is_internal = null;
                    }

                    $inserts->push([
                        'contract_type_id' => $contract_type_id,
                        'profit_center_id' => null,
                        'legal_entity_id' => $legal_entity_id,
                        'shipment_month' => null,
                        'shipment_year' => null,
                        'product_id' => null,
                        'is_draft' => 0,
                        'prefix' => null,
                        'next_number' => $running_number,
                        'is_internal' => $is_internal,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]);
                }


            }
        }


        // Insert into master_contract_numbers (MINAMAS UPSTREAM)
        foreach ( $minamas_summary as $contract_type_id => $legal_entity_ids ){
            foreach ( $legal_entity_ids as $legal_entity_id => $is_internationals ){
                foreach ( $is_internationals as $is_international => $running_number ){

                    $inserts->push([
                        'contract_type_id' => $contract_type_id,
                        'profit_center_id' => null,
                        'legal_entity_id' => $legal_entity_id,
                        'shipment_month' => null,
                        'shipment_year' => null,
                        'product_id' => null,
                        'is_international' => $is_international,
                        'is_draft' => 0,
                        'prefix' => null,
                        'next_number' => $running_number,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]);
                }

            }
        }

        $this->info("Inserting into master_contract_numbers table");
        $contract_types = ContractType::whereIn('code', array_keys(BillingDocument::INVOICE_NAME_MAPPING))->get()->pluck('id');
        ContractNumber::whereIn('contract_type_id', $contract_types->toArray())->delete();

        DB::transaction(function() use ($inserts){

            foreach ( $inserts->chunk(150) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });


        $this->info("Completed.");

    }

    private function processBillingDocumentsRubber() {

        $this->info("Processing Billing Documents Rubber..");

        foreach ( array_keys(BillingDocument::INVOICE_NAME_MAPPING) as $doctype ){

            $summary = [];

            $this->info("Processing " . $doctype);

            // Get physical contract type
            $contract_type = ContractType::where('code', $doctype)->first();

            if ( $contract_type == null ){
                continue;
            }

            BillingDocument::select(DB::raw('reference_number'), 'contractable_type', 'contractable_id')
                ->where('type', $doctype)
                ->where('contractable_type', RubberContract::class)
                ->with('contractable')
                ->orderBy('id', 'DESC')
                ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                    $this->info("Processing chunk..");

                    foreach ( $contracts as $contract ){

                        $profit_center_id = isset($contract->contractable) ? $contract->contractable->profit_center_id : 0;
                        $running_number = $this->getBillingDocumentRunningNumber($contract->reference_number);

                        if ( !isset($summary[$contract_type->id]) ){
                            $summary[$contract_type->id] = [];
                        }

                        if ( !isset($summary[$contract_type->id][$profit_center_id]) ){
                            $summary[$contract_type->id][$profit_center_id] = ($running_number + 1);
                        }
                        else{
                            if ( ($running_number + 1) > $summary[$contract_type->id][$profit_center_id] ){
                                $summary[$contract_type->id][$profit_center_id] = ($running_number + 1);
                            }
                        }


                    }

                });

            Log::info("== {$doctype} ==");
            Log::info(json_encode($summary));

            $inserts = collect();

            $this->info("Preparing insert array..");

            // Insert into master_contract_numbers
            foreach ( $summary as $contract_type_id => $profit_center_ids ){
                foreach ( $profit_center_ids as $profit_center_id => $running_number ){

                    $inserts->push([
                        'contract_type_id' => $contract_type_id,
                        'profit_center_id' => $profit_center_id,
                        'legal_entity_id' => null,
                        'shipment_month' => null,
                        'shipment_year' => null,
                        'product_id' => null,
                        'is_draft' => 0,
                        'prefix' => null,
                        'next_number' => $running_number,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]);


                }
            }

            $this->info("Inserting into master_contract_numbers table");

            DB::transaction(function() use ($contract_type, $inserts){

                ContractNumber::where('contract_type_id', $contract_type->id)
                    ->where('profit_center_id', 3427)
                    ->delete();

                foreach ( $inserts->chunk(180) as $chunks ){

                    $this->info("Inserting chunk..");
                    ContractNumber::insert($chunks->toArray());

                }

            });


        }


        $this->info("Completed.");

    }


    private function processVesselNomination() {

        $this->info("Processing Vessel Nomination..");
        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', 'vessel-nomination')->first();

        VesselNomination::select(DB::raw('vessel_nomination_code'))
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->vessel_nomination_code);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = ($running_number + 1);
                    }
                    else{
                        if ( ($running_number + 1) > $summary[$contract_type->id] ){
                            $summary[$contract_type->id] = ($running_number + 1);
                        }
                    }

                }

            });

        Log::info('== Vessel Nomination ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $running_number ){
            $inserts->push([
                'contract_type_id' => $contract_type_id,
                'profit_center_id' => null,
                'legal_entity_id' => null,
                'shipment_month' => null,
                'shipment_year' => null,
                'product_id' => null,
                'is_draft' => 0,
                'prefix' => null,
                'next_number' => $running_number,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts){

            ContractNumber::where('contract_type_id', $contract_type->id)->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");

    }

    private function processStrings() {

        $this->info("Processing Strings..");
        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_STRING)->first();

        ContractString::select(DB::raw('code'))
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->code);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = ($running_number + 1);
                    }
                    else{
                        if ( ($running_number + 1) > $summary[$contract_type->id] ){
                            $summary[$contract_type->id] = ($running_number + 1);
                        }
                    }

                }

            });

        Log::info('== Strings ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $running_number ){
            $inserts->push([
                'contract_type_id' => $contract_type_id,
                'profit_center_id' => null,
                'legal_entity_id' => null,
                'shipment_month' => null,
                'shipment_year' => null,
                'product_id' => null,
                'is_draft' => 0,
                'prefix' => null,
                'next_number' => $running_number,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts){

            ContractNumber::where('contract_type_id', $contract_type->id)->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");

    }

    private function processRubberContract() {

        $this->info("Processing Rubber contracts..");
        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_PHYSICAL_RUBBER)->first();

        RubberContract::select(DB::raw('contract_number, profit_center_id'))
            ->where('status', '!=', ContractStatus::TYPE_DRAFT)
            ->where('contract_number', 'not like', 'DRAFT-%')
            ->where('created_at', '>=', '2020-10-01')
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->contract_number);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }

                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id]) ){
                        $summary[$contract_type->id][$contract->profit_center_id] = ($running_number + 1);
                    }
                    else{
                        if ( ($running_number + 1) > $summary[$contract_type->id][$contract->profit_center_id] ){
                            $summary[$contract_type->id][$contract->profit_center_id] = ($running_number + 1);
                        }
                    }

                }

            });

        Log::info('== Rubber Contract ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $array1 ){
            foreach ( $array1 as $profit_center_id => $running_number ){

                $inserts->push([
                    'contract_type_id' => $contract_type_id,
                    'profit_center_id' => $profit_center_id,
                    'legal_entity_id' => null,
                    'shipment_month' => null,
                    'shipment_year' => null,
                    'product_id' => null,
                    'is_draft' => 0,
                    'prefix' => null,
                    'next_number' => $running_number,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);
            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts){

            ContractNumber::where('contract_type_id', $contract_type->id)->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");

    }

    private function processStockTransfer() {

        $this->info("Processing Stock transfer contracts..");
        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_STOCK_TRANSFER)->first();

        StockTransferContract::select(DB::raw('contract_number, profit_center_id, product_id, MONTH(shipment_month) AS shipmonth, YEAR(shipment_month) AS shipyear'))
            ->where('status', '!=', ContractStatus::TYPE_DRAFT)
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->contract_number);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id]) ){
                        $summary[$contract_type->id][$contract->profit_center_id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth] = [];
                    }

                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] = ($running_number + 1);
                    }
                    else{
                        if ( ($running_number + 1) > $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] ){
                            $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] = ($running_number + 1);
                        }
                    }

                }

            });

        Log::info('== Stock transfer Contract ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $array1 ){
            foreach ( $array1 as $profit_center_id => $array2 ){
                foreach ( $array2 as $shipyear => $array3 ){
                    foreach ( $array3 as $shipmonth => $array4 ){
                        foreach ( $array4 as $product_id => $running_number ){

                            $inserts->push([
                                'contract_type_id' => $contract_type_id,
                                'profit_center_id' => $profit_center_id,
                                'legal_entity_id' => null,
                                'shipment_month' => $shipmonth,
                                'shipment_year' => $shipyear,
                                'product_id' => $product_id,
                                'is_draft' => 0,
                                'prefix' => null,
                                'next_number' => $running_number,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ]);

                        }
                    }
                }
            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts){

            ContractNumber::where('contract_type_id', $contract_type->id)->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");

    }

    private function processStockTransferHeader() {

        $this->info("Processing Stock Transfer Header..");
        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_STOCK_TRANSFER_TICKET_HEADER)->first();

        StockTransferTicketHeader::select(DB::raw('reference_no, product_id, MONTH(shipment_month) AS shipmonth, YEAR(shipment_month) AS shipyear'))
            ->where('status', '!=', ContractStatus::TYPE_DRAFT)
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->reference_no);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->shipyear]) ){
                        $summary[$contract_type->id][$contract->shipyear] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->shipyear][$contract->shipmonth]) ){
                        $summary[$contract_type->id][$contract->shipyear][$contract->shipmonth] = [];
                    }

                    if ( !isset($summary[$contract_type->id][$contract->shipyear][$contract->shipmonth][$contract->product_id]) ){
                        $summary[$contract_type->id][$contract->shipyear][$contract->shipmonth][$contract->product_id] = ($running_number + 1);
                    }
                    else{
                        if ( ($running_number + 1) > $summary[$contract_type->id][$contract->shipyear][$contract->shipmonth][$contract->product_id] ){
                            $summary[$contract_type->id][$contract->shipyear][$contract->shipmonth][$contract->product_id] = ($running_number + 1);
                        }
                    }

                }

            });

        Log::info('== Stock transfer header Contract ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $array2 ){
            foreach ( $array2 as $shipyear => $array3 ){
                foreach ( $array3 as $shipmonth => $array4 ){
                    foreach ( $array4 as $product_id => $running_number ){

                        $inserts->push([
                            'contract_type_id' => $contract_type_id,
                            'profit_center_id' => null,
                            'legal_entity_id' => null,
                            'shipment_month' => $shipmonth,
                            'shipment_year' => $shipyear,
                            'product_id' => $product_id,
                            'is_draft' => 0,
                            'prefix' => null,
                            'next_number' => $running_number,
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ]);

                    }
                }
            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts){

            ContractNumber::where('contract_type_id', $contract_type->id)->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");

    }

    private function processFfbMonthly() {

        $this->info("Processing FFB Monthly contracts..");
        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_FFB_MONTHLY)->first();

        MonthlyContract::select(DB::raw('contract_number, legal_entity_id, product_id, MONTH(shipment_month) AS shipmonth, YEAR(shipment_month) AS shipyear'))
            ->where('status', '!=', ContractStatus::TYPE_DRAFT)
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->contract_number);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->legal_entity_id]) ){
                        $summary[$contract_type->id][$contract->legal_entity_id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->legal_entity_id][$contract->shipyear]) ){
                        $summary[$contract_type->id][$contract->legal_entity_id][$contract->shipyear] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->legal_entity_id][$contract->shipyear][$contract->shipmonth]) ){
                        $summary[$contract_type->id][$contract->legal_entity_id][$contract->shipyear][$contract->shipmonth] = [];
                    }

                    if ( !isset($summary[$contract_type->id][$contract->legal_entity_id][$contract->shipyear][$contract->shipmonth][$contract->product_id]) ){
                        $summary[$contract_type->id][$contract->legal_entity_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] = ($running_number + 1);
                    }
                    else{
                        if ( ($running_number + 1) > $summary[$contract_type->id][$contract->legal_entity_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] ){
                            $summary[$contract_type->id][$contract->legal_entity_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] = ($running_number + 1);
                        }
                    }

                }

            });

        Log::info('== FFB Monthly Contract ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $array1 ){
            foreach ( $array1 as $legal_entity_id => $array2 ){
                foreach ( $array2 as $shipyear => $array3 ){
                    foreach ( $array3 as $shipmonth => $array4 ){
                        foreach ( $array4 as $product_id => $running_number ){

                            $inserts->push([
                                'contract_type_id' => $contract_type_id,
                                'profit_center_id' => null,
                                'legal_entity_id' => $legal_entity_id,
                                'shipment_month' => $shipmonth,
                                'shipment_year' => $shipyear,
                                'product_id' => $product_id,
                                'is_draft' => 0,
                                'prefix' => null,
                                'next_number' => $running_number,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ]);

                        }
                    }
                }
            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts){

            ContractNumber::where('contract_type_id', $contract_type->id)->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");

    }

    private function processFfbLta() {

        $this->info("Processing FFB LTA..");
        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_FFB_LTA)->first();

        LongTermAgreementContract::select(DB::raw('contract_number, product_id'))
            ->where('status', '!=', ContractStatus::TYPE_DRAFT)
            ->where('contract_number', 'not like', 'DRAFT-%')
            ->orderBy('id', 'DESC')
            ->where('contract_date', '>=', '2020-01-01')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->contract_number);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }

                    if ( !isset($summary[$contract_type->id][$contract->product_id]) ){
                        $summary[$contract_type->id][$contract->product_id] = ($running_number + 1);
                    }
                    else{
                        if ( ($running_number + 1) > $summary[$contract_type->id][$contract->product_id] ){
                            $summary[$contract_type->id][$contract->product_id] = ($running_number + 1);
                        }
                    }

                }

            });

        Log::info('== FFB LTA Contract ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $array1 ){
            foreach ( $array1 as $product_id => $running_number ){

                $inserts->push([
                    'contract_type_id' => $contract_type_id,
                    'profit_center_id' => null,
                    'legal_entity_id' => null,
                    'shipment_month' => null,
                    'shipment_year' => null,
                    'product_id' => $product_id,
                    'is_draft' => 0,
                    'prefix' => null,
                    'next_number' => $running_number,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);

            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts){

            ContractNumber::where('contract_type_id', $contract_type->id)->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");


    }

    private function processFuturesContract() {

        $this->info("Processing Futures contracts..");
        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_FUTURES_BMD)->first();

        FuturesContract::select(DB::raw('contract_number, profit_center_id, product_id, MONTH(shipment_month) AS shipmonth, YEAR(shipment_month) AS shipyear'))
            ->where('status', '!=', ContractStatus::TYPE_DRAFT)
            ->where('contract_number', 'not like', 'DRAFT-%')
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->contract_number);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id]) ){
                        $summary[$contract_type->id][$contract->profit_center_id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth] = [];
                    }

                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] = ($running_number + 1);
                    }
                    else{

                        if ( ($running_number + 1) > $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] ){
                            $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] = ($running_number + 1);
                        }

                    }

                }

            });

        Log::info('== Futures Contract ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $array1 ){
            foreach ( $array1 as $profit_center_id => $array2 ){
                foreach ( $array2 as $shipyear => $array3 ){
                    foreach ( $array3 as $shipmonth => $array4 ){
                        foreach ( $array4 as $product_id => $running_number ){

                            $inserts->push([
                                'contract_type_id' => $contract_type_id,
                                'profit_center_id' => $profit_center_id,
                                'legal_entity_id' => null,
                                'shipment_month' => $shipmonth,
                                'shipment_year' => $shipyear,
                                'product_id' => $product_id,
                                'is_draft' => 0,
                                'prefix' => null,
                                'next_number' => $running_number,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ]);

                        }
                    }
                }
            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts){

            ContractNumber::where('contract_type_id', $contract_type->id)->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");


    }

    private function processOptionsContract() {

        $this->info("Processing Options contracts..");
        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_OPTIONS)->first();

        OptionsContract::select(DB::raw('contract_number, profit_center_id, product_id, MONTH(shipment_month) AS shipmonth, YEAR(shipment_month) AS shipyear'))
            ->where('status', '!=', ContractStatus::TYPE_DRAFT)
            ->where('contract_number', 'not like', 'DRAFT-%')
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->contract_number);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id]) ){
                        $summary[$contract_type->id][$contract->profit_center_id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth] = [];
                    }

                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] = ($running_number + 1);
                    }
                    else{
                        if ( ($running_number + 1) > $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] ){
                            $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth][$contract->product_id] = ($running_number + 1);
                        }
                    }

                }

            });

        Log::info('== Options Contract ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $array1 ){
            foreach ( $array1 as $profit_center_id => $array2 ){
                foreach ( $array2 as $shipyear => $array3 ){
                    foreach ( $array3 as $shipmonth => $array4 ){
                        foreach ( $array4 as $product_id => $running_number ){

                            $inserts->push([
                                'contract_type_id' => $contract_type_id,
                                'profit_center_id' => $profit_center_id,
                                'legal_entity_id' => null,
                                'shipment_month' => $shipmonth,
                                'shipment_year' => $shipyear,
                                'product_id' => $product_id,
                                'is_draft' => 0,
                                'prefix' => null,
                                'next_number' => $running_number,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ]);

                        }
                    }
                }
            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts){

            ContractNumber::where('contract_type_id', $contract_type->id)->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");


    }

    private function processPhysicalContract() {

        $this->info("Processing physical contracts..");

        $gtm_legal_entities = [1,2,3,4,5,6,7,8,9,10,11,12];

        // Profit centers
        $profit_center_ids = Counterparty::whereIn('legal_entity_id', $gtm_legal_entities)
            ->where('is_internal', true)->get()->pluck('id')->unique()->values();

        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_PHYSICAL)->first();

        // Non minmas contract
        PhysicalContract::select(DB::raw('contract_number, profit_center_id, product_id, shipment_month, MONTH(shipment_month) AS shipmonth, YEAR(shipment_month) AS shipyear'))
            ->where('status', '!=', ContractStatus::TYPE_DRAFT)
            ->where('contract_number', 'not like', 'DRAFT-%')
            ->whereIn('legal_entity_id', $gtm_legal_entities)
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->contract_number);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id]) ){
                        $summary[$contract_type->id][$contract->profit_center_id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth] = ($running_number + 1);
                    }
                    else{
                        if ( ($running_number + 1) > $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth] ){
                            $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth] = ($running_number + 1);
                        }
                    }


                }

            });

        Log::info('== Physical Contract ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $array1 ){
            foreach ( $array1 as $profit_center_id => $array2 ){
                foreach ( $array2 as $shipyear => $array3 ){
                    foreach ( $array3 as $shipmonth => $running_number ){

                        $inserts->push([
                            'contract_type_id' => $contract_type_id,
                            'profit_center_id' => $profit_center_id,
                            'legal_entity_id' => null,
                            'shipment_month' => $shipmonth,
                            'shipment_year' => $shipyear,
                            'product_id' => null,
                            'is_draft' => 0,
                            'prefix' => null,
                            'next_number' => $running_number,
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ]);

                    }
                }
            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts, $profit_center_ids){

            ContractNumber::where('contract_type_id', $contract_type->id)
                ->where('is_draft', 0)
                ->whereIn('profit_center_id', $profit_center_ids)
                ->delete();

            foreach ( $inserts->chunk(150) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");


    }

    private function processPngPhysicalContract() {
        $this->info("Processing physical contracts..");

        $png_legal_entities = LegalEntity::where('is_png', true)->get()->pluck('id');

        // Profit centers
        $profit_center_ids = Counterparty::whereIn('legal_entity_id', $png_legal_entities)
            ->where('is_internal', true)->get()->pluck('id')->unique()->values();

        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_PHYSICAL)->first();

        // Non minmas contract
        PhysicalContract::select(DB::raw('contract_number, profit_center_id, product_id, shipment_month, MONTH(shipment_month) AS shipmonth, YEAR(shipment_month) AS shipyear'))
            ->where('status', '!=', ContractStatus::TYPE_DRAFT)
            ->where('contract_number', 'not like', 'DRAFT-%')
            ->whereIn('legal_entity_id', $png_legal_entities)
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPngPhysicalRunningNumber(get_class($contract), $contract->contract_number);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id]) ){
                        $summary[$contract_type->id][$contract->profit_center_id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth]) ){
                        $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth] = ($running_number + 1);
                    }
                    else{
                        if ( ($running_number + 1) > $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth] ){
                            $summary[$contract_type->id][$contract->profit_center_id][$contract->shipyear][$contract->shipmonth] = ($running_number + 1);
                        }
                    }


                }

            });

        Log::info('== Physical Contract ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $array1 ){
            foreach ( $array1 as $profit_center_id => $array2 ){
                foreach ( $array2 as $shipyear => $array3 ){
                    foreach ( $array3 as $shipmonth => $running_number ){

                        $inserts->push([
                            'contract_type_id' => $contract_type_id,
                            'profit_center_id' => $profit_center_id,
                            'legal_entity_id' => null,
                            'shipment_month' => $shipmonth,
                            'shipment_year' => $shipyear,
                            'product_id' => null,
                            'is_draft' => 0,
                            'prefix' => null,
                            'next_number' => $running_number,
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ]);

                    }
                }
            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts, $profit_center_ids){

            ContractNumber::where('contract_type_id', $contract_type->id)
                ->where('is_draft', 0)
                ->whereIn('profit_center_id', $profit_center_ids)
                ->delete();

            foreach ( $inserts->chunk(150) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");
    }

    private function processMinamasDownstreamPhysicalContract() {

        $this->info("Processing Minamas downstream physical contracts..");

        $minamas_downstream_legal_entities = [16,17];

        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_PHYSICAL)->first();

        // Non minmas contract
        PhysicalContract::select(DB::raw('contract_number, legal_entity_id'))
            ->where('status', '!=', ContractStatus::TYPE_DRAFT)
            ->where('contract_number', 'not like', 'DRAFT-%')
            ->whereIn('legal_entity_id', $minamas_downstream_legal_entities)
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->contract_number);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }

                    if ( !isset($summary[$contract_type->id][$contract->legal_entity_id]) ){
                        $summary[$contract_type->id][$contract->legal_entity_id] = ($running_number + 1);
                    }
                    else{
                        if ( ($running_number + 1) > $summary[$contract_type->id][$contract->legal_entity_id] ){
                            $summary[$contract_type->id][$contract->legal_entity_id] = ($running_number + 1);
                        }
                    }

                }

            });

        Log::info('== Physical Contract (Minamas Downstream) ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $array1 ){
            foreach ( $array1 as $legal_entity_id => $running_number ){
                $inserts->push([
                    'contract_type_id' => $contract_type_id,
                    'profit_center_id' => null,
                    'legal_entity_id' => $legal_entity_id,
                    'shipment_month' => null,
                    'shipment_year' => null,
                    'product_id' => null,
                    'is_draft' => 0,
                    'prefix' => null,
                    'next_number' => $running_number,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);
            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts){

            ContractNumber::where('contract_type_id', $contract_type->id)->whereIn('legal_entity_id', [16,17])->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");


    }

    private function processMinamasUpstreamPhysicalContract() {

        $this->info("Processing Minamas upstream physical contracts..");

        $minamas_upstream_legal_entities = [13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41];

        $summary = [];

        // Get physical contract type
        $contract_type = ContractType::where('code', ContractType::TYPE_PHYSICAL)->first();

        // Non minmas contract
        PhysicalContract::select(DB::raw('contract_number, legal_entity_id, product_id, is_international, transaction_type'))
            ->where('status', '!=', ContractStatus::TYPE_DRAFT)
            ->where('contract_number', 'not like', 'DRAFT-%')
            ->whereIn('legal_entity_id', $minamas_upstream_legal_entities)
            ->orderBy('id', 'DESC')
            ->chunk(10000, function($contracts) use (&$test, $contract_type, &$summary){

                $this->info("Processing chunk..");

                foreach ( $contracts as $contract ){

                    $running_number = $this->getPhysicalRunningNumber(get_class($contract), $contract->contract_number, true);

                    if ( !isset($summary[$contract_type->id]) ){
                        $summary[$contract_type->id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->legal_entity_id]) ){
                        $summary[$contract_type->id][$contract->legal_entity_id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->legal_entity_id][$contract->product_id]) ){
                        $summary[$contract_type->id][$contract->legal_entity_id][$contract->product_id] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->legal_entity_id][$contract->product_id][$contract->is_international]) ){
                        $summary[$contract_type->id][$contract->legal_entity_id][$contract->product_id][$contract->is_international] = [];
                    }
                    if ( !isset($summary[$contract_type->id][$contract->legal_entity_id][$contract->product_id][$contract->is_international][$contract->transaction_type]) ){
                        $summary[$contract_type->id][$contract->legal_entity_id][$contract->product_id][$contract->is_international][$contract->transaction_type] = ($running_number + 1);
                    }else{
                        if ( ($running_number + 1) > $summary[$contract_type->id][$contract->legal_entity_id][$contract->product_id][$contract->is_international][$contract->transaction_type] ){
                            $summary[$contract_type->id][$contract->legal_entity_id][$contract->product_id][$contract->is_international][$contract->transaction_type] = ($running_number + 1);
                        }
                    }


                }

            });

        Log::info('== Physical Contract (Minamas Upstream) ==');
        Log::info(json_encode($summary));

        $inserts = collect();

        $this->info("Preparing insert array..");

        // Insert into master_contract_numbers
        foreach ( $summary as $contract_type_id => $array1 ){
            foreach ( $array1 as $legal_entity_id => $array2 ){
                foreach ( $array2 as $product_id => $array3 ){
                    foreach ( $array3 as $is_international => $array4 ){
                        foreach ( $array4 as $transaction_type => $running_number ){

                            $inserts->push([
                                'contract_type_id' => $contract_type_id,
                                'profit_center_id' => null,
                                'legal_entity_id' => $legal_entity_id,
                                'shipment_month' => null,
                                'shipment_year' => null,
                                'product_id' => $product_id,
                                'is_draft' => 0,
                                'prefix' => null,
                                'is_international' => (int) $is_international,
                                'transaction_type' => $transaction_type,
                                'next_number' => $running_number,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ]);

                        }
                    }
                }
            }
        }

        $this->info("Inserting into master_contract_numbers table");

        DB::transaction(function() use ($contract_type, $inserts, $minamas_upstream_legal_entities){

            ContractNumber::where('contract_type_id', $contract_type->id)
                ->whereIn('legal_entity_id', $minamas_upstream_legal_entities)
                ->delete();

            foreach ( $inserts->chunk(180) as $chunks ){

                $this->info("Inserting chunk..");
                ContractNumber::insert($chunks->toArray());

            }

        });

        $this->info("Completed.");


    }

    private function getPhysicalRunningNumber($contract_type, $contract_number, $is_minamas_upstream = false){

        $matches = [];

        // Rubber contract number old format no slash, need to add slash
        if ( $contract_type == RubberContract::class && !strstr($contract_number, '/') ){

            $first_part = substr($contract_number, 0, 6);
            $second_part = substr($contract_number, 6);

            $contract_number = $first_part . '/' . $second_part;
        }

        if ( $is_minamas_upstream ) {

            // Minamas
            preg_match('/([a-zA-Z])?(\d+)\/[A-Za-z]{3}\/[A-Za-z]+\-[A-Za-z]+\/[A-Za-z]{3}/', $contract_number,$matches);

            if ( count($matches) >= 3 ){
                return (int) $matches[2];
            }else{
                // fallback to GTM
                preg_match('/\/([a-zA-Z]*)([0]*)(\d+)([a-zA-Z]*)$/', $contract_number,$matches);

                return (int) $matches[3];
            }

        }else{
            // GTM
            preg_match('/\/([a-zA-Z]*)([0]*)(\d+)([a-zA-Z]*)$/', $contract_number,$matches);

            return (int) $matches[3];
        }

    }

    private function getPngPhysicalRunningNumber($contract_type, $contract_number){

        $matches = [];

        preg_match('/\/([a-zA-Z]*)([0]*)(\d+)([a-zA-Z]*)$/', $contract_number,$matches);

        return (int) $matches[3];

    }

    private function getDeliveryOrderRunningNumber($contract_number) {

        $matches = [];

        preg_match('/^(\d{5})\/\d{5}/', $contract_number, $matches);

        if ( count($matches) >= 2 ){
            return (int) $matches[1];
        }

    }

    private function getBillingDocumentRunningNumber($contract_number){

        $matches = [];

        // minamas
        preg_match('/(\d+)\/[a-zA-Z]{3}\/[a-zA-Z]+\/\d+/', $contract_number,$matches);

        if ( count($matches) == 2 ){

            return (int) $matches[1];

        }else{

            // GTM
            preg_match('/[\-\/]([a-zA-Z]*)([0]*)(\d+)([a-zA-Z]*)$/', $contract_number,$matches);

            if ( count($matches) >= 3 ){
                return (int) $matches[3];
            }else{

                // GHN
                preg_match('/^(GHN|SDOI)\/[a-zA-Z]+\-(\d+)\//', $contract_number,$matches);

                if ( count($matches) >= 3) {
                    return (int) $matches[2];
                }else{
                    dd($contract_number, $matches);
                }

            }
        }


    }
}
