<?php

namespace App\Console\Commands;

use App\Helpers\DispatchAuditTrailJobParams;
use App\Interfaces\Contractable;
use App\Interfaces\IAuditTrailActions;
use App\Models\AuditTrail;
use App\Models\ContractAction;
use App\Models\FuturesContract;
use App\Models\NonBmdFuturesContract;
use App\Services\ContractMatchedService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;

class BatchUndoCloseout extends Command
{

    protected $iAuditTrailActions;
    protected $alreadyCloseOut;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'batch:undo-closeout {--type=} {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Batch undo contracts that has been closeout. works for both BMD or NONBMD';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(IAuditTrailActions $iAuditTrailActions)
    {
        parent::__construct();
        $this->iAuditTrailActions = $iAuditTrailActions;
        $this->alreadyCloseOut = collect([]);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Running undo batch closeout');

        $type = $this->option('type');
        $contract_class = null;

        if (!in_array($type, ['BMD', 'NONBMD'])) {
            throw new \Exception('Unsupported type - must be BMD or NONBMD only.');
        }

        // get excel file
        $file = fopen(storage_path('batch_undo_closeout.csv'), 'r');
        $list = [];
        $line_count = 0;
        $errors = [];

        $this->alreadyCloseOut = collect([]);

        while (($line = fgetcsv($file)) !== FALSE) {

            if ( $line_count == 0 ){
                $line_count++;
                continue;
            }

            if ( strlen($line[0]) == 0 ){
                $line_count++;
                continue;
            }

            $line_count++;

            $contract_number_left = trim($line[0]);
            $contract_number_right = trim($line[1]);

            if ($contract_number_left == $contract_number_right) {
                $this->warn('Left contract number is same as right contract number');
                $errors[] = 'Left contract number is same as right contract number';
                continue;
            }

            // lookup contract
            if ( $type === 'BMD' ){
                $contract_class = FuturesContract::query();
            }
            else if ( $type === 'NONBMD' ){
                $contract_class = NonBmdFuturesContract::query();
            }

            $contracts = $contract_class->whereIn('contract_number', [$contract_number_left, $contract_number_right])->get();

            if ( $contracts->count() != 2 ) {
                if ($contracts->where('contract_number', $contract_number_left)->count() == 0) {
                    $this->warn("Contract {$contract_number_left} not found.");
                    $errors[] = "Contract {$contract_number_left} not found.";
                }
                if ($contracts->where('contract_number', $contract_number_right)->count() == 0) {
                    $this->warn("Contract {$contract_number_right} not found.");
                    $errors[] = "Contract {$contract_number_right} not found.";
                }
                continue;
            }

            $check_is_close_out_error = false;

            foreach ($contracts as $contract) {
                if (!$contract->isClosedOut()) {
                    $this->warn("Contract {$contract->contract_number} is not closed-out.");
                    $errors[] = "Contract {$contract->contract_number} is not closed-out.";
                    $check_is_close_out_error = true;
                }
            }

            if (!$check_is_close_out_error) {
                $list[] = collect([
                    $contracts->where('contract_number', $contract_number_left)->values()->first(),
                    $contracts->where('contract_number', $contract_number_right)->values()->first(),
                ]);
            }
        }

        fclose($file);

        if ( count($errors) > 0 ){
            $this->error(join("\n", $errors));
            return false;
        }

        foreach ( $list as $queried_contracts ) {
            $this->info("Processing closeout {$queried_contracts[0]->contract_number} - {$queried_contracts[1]->contract_number}");
        }


        if ( $this->option('actual') != null ) {

            Auth::loginUsingId(1);
            $all_errors = [];

            foreach ($list as $index => $queried_contracts) {
                try {
                    $contract = $queried_contracts->first();

                    $errors = $this->actualUndo($contract);

                    if ( count($errors) > 0 ) {

                        // if left contract failed due to need to undo from opposite contract, try right contract
                        if ($errors[0] == 'Undo close out need to done on opposite contract.') {
                            $this->info("Left side contract not found, using right side contract instead.");

                            $contract = $queried_contracts->last();
                            $errors = $this->actualUndo($contract);

                            foreach ($errors as $error) {
                                $all_errors[] = '[' . $contract->contract_number . '] ' . $error;
                            }

                        }else{
                            foreach ($errors as $error) {
                                $all_errors[] = '[' . $contract->contract_number . '] ' . $error;
                            }
                        }
                    }

                } catch (\Exception $ex) {
                    // catch error such as price index entries not found which is not validated by validateAsyncSplit
                    $this->error("Exception thrown when processing " . $contract->contract_number);
                    $list[$index]['error'] = $ex->getMessage();

                    $all_errors[] = '[' . $contract->contract_number . '] ' . $ex->getMessage();
                }
            }

            \Log::info("====== BATCH UNDO CLOSEOUT ======");
            \Log::info(json_encode($all_errors));

            $this->info("DONE!");

        } else {
            $this->info("End dry run");
        }

    }

    public function actualUndo(Contractable $contract) {

        $errors = [];

        if ( $this->alreadyCloseOut->contains($contract->contract_number) ) {
            $this->warn("Contract " . $contract->contract_number . " already processed undo closeout before, ignore.");
            \Log::info("Contract " . $contract->contract_number . " already processed undo closeout before, ignore.");
            return $errors;
        }

        $this->info("Processing " . $contract->contract_number . " to undo closeout");

        $old_data = clone $contract;

        $service = app()->make(ContractMatchedService::class);
        $service->undoCloseOutContract($contract, $errors, false);

        if ( count($errors) > 0 ) {
            $this->info("Undo closeout for contract " . $contract->contract_number . " failed. " . json_encode($errors));
        } else {
            $this->info("Completed undo closeout for contract " . $contract->contract_number);

            \Log::info("Undo closeout success: {$contract->contract_number}");

            if ( count($errors) == 0 ) {
                $this->alreadyCloseOut->push($contract->contract_number);
            }

            $contract->refresh();
            $this->iAuditTrailActions->dispatchJob(
                new DispatchAuditTrailJobParams(
                    AuditTrail::CONTRACT_ACTIONS['ACTION']['CHANGE'],
                    trans('audittrail.description.contract_action_change', ['action' => ContractAction::ACTION_UNDO_CLOSE_OUT]),
                    $contract->id,
                    get_class($contract),
                    $old_data->toArray(),
                    $contract->id,
                    get_class($contract)
                )
            );
        }

        return $errors;

    }

}
