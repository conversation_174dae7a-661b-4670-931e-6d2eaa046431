<?php

namespace App\Console\Commands;

use App\Models\NonBmdFuturesContract;
use App\Models\Uom;
use App\Repositories\LotToUomConversionRepository;
use App\Repositories\UomConversionRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PatchNonBMDQuantity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:nonbmd-quantity {--actual} { --contract-number= : Contract number to patch eg.SCF/DTS/2211/SOY0001,SCF/DTS/2211/SOY0002}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch nonbmd (previously CBOT) contract quantity in mt based on conversion';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $contract_numbers = $this->option('contract-number');
        $contract_numbers = explode(',', $contract_numbers);

        DB::transaction(function () use ($contract_numbers) {
            $contracts = NonBmdFuturesContract::whereIn('contract_number', $contract_numbers)->get();
            if ($contracts->count() == 0) {
                $this->error("Contracts not found.");
                exit(1);
            }

            Auth::onceUsingId(1);
            $lotToUomConversionRepository = new LotToUomConversionRepository();
            $uomConversionRepository = new UomConversionRepository();
            $mt_uom = Uom::where('code', 'MT')->first();

            foreach ($contracts as $contract) {
                $before_patch_quantity_in_mt = $contract->quantity_in_mt;
                $product = $contract->product;
                $lot_to_uom = $lotToUomConversionRepository->getLotToUomConversionRate($product->defaultUom, $product);
                $uom_to_mt_conversion_rate = $uomConversionRepository->getUomConversionRate($product->defaultUom, $mt_uom, $product);
                $contract->quantity_in_mt = round($contract->quantity_in_lot * $lot_to_uom * $uom_to_mt_conversion_rate, 4);
                $this->info("Patching contract {$contract->contract_number} quantity_in_mt from {$before_patch_quantity_in_mt} to {$contract->quantity_in_mt}.");
                if ($this->option('actual') != null) {
                    $contract->save();
                }
            }
        });
    }
}
