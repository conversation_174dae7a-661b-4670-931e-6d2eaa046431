<?php

namespace App\Console\Commands;

use App\Services\FileManagementService;
use Illuminate\Console\Command;

class Cleanup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup all temp files';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $fileService = new FileManagementService('local_root');

        $files = $fileService->listFiles('temp/downloads');

        $fileService->deleteFiles($files);

        $this->info('Deleted files');

    }
}
