<?php

namespace App\Console\Commands;

use App\Models\InventoryLocation;
use App\Models\LegalEntity;
use App\Models\ContractStatus;
use Illuminate\Console\Command;
use App\Models\PhysicalContract;
use Illuminate\Support\Facades\DB;

class PatchExLocationTrading extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:exlocation-trading {legal_entity_code}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch old contracts inventory location for s4 wave 3 go-live for trading entities. Default to a specific inventory location by legal entity.';

    const INVENTORY_LOCATION_CODE_MAPPING = [
        'T' => 'SDGI Trading',
        'S' => 'SDGI Trading (Labuan)',
        'I' => 'SDGI Commodities',
        'D' => 'SDGI TPL',
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $dispatcher = PhysicalContract::getEventDispatcher();
        PhysicalContract::unsetEventDispatcher();

        $legal_entity_codes = $this->argument('legal_entity_code');

        $legal_entity_codes = explode(',', $legal_entity_codes);
        $legal_entity_codes = array_map('trim', $legal_entity_codes);
        $legal_entity_codes = array_map('strtoupper', $legal_entity_codes);

        $legal_entity_invloc_id_mapping = LegalEntity::whereIn('code', $legal_entity_codes)->get()->mapWithKeys(function ($item) {
            if( !isset(self::INVENTORY_LOCATION_CODE_MAPPING[$item['code']]) ){
                $this->error('inventory location id for legal entity '.$item['code'].' not found');
                throw new \Exception('inventory location id for legal entity '.$item['code'].' not found');
            }
            $inventory_location = InventoryLocation::where('code', self::INVENTORY_LOCATION_CODE_MAPPING[$item['code']])->first();
            if(!$inventory_location){
                $this->error('Inventory location not found. Code: '.self::INVENTORY_LOCATION_CODE_MAPPING[$item['code']]);
                throw new \Exception('Inventory location not found. Code: '.self::INVENTORY_LOCATION_CODE_MAPPING[$item['code']]);
            }
            // inventory location id => legal entity id
            return [$item['id'] => $inventory_location->id];
        })->toArray();

        if ( count($legal_entity_invloc_id_mapping) === 0 ) {
            $this->error('No legal entity found');
            throw new \Exception('No legal entity found');
        }

        $updatedCount = 0;

        DB::transaction(function() use (&$updatedCount, $legal_entity_invloc_id_mapping){
            PhysicalContract::whereNotIn('status', [ContractStatus::TYPE_DRAFT, ContractStatus::TYPE_POSTED, ContractStatus::TYPE_VOIDED])
                ->whereIn('legal_entity_id', array_keys($legal_entity_invloc_id_mapping))
                ->whereNull('inventory_location_id')
                ->where('shipment_month', '>=', '2021-01-01')       // only update shipment month 2021 jan onwards.
                ->chunkById(1000, function ($items) use (&$updatedCount, $legal_entity_invloc_id_mapping) {
                    foreach ($items as $item) {

                        $this->info("Patching contract {$item->contract_number}");

                        $inventory_location_id = $legal_entity_invloc_id_mapping[$item->legal_entity_id];
                        $this->info("Updating contract {$item->contract_number} inventory location to {$inventory_location_id}");
                        $item->update(['inventory_location_id' => $inventory_location_id]);
                        $updatedCount++;
                    }
                });
        });

        PhysicalContract::setEventDispatcher($dispatcher);
        $this->info("Total updated physical contracts: {$updatedCount}\n");
    }
}
