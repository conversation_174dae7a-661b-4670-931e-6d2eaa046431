<?php

namespace App\Console\Commands;

use App\Models\ContractStatus;
use App\Models\OptionsContract;
use App\Services\EmailService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;

/**
 * Class OptionsContractTriggerExpiryNotification
 * @package App\Console\Commands
 */
class OptionsContractTriggerExpiryNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'options:trigger-expiry-notification {x_days_before} {--now= }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate email notification to notify traders about to expire Options contracts x days before expiry';

    /** @var EmailService  */
    protected $email_service;
    protected $now;

    /**
     * OptionsContractTriggerExpiryNotification constructor.
     * @param EmailService $email_service
     */
    public function __construct(EmailService $email_service)
    {
        parent::__construct();
        $this->email_service = $email_service;
    }


    public function handle()
    {
        if ($this->option('now')) {
            $this->now = Carbon::parse($this->option('now'), 'Asia/Kuala_Lumpur')->tz('UTC');
        } else {
            $this->now = Carbon::now('UTC');
        }

        $x_days_before = $this->argument('x_days_before');

        $contracts = OptionsContract::with('trader')
            ->where('status', ContractStatus::TYPE_CONFIRMED)
            ->where(function ($q) {
                return $q->where('sub_status', '=', '')->orWhereNull('sub_status');
            })
            ->whereRaw(DB::raw('DATEDIFF("day", ?, expiry_date) <= ?'), [$this->now->endOfDay(), $x_days_before])
            ->get();

        $this->info(sprintf('Total %s contract are going to expire in %s day(s) from %s', $contracts->count(), $x_days_before, $this->now->toDateString()));

        $contracts_group_by_trader = $contracts->groupBy(function (OptionsContract $contract) {
            return $contract->trader->email;
        });

        foreach ($contracts_group_by_trader as $email => $contracts) {

            if ($contracts->count() <= 0) {
                continue;
            }

            $this->info(sprintf('%s has %s contract(s)', $email, $contracts->count()));

            $view = View::make('mail._options_contract_expiry_notification', [
                'now' => $this->now,
                'x_days_before' => $x_days_before,
                'contracts' => $contracts
            ]);

            $this->email_service->sendEmail([$email],
                sprintf('Options Contract(s) - expire in %s day(s)', $x_days_before),
                $view->render(),
                null, null, null);

            // sandbox mailtrap cannot send more than 2 emails per second
            if (!App::environment('production') || !App::environment('prod')) {
                sleep(3);
            }
        }
    }
}
