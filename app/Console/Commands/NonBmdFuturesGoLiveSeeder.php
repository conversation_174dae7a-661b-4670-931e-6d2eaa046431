<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Models\Uom;
use App\Models\UomConversion;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class NonBmdFuturesGoLiveSeeder extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'nonbmdfutures:seed {--actual} {--action=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    const ACTION_PRODUCT = 'product';
    const ACTION_CONTRACT_TYPE = 'contract-type';
    const ACTION_COUNTERPARTY = 'counterparty';
    const ACTION_PERMISSION = 'permission';
    const ACTION_CONTRACTABLE_TYPE = 'contractable-type';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual');
        $this->action = $this->option('action');

        $this->info("Running NonBmdFuturesGoLiveSeeder with action: {$this->action}");

        DB::transaction(function() {
            switch ($this->action) {
                case self::ACTION_PRODUCT:
                    $this->product();
                    break;
                case self::ACTION_CONTRACT_TYPE:
                    $this->contractType();
                    break;
                case self::ACTION_COUNTERPARTY:
                    $this->counterparty();
                    break;
                case self::ACTION_PERMISSION:
                    $this->permission();
                    break;
                case self::ACTION_CONTRACTABLE_TYPE:
                    $this->contractableType();
                    break;
                default:
                    $this->error('Action not found.');
                    break;
            }
        });
    }


    private function product()
    {
        $this->info("Seeding products...");

        $mt_uom = Uom::where('code', Uom::METRIC_TONNES)->first();
        $bulk = \App\Models\PackingUnit::where('code', \App\Models\PackingUnit::TYPE_BULK)->first();

        // create new products
        $product_list = [
            [
                'code' => 'LSG-ICE',
                'description' => 'Low Sulphur Gasoil (ICE)',
                'category' => Product::TYPE_DOWNSTREAM,
                'packing_unit_id' => $bulk->id,
                'rspo_type' => 'NA',
                'dry_rubber_percentage' => 0,
                'contract_number_reference' => 'LSG',
                'minamas_contract_number_reference' => 'LSG',
                'minamas_description' => 'Low Sulphur Gasoil (ICE)',
                'default_uom_id' => $mt_uom->id,
            ],
            [
                'code' => 'RBDPOLN-DCE',
                'description' => 'RBD Palm Olein (DCE)',
                'category' => Product::TYPE_DOWNSTREAM,
                'packing_unit_id' => $bulk->id,
                'rspo_type' => 'NA',
                'dry_rubber_percentage' => 0,
                'contract_number_reference' => 'RBDPOLN',
                'minamas_contract_number_reference' => 'RBDPOLN',
                'minamas_description' => 'RBD Palm Olein (DCE)',
                'default_uom_id' => $mt_uom->id,
            ],
            [
                'code' => 'RAPE-EURONEXT',
                'description' => 'Rapeseed (EURONEXT)',
                'category' => Product::TYPE_DOWNSTREAM,
                'packing_unit_id' => $bulk->id,
                'rspo_type' => 'NA',
                'dry_rubber_percentage' => 0,
                'contract_number_reference' => 'RAPE',
                'minamas_contract_number_reference' => 'RAPE',
                'minamas_description' => 'Rapeseed (EURONEXT)',
                'default_uom_id' => $mt_uom->id
            ],
        ];

        // setup Product
        if ($this->isActual) {

            foreach ($product_list as $entry) {
                $product = Product::firstOrCreate(
                    ['code' => $entry['code']],
                    $entry
                );

                // must have MT to MT conversion rate
                UomConversion::updateOrCreate([
                    'from_uom_id' => $mt_uom->id,
                    'to_uom_id' => $mt_uom->id,
                    'product_id' => $product->id,
                ], [
                    'multiplier' => 1
                ]);
            }

        }
        $this->info("Products seeding completed.");

    }

    private function counterparty()
    {
        $this->info("Seeding counterparties...");

        if ($this->isActual) {
            // create new counterparty for futures
            \App\Models\Counterparty::updateOrCreate(
                ['code' => 'ICE'],
                [
                    'code' => 'ICE',
                    'name' => 'ICE',
                    'is_internal' => false,
                    'is_upstream' => false,
                    'long_name' => 'INTERCONTINENTAL EXCHANGE',
                    'is_active' => true,
                    'address' => 'ICE',
                    'country' => 'US',
                    'billing_address' => 'ICE',
                    'billing_country' => 'US',
                    'is_certificate' => false,
                    'reference_1' => '',
                    'reference_2' => '',
                    'is_ffb_integration' => false,
                    'is_rubber_integration' => false,
                    'is_trading' => false,
                    'main_contact_email' => 'ICE',
                    'main_contact_name' => 'ICE',
                    'main_contact_phone' => '',
                    'billing_contact_name' => 'ICE',
                    'billing_contact_email' => 'ICE',
                    'billing_contact_phone' => '',
                    'operations_contact_name' => 'ICE',
                    'operations_contact_email' => 'ICE',
                    'operations_contact_phone' => '',
                    'is_minamas_integration' => false,
                    'is_stock_transfer' => false
                ]
            );

            \App\Models\Counterparty::updateOrCreate(
                ['code' => 'DCE'],
                [
                    'code' => 'DCE',
                    'name' => 'DCE',
                    'is_internal' => false,
                    'is_upstream' => false,
                    'long_name' => 'DALIAN COMMODITY EXCHANGE',
                    'is_active' => true,
                    'address' => 'DCE',
                    'country' => 'US',
                    'billing_address' => 'DCE',
                    'billing_country' => 'US',
                    'is_certificate' => false,
                    'reference_1' => '',
                    'reference_2' => '',
                    'is_ffb_integration' => false,
                    'is_rubber_integration' => false,
                    'is_trading' => false,
                    'main_contact_email' => 'DCE',
                    'main_contact_name' => 'DCE',
                    'main_contact_phone' => '',
                    'billing_contact_name' => 'DCE',
                    'billing_contact_email' => 'DCE',
                    'billing_contact_phone' => '',
                    'operations_contact_name' => 'DCE',
                    'operations_contact_email' => 'DCE',
                    'operations_contact_phone' => '',
                    'is_minamas_integration' => false,
                    'is_stock_transfer' => false
                ]
            );

            \App\Models\Counterparty::updateOrCreate(
                ['code' => 'EURONEXT'],
                [
                    'code' => 'EURONEXT',
                    'name' => 'EURONEXT',
                    'is_internal' => false,
                    'is_upstream' => false,
                    'long_name' => 'EUROPEAN NEW EXCHANGE TECHNOLOGY',
                    'is_active' => true,
                    'address' => 'EURONEXT',
                    'country' => 'US',
                    'billing_address' => 'EURONEXT',
                    'billing_country' => 'US',
                    'is_certificate' => false,
                    'reference_1' => '',
                    'reference_2' => '',
                    'is_ffb_integration' => false,
                    'is_rubber_integration' => false,
                    'is_trading' => false,
                    'main_contact_email' => 'EURONEXT',
                    'main_contact_name' => 'EURONEXT',
                    'main_contact_phone' => '',
                    'billing_contact_name' => 'EURONEXT',
                    'billing_contact_email' => 'EURONEXT',
                    'billing_contact_phone' => '',
                    'operations_contact_name' => 'EURONEXT',
                    'operations_contact_email' => 'EURONEXT',
                    'operations_contact_phone' => '',
                    'is_minamas_integration' => false,
                    'is_stock_transfer' => false
                ]
            );
            $this->info("Counterparties seeding completed.");
        }

    }

    private function permission()
    {
        $this->info("Updating permissions...");

        if ($this->isActual) {
            DB::table('permissions')
                ->where('name', 'like', '%cbot-futures%')
                ->orWhere('category', 'like', '%cbot-futures%')
                ->update([
                    'name' => DB::raw("REPLACE(name, 'cbot-futures', 'non-bmd-futures')"),
                    'category' => DB::raw("REPLACE(category, 'cbot-futures', 'non-bmd-futures')")
                ]);
            $this->info("Permissions update completed.");
        }
    }

    private function contractType()
    {
        $this->info("Updating contract type...");

        if ($this->isActual)
        {
            // rename contract type
            DB::table('master_contract_type')
                ->where('code', 'futures-cbot')
                ->update([
                    'code' => \App\Models\ContractType::TYPE_FUTURES_NON_BMD,
                    'name' => 'Non BMD Futures Contract'
                ]);
            $this->info("Contract type update completed.");

        }
    }

    private function contractableType()
    {
        $this->info("Updating contractable type...");

        $table_to_updates = [
            'splits',
            'audit_logs',
            'contracts_matched',
            'contracts'
        ];

        if ($this->isActual)
        {
            foreach ($table_to_updates as $table) {
                DB::statement("UPDATE {$table} SET contractable_type = 'App\Models\NonBmdFuturesContract' WHERE contractable_type = 'App\Models\CbotFuturesContract';");
            }

            DB::statement("UPDATE contracts_matched SET matched_contractable_type = 'App\Models\NonBmdFuturesContract' WHERE matched_contractable_type = 'App\Models\CbotFuturesContract';");
            DB::statement("UPDATE audit_logs SET auditable_type = 'App\Models\NonBmdFuturesContract' WHERE auditable_type = 'App\Models\CbotFuturesContract';");

            // Update the JSON data in the 'splits' table
            DB::statement("UPDATE splits SET contract = JSON_MODIFY(contract, '$.class', 'App\Models\NonBmdFuturesContract') WHERE contractable_type = 'App\Models\NonBmdFuturesContract' AND JSON_VALUE(contract, '$.class') = 'App\Models\CbotFuturesContract';");

            $this->info("Contractable type update completed.");
        }
    }
}
