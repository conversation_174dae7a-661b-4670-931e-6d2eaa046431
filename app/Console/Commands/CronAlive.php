<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CronAlive extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:alive';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test whether cron is alive';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('<PERSON><PERSON> is running at MYT ' . Carbon::now('Asia/Kuala_Lumpur')->format(config('spot.datetime')));
    }
}
