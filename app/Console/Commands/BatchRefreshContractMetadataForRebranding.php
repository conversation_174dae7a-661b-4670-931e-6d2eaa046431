<?php

namespace App\Console\Commands;

use App\Models\ContractStatus;
use App\Models\Counterparty;
use App\Models\InventoryLocation;
use App\Models\LegalEntity;
use App\Models\PhysicalContract;
use App\Repositories\AllocationRepository;
use App\Services\GeneralContractService;
use App\Services\PhysicalContractService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BatchRefreshContractMetadataForRebranding extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'metadata:batch-refresh-rebranding {--legal-entity-code=} {--contract-number=} {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Batch refresh physical contract metadata for rebranding exercise';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Auth::loginUsingId(1);

        $legal_entity = LegalEntity::where('code', $this->option('legal-entity-code'))->first();
        $contract_number = $this->option('contract-number');

        if ( $legal_entity === null ) {
            throw new \Exception('Legal entity not found.');
        }

        $this->info('Running Sime Darby Rebranding contract metadata patching script for legal entity ' . $legal_entity->name);

        // only patch draft, posted, confirmed contracts for the legal entity
        $contracts = PhysicalContract::whereIn('status', [ContractStatus::TYPE_DRAFT, ContractStatus::TYPE_POSTED, ContractStatus::TYPE_CONFIRMED])
            ->where('legal_entity_id', $legal_entity->id)
            ->orderBy('id', 'DESC');

        if ( $contract_number !== null ) {
            $contracts->where('contract_number', $contract_number);
        }

        $contracts = $contracts->get();

        $this->info(count($contracts) . ' contracts to be patched');

        sleep(5);

        foreach ( $contracts as $contract ) {

            $this->info('Patching contract ' . $contract->contract_number);

            if ( $this->option('actual') ) {
                $this->updateLegalEntityMetadata($contract);
            }

            $this->info('Contract updated.');

            $contract->save();
        }



    }

    private function updateLegalEntityMetadata(&$contract) {

        $metadata = json_decode($contract->metadata, true);
        $le = $contract->legalEntity;

        $contract->legal_entity_name = $le->name;

        $metadata['legal_entity']['name'] = $le->name;
        $metadata['legal_entity']['name_cn'] = $le->name_cn;
        $metadata['legal_entity']['company_registration_number'] = $le->company_registration_number;
        $metadata['legal_entity']['address'] = $le->address;
        $metadata['legal_entity']['billing_address'] = $le->billing_address;
        $metadata['legal_entity']['tax_registration_number'] = $le->tax_registration_number;
        $metadata['legal_entity']['website'] = $le->website;

        $contract->metadata = json_encode($metadata);

    }

}
