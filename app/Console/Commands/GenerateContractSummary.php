<?php

namespace App\Console\Commands;

use App\Helpers\ConfigHelper;
use App\Models\Config;
use App\Models\Contract;
use App\Models\ContractStatus;
use App\Models\ContractType;
use App\Services\EmailService;
use App\Services\ReportService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\View;

class GenerateContractSummary extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:contract-summary {--from_datetime= : from datetime in Asia/Kuala_Lumpur timezone } {--to_datetime= : to datetime in Asia/Kuala_Lumpur timezone }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate contract summary of all Draft (Public) created and contract from Drafted to Posted';

    /** @var ReportService */
    private $report_service;
    /** @var EmailService */
    private $email_service;
    /** @var array  */
    private $contract_by_types = [];
    private $to_datetime;
    private $from_datetime;

    /**
     * GenerateContractSummary constructor.
     * @param ReportService $report_service
     * @param EmailService $email_service
     */
    public function __construct(ReportService $report_service, EmailService $email_service)
    {
        parent::__construct();
        $this->report_service = $report_service;
        $this->email_service = $email_service;
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {
        // generate on Asia/Kuala_Lumpur timezone
        if ($this->option('to_datetime')) {
            $this->to_datetime = Carbon::parse($this->option('to_datetime'), 'Asia/Kuala_Lumpur');
        } else {
            $this->to_datetime = Carbon::today('Asia/Kuala_Lumpur')->setTime(6, 0, 0);
        }

        if ($this->option('from_datetime')) {
            $this->from_datetime = Carbon::parse($this->option('from_datetime'), 'Asia/Kuala_Lumpur');
        } else {
            $this->from_datetime = $this->to_datetime->clone()->subHours(24);
        }

        $this->info('Get summary (GTM+8) from: ' . $this->from_datetime->toDateTimeString() . ' - ' . $this->to_datetime->toDateTimeString());

        $this->getDraftToPostedSummary();
        $this->getDraftContractSummary();
        foreach ($this->contract_by_types as $class_name => $by_types) {
            $this->info('Sending email for ' . $class_name);
            $contract_type = Contract::MODEL_TO_CONTRACT_TYPE[$class_name] ?? null;
            $contract_name = ContractType::TYPE_MAPPING[$contract_type] ?? null;
            $camel_case = substr($class_name, strrpos($class_name, '\\') + 1);

            switch ($contract_type) {
                case ContractType::TYPE_PHYSICAL:
                    $route_name = 'contract-physical-index';
                    break;
                case ContractType::TYPE_PHYSICAL_RUBBER:
                    $route_name = 'rubber-physical-index';
                    break;
                case ContractType::TYPE_FUTURES_BMD:
                    $route_name = 'contract-futures-view-contract';
                    break;
                case ContractType::TYPE_STOCK_TRANSFER:
                    $route_name = 'stock-transfer-ticket-show';
                    break;
                default:
                    $route_name = null;
                    break;
            }

            $this->info(sprintf('%s - Draft to Posted: %s', ucwords($contract_name), count($by_types['draft_to_posted'] ?? [])));
            $this->info(sprintf('%s - Total Draft: %s', ucwords($contract_name), count($by_types['all_draft'] ?? [])));

            $contract_name = !empty($contract_name) ? $contract_name . ' Contracts' : $camel_case;
            $view = View::make('mail._contract_summary_email', [
                'from_datetime' => $this->from_datetime,
                'to_datetime' => $this->to_datetime,
                'route_name' => $route_name,
                'contract_name' => $contract_name,
                'draft_to_posted' => $by_types['draft_to_posted'] ?? [],
                'all_draft' => $by_types['all_draft'] ?? [],
            ]);

            /*$this->email_service->sendEmail([ConfigHelper::get(Config::TRADE_CONFIRMATION_EMAIL)],
                sprintf('Summary of %s', $contract_name),
                $view->render(),
                null, null, null);*/

            // sandbox mailtrap cannot send more than 2 emails per second
            if (!App::environment('production') || !App::environment('prod')) {
                sleep(3);
            }
        }
    }

    /**
     * Get all draft to posted from 6am - 6am daily (GTM+8 Asia/Kuala_lumpur)
     *
     * @throws \Exception
     */
    public function getDraftToPostedSummary()
    {
        // DB store time in UTC format, convert time to UTC
        $contract_types = $this->report_service->getDraftToPostedContractsGroupByContractClass([
            'from_datetime' => $this->from_datetime->setTimezone('UTC'),
            'to_datetime' => $this->to_datetime->setTimezone('UTC'),
        ]);

        foreach ($contract_types as $class_name => $contracts) {
            if (!isset($this->contract_by_types[$class_name])) {
                $this->contract_by_types[$class_name] = [
                    'draft_to_posted' => [],
                    'all_draft' => [],
                ];
            }
            $this->contract_by_types[$class_name]['draft_to_posted'] = $contracts;
        }
    }

    /**
     * Get all the draft contract and save into array key = all_draft
     */
    public function getDraftContractSummary()
    {
        $contracts_by_types = $this->report_service->getDraftContractsGroupByContractType([
            'excluded_contract_types' => [ContractType::TYPE_FFB_LTA],
            'status' => ContractStatus::TYPE_DRAFT, 'is_private' => false, 'public_only' => true
            ]);
        foreach ($contracts_by_types as $contract_type => $contracts) {
            if (count($contracts) <= 0) {
                continue;
            }
            $class_name = get_class($contracts[0]);

            if (!isset($this->contract_by_types[$class_name])) {
                $this->contract_by_types[$class_name] = [
                    'draft_to_posted' => [],
                    'all_draft' => [],
                ];
            }
            $this->contract_by_types[$class_name]['all_draft'] = $contracts;
        }
    }
}
