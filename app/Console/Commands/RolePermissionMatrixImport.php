<?php

namespace App\Console\Commands;

use App\Imports\RolePermissionMatrixImport as ImportsRolePermissionMatrixImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\HeadingRowImport;

class RolePermissionMatrixImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:import {filepath : file must be starting from storage path as root}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Permission via filepath given';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $filepath = $this->argument('filepath');

        if (!$filepath) {
            $this->error('Filepath is required for argument');
        }

        $importClass = new ImportsRolePermissionMatrixImport;

        Excel::import($importClass, $filepath, 'local_root');
    }
}
