<?php

namespace App\Console\Commands;

use App\Http\Exports\RolePermissionMatrix;
use Carbon\Carbon;
use Illuminate\Console\Command;

class RolePermissionMatrixExport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:export';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export Permission';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // no path then create
        if (!realpath(storage_path('role-permission'))) {
            mkdir(storage_path('role-permission'));
        }
        
        return (new RolePermissionMatrix())->store('role-permission/' . Carbon::now(config('spot.local_timezone'))->format('Ymd-His') .'.xlsx', 'local_root');
    }
}
