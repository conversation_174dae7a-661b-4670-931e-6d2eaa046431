<?php

namespace App\Console\Commands;

use App\Exports\TradesparentExport;
use App\Exports\TradesparentExportText;
use App\Services\FileManagementService;
use App\Services\IntegrationLogService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Excel;

class TradesparentDataExport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tradesparent:export { --view-name= : view name to import, need to use with --prefix } { --prefix= : file name prefix, need to use with --view-name } { --file-date= : file date used for file generation }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export Tradesparent view and upload to Tradesparent FTP';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        // ! Important notice: All the view is written in SQL script created in database. If changing information will have to ALTER VIEW
        $viewNames = [
            'CXC_GTM_JR_' => 'TRADESPARENT_SPOT_JR_Export',
            'CXC_GTM_JR_OS_' => 'TRADESPARENT_SPOT_JR_OUTSTANDING_Export',
            'CXC_GTM_NKKE_' => 'TRADESPARENT_SPOT_NKKE_Export',
            'CXC_GTM_NKKE_OS_' => 'TRADESPARENT_SPOT_NKKE_OUTSTANDING_Export',
            'CXC_GTM_NR_' => 'TRADESPARENT_SPOT_NR_Export',
            'CXC_GTM_NR_OS_' => 'TRADESPARENT_SPOT_NR_OUTSTANDING_Export',
            'CXC_GTM_SDA_' => 'TRADESPARENT_SPOT_SDAUSTRAL_Export',
            'CXC_GTM_SDA_OS_' => 'TRADESPARENT_SPOT_SDAUSTRAL_OUTSTANDING_Export',
            'CXC_GTM_SDFT_' => 'TRADESPARENT_SPOT_SDFT_Export',
            'CXC_GTM_SDFT_OS_' => 'TRADESPARENT_SPOT_SDFT_OUTSTANDING_Export',
            'CXC_GTM_SDGTL_' => 'TRADESPARENT_SPOT_SDGTL_Export',
            'CXC_GTM_SDGTL_OS_' => 'TRADESPARENT_SPOT_SDGTL_OUTSTANDING_Export',
            'CXC_MNS_' => 'TRADESPARENT_SPOT_Minamas_Export',
            'CXC_MNS_OS_' => 'TRADESPARENT_SPOT_Minamas_OUTSTANDING_Export',
            'CXC_GHN_' => 'TRADESPARENT_SPOT_GHN_Export',
            'CXC_GHN_OS_' => 'TRADESPARENT_SPOT_GHN_OUTSTANDING_Export',
            'CXC_GTM_SDOTPL_' => 'TRADESPARENT_SPOT_SDOTPL_Export',
            'CXC_GTM_SDOTPL_OS_' => 'TRADESPARENT_SPOT_SDOTPL_OUTSTANDING_Export',
            'CXC_GTM_SDOPNG_' => 'TRADESPARENT_SPOT_SDOPNG_Export',
            'CXC_GTM_SDOPNG_OS_' => 'TRADESPARENT_SPOT_SDOPNG_OUTSTANDING_Export'
        ];

        if ($this->isTodayFirst3WeekdaysOfMonth()) {
            $viewNames['SPOT_ADVFUL_'] = 'TRADESPARENT_SPOT_ADVFUL_Export';
        }

        if ($this->option('view-name') && $this->option('prefix')) {
            $viewNames = [$this->option('prefix') => $this->option('view-name')];
        }

        $this->info('TRADESPARENT EXPORT BEGINS');
        Log::info('TRADESPARENT EXPORT BEGINS');


        foreach ($viewNames as $filePrefix => $viewName) {

            if ($this->option('file-date')) {
                $timeGenerated = Carbon::parse($this->option('file-date'), config('spot.local_timezone'))->format('Ymd_His');
            } else {
                $timeGenerated = Carbon::now(config('spot.local_timezone'))->format('Ymd_His');
            }

            // ! the space before .csv is important else Tradesparent cannot process it
            $filename = $filePrefix . $timeGenerated . ' .csv';

            if (!realpath(storage_path('tradesparent'))) {
                mkdir(storage_path('tradesparent'));
            }

            $filepath = 'tradesparent/' . $filename;

            $success = (new TradesparentExportText($viewName))->store(storage_path($filepath));

            $loggerService = new IntegrationLogService;

            if (!$success) {
               $loggerService->logMessage('TRADESPARENT NO FILE GENERATED FOR VIEW ' . $viewName);
            }

            $this->line('CREATED FILE TO LOCAL: '. $filepath);

            try {

                $fileManagementService = new FileManagementService(config('filesystems.default'));

                $uploadedPath = $fileManagementService->uploadFile(storage_path($filepath), config('integration.basepath.TRADESPARENT'). $filename);

                if ( file_exists(storage_path($filepath)) ){
                     unlink(storage_path($filepath));
                     $this->line("Deleted temp file from " . storage_path($filepath));
                }

                $this->line('UPLOADED FILE TO : '. config('integration.basepath.TRADESPARENT'). $filename);

                $loggerService->logMessage('TRADEPARENT FILE GENERATED IN ' . $uploadedPath);
                $loggerService->log('TRADEPARENT FILE GENERATED', $filename, $uploadedPath);

            } catch (\Exception $e) {

                $loggerService->logMessage('TRADESPARENT FILE GENERATE ERROR FOR VIEW ' . $viewName);
                $loggerService->logStackTrace($e);
                $loggerService->log('TRADESPARENT FILE GENERATE ERROR FOR VIEW ' . $viewName, null, null, 'error', false, $e);

                continue;
            }

        }

        $this->info('TRADESPARENT EXPORT ENDS');
        Log::info('TRADESPARENT EXPORT ENDS');
    }

    public function isTodayFirst3WeekdaysOfMonth(): bool
    {
        $first3Weekdays = [];
        $today = Carbon::now(config('spot.local_timezone'));

        $day = $today->copy()->startOfMonth();
        while (count($first3Weekdays) < 3) {
            if ($day->isWeekday()) {
                $first3Weekdays[] = $day->copy();
            }

            $day->addDay();
        }

        foreach ($first3Weekdays as $weekday) {
            if ($weekday->isSameDay($today)) {
                return true;
            }
        }

        return false;
    }
}
