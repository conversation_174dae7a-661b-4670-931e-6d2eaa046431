<?php

namespace App\Console\Commands;

use App\Models\VesselNomination;
use App\Models\VesselNominationContract;
use Illuminate\Console\Command;

class PatchDuplicatedVesselNomination extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:duplicated-vessel-nomination';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch duplicated vessel nomination';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Update allocation quantity
        $this->info('Patching vessel nomination id = 41057');
        $correct_vessel_nomination_contract = VesselNominationContract::whereId(41057)->first();
        $old_allocation_quantity = $correct_vessel_nomination_contract->allocation_quantity;
        $correct_vessel_nomination_contract->allocation_quantity = 42;
        $this->info("Updating vessel nomination id = {$correct_vessel_nomination_contract->id} allocation quantity from $old_allocation_quantity to $correct_vessel_nomination_contract->allocation_quantity");
        $correct_vessel_nomination_contract->save();

        // Delete duplicated vessel nomination
        $this->info('Vessel nomination updated.');
        $this->info('Deleting duplicated vessel nomination id = 41058');
        if (VesselNominationContract::whereId(41058)->first()->delete()) {
            $this->info('Vessel nomination id = 41058 deleted.');
        }
    }
}
