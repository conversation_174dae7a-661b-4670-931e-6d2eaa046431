<?php

namespace App\Console\Commands;

use App\Helpers\DocumentHelper;
use App\Models\BillingDocument;
use App\Models\DocumentTemplates;
use App\Models\TemplateFieldValue;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PatchEinvoiceTemplateTaxAmount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:template-tax-amount {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch E-invoice existing template tax amount to tax amount after knockoff';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $updated_count = 0;

        $einv_document_template = DocumentTemplates::where('name', DocumentTemplates::EINVOICE_GENERIC_BILLING_PRINTOUT_NAME)->firstOrFail();

        $template_field_values = TemplateFieldValue::query()
            ->with([
                'contractable' => function ($query) {
                    $query->select(['id', 'tax_amount']);
                    $query->with([
                        'lineItems' => function ($query) {
                            $query->select(['id', 'billing_document_id', 'tax_amount', 'is_deduct_advance']);
                        },
                    ]);
                }
            ])
            ->whereHasMorph('contractable', [BillingDocument::class])
            ->where('document_template_id', $einv_document_template->id)
            ->get();

        $this->info('Total rows: ' . $template_field_values->count());

        DB::transaction(function() use (&$updated_count, &$template_field_values){
            foreach ($template_field_values as $template_field_value) {
 
                $value = $template_field_value->value;

                // already has tax_amount_after_knockoff, skip
                if (isset($value['tax_amount_after_knockoff'])) {
                    continue;
                }

                // remove tax_amount
                unset($value['tax_amount']);
                // add tax_amount_after_knockoff
                $value['tax_amount_after_knockoff'] = [
                    'template_field_id' => 'tax_amount_after_knockoff',
                    'value' => DocumentHelper::getTaxAmountAfterKnockoff($template_field_value->contractable),
                ];

                if ($this->option('actual')) {
                    $template_field_value->update([
                        'value' => $value,
                    ]);
                }
                $updated_count++;
            }
        });

        $this->info("Update {$updated_count} Template Field Value");
    }
}
