<?php

namespace App\Console\Commands;

use App\Models\Counterparty;
use App\Models\ExternalSystem;
use App\Models\IntegrationReference;
use App\Models\LegalEntity;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class EinvoicingGoLiveSeeder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'einvoicing:seed {--actual} {--action=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run seeder for e-invoicing data during e-invoicing go live';

    protected $isActual;
    protected $action;

    const ACTION_LEGAL_ENTITY = 'legal-entity';
    const ACTION_COUNTERPARTY = 'counterparty';

    const EXISTING_LEGAL_ENTITIES = [
        [
            "code" => "P",
            "tin_no" => "C11427628050",
            "msic_code" => "10401",
        ],
        [
            "code" => "T",
            "tin_no" => "C2871808090",
            "msic_code" => "46202",
        ],
        [
            "code" => "W",
            "tin_no" => "C3873811020",
            "msic_code" => "01261",
        ],
        [
            "code" => "C",
            "tin_no" => "C859249070",
            "msic_code" => "01261",
        ],
        [
            "code" => "K",
            "tin_no" => "C8862839060",
            "msic_code" => "01261",
        ],
        [
            "code" => "B",
            "tin_no" => "C7850025070",
            "msic_code" => "01261",
        ],
        [
            "code" => "G",
            "tin_no" => "C852990060",
            "msic_code" => "10401",
        ],
        [
            "code" => "S",
            "tin_no" => "LE2475214903",
            "msic_code" => "46202",
        ],
        [
            "code" => "A",
            "tin_no" => "C3894589040",
            "msic_code" => "46202",
        ],
        [
            "code" => "I",
            "tin_no" => "C26813495020",
            "msic_code" => "46202",
        ],
        [
            "code" => "H",
            "tin_no" => "C26813496000",
            "msic_code" => "10403",
        ],
        [
            "code" => "E",
            "tin_no" => "C26813497090",
            "msic_code" => "10402",
        ],
        [
            "code" => "F",
            "tin_no" => "C26813582070",
            "msic_code" => "10402",
        ],
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->isActual = false;
        $this->action = null;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual');
        $this->action = $this->option('action');

        switch ($this->action) {
            case self::ACTION_LEGAL_ENTITY:
                $this->legalEntity();
                break;
            case self::ACTION_COUNTERPARTY:
                $this->counterparty();
                break;
            default:
                $this->error('Action not found.');
                break;
        }
    }

    private function legalEntity()
    {
        $this->info('Seeding e-invoicing legal entity');
        $updated_legal_entity_count = 0;

        DB::transaction(function() use (&$updated_legal_entity_count) {
            foreach (self::EXISTING_LEGAL_ENTITIES as $data) {
                $legal_entity = LegalEntity::where('code', $data['code'])->first();

                if(!$legal_entity){
                    $this->error("Legal Entity (code:{$data['code']}) not found.");
                    continue;
                }

                $this->info("Updating Legal Entity (code:{$data['code']}).");
                if ($this->isActual) {
                    $old_tin_no = $legal_entity->tin_no ?? 'null';
                    $old_msic_code = $legal_entity->msic_no ?? 'null';

                    $legal_entity->update([
                        'tin_no' => $data['tin_no'],
                        'msic_code' => $data['msic_code'],
                    ]);
                    $legal_entity->refresh();
                    $this->info("Updated Legal Entity (code:{$data['code']}) | Tin no: {$old_tin_no} -> {$legal_entity->tin_no} | MSIC Code: {$old_msic_code} -> {$legal_entity->msic_code}");
                    $updated_legal_entity_count++;
                }
            }
        });
        $this->info("Total Legal Entity Updated: {$updated_legal_entity_count}");
    }

    private function counterparty()
    {
        $file = storage_path('csv-source/einvoice_counterparties.csv');

        $this->info('Seeding e-invoicing counterparty');
        $external_system = ExternalSystem::where('code', ExternalSystem::SAP)->firstOrFail();
        $updated_counterparty_count = 0;

        if(file_exists($file)){
            $row = 0;
            if (($handle = fopen($file, "r")) !== false) {
                DB::transaction(function() use (&$handle, &$row, &$updated_counterparty_count, $external_system) {
                    while (($data = fgetcsv($handle, null, ',')) !== false) {
                        $row ++;
                        // skip header row
                        if($row == 1){
                            continue;
                        }

                        // $data['0'] = counterparty integration reference no
                        // $data['1'] = tin_no
                        // $data['2'] = company_registration_number

                        $integration_refs_with_counterparty = IntegrationReference::with('integratable')->where('integratable_type', Counterparty::class)
                        ->where('type', 'customer_id')
                        ->where('external_system_id', $external_system->id)
                        ->where('reference_no', $data['0'])
                        ->get()
                        ->unique('integratable_id');

                        if(count($integration_refs_with_counterparty) == 0){
                            $this->error("Counterparty integration reference {$data['0']} not found.");
                            continue;
                        }

                        $this->info("Found ". count($integration_refs_with_counterparty). " counterparties with integration reference no {$data['0']}");

                        foreach($integration_refs_with_counterparty as $integration_ref_with_counterparty){
                            $counterparty = $integration_ref_with_counterparty->integratable;
                            $old_tin_no = $counterparty->tin_no ?? 'null';
                            $old_company_registration_number = $counterparty->company_registration_number ?? 'null';

                            if ($this->isActual) {
                                $counterparty->update([
                                    'tin_no' => strtoupper($data['1']) == 'NULL' ? null : $data['1'],
                                    'company_registration_number' => strtoupper($data['2']) == 'NULL' ? null : $data['2'],
                                ]);
                                $updated_counterparty_count++;
                                $counterparty->refresh();
                                $this->info("Updated Counterparty {$counterparty->long_name} (ID: {$counterparty->id}) | Tin no: {$old_tin_no} -> {$counterparty->tin_no} | Company Registration Number: {$old_company_registration_number} -> {$counterparty->company_registration_number}");
                            }
                        }
                    }
                });
                $this->info("Total Counterparty Updated: {$updated_counterparty_count}");
                fclose($handle);
            }
        }else{
            $this->error('File not found');
        }
    }
}
