<?php

namespace App\Console\Commands;

use App\Models\Counterparty;
use App\Models\PaymentTerm;
use App\Models\PaymentTermsProfitCenter;
use App\Models\Product;
use App\Models\ProductsProfitCenter;
use App\Models\User;
use App\Models\UsersProfitCenter;
use Illuminate\Console\Command;

class CopyProfitCenterMasterData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'master-data:copy { --from-pc= : Profit Center code to copy from}
                                             { --to-pc= : Profit Center code to copy to }
                                             { --master-data= : which master data to copy (only support payment_terms, products, users }
                                             { --actual }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Copy master data (payment terms, products, users) from one profit center to another';

    /**
     * Allowed master data to copy from one profit center to another
     *
     * @var string[]
     */
    protected $allowed_master_data = [
        'payment_terms' => PaymentTerm::class,
        'products' => Product::class,
        'users' => User::class,
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $from_pc = $this->option('from-pc');

        $copy_from_pc = Counterparty::where('code', $from_pc)->where('is_internal', 1)->first();

        if (!$copy_from_pc) {
            $this->error("Profit center {$from_pc} not found ");
            exit(1);
        }


        $to_pc = $this->option('to-pc');

        $copy_to_pc = Counterparty::where('code', $to_pc)->where('is_internal', 1)->first();

        if (!$copy_to_pc) {
            $this->error("Profit center {$to_pc} not found ");
            exit(1);
        }

        $master_data = $this->option('master-data');

        if (empty($master_data)) {
            $this->error("Master data is required.");
            exit(1);
        }

        $master_data_to_copy = explode(',', $master_data);

        foreach ($master_data_to_copy as $datum) {
            if (!in_array($datum, array_keys($this->allowed_master_data))) {
                $this->error("Master data {$datum} is not allowed");
                exit(1);
            }
        }

        if (in_array('payment_terms', $master_data_to_copy)) {
            $this->info("Copying payment terms from profit center {$from_pc} to profit center {$to_pc}");

            $payment_terms_data = $copy_from_pc->profitCenterPaymentTerms
                ->map(function ($item) use ($copy_to_pc) {
                    return [
                        'payment_terms_id' => $item->id,
                        'counterparty_id' => $copy_to_pc->id,
                    ];
                })->toArray();

            $this->info(count($payment_terms_data) . ' payment terms to profit center mapping found.');

            if (count($payment_terms_data) > 0) {

                if ($this->option('actual')) {

                    foreach ($payment_terms_data as $datum) {
                        $this->info("Copying payment terms id {$datum['payment_terms_id']} to {$to_pc}");
                        PaymentTermsProfitCenter::updateOrCreate($datum);
                    }

                    $this->info("Total of " . count($payment_terms_data) . " payment terms copied.");

                }
            }

            $this->info("End");
        }

        if (in_array('users', $master_data_to_copy)) {
            $this->info("Copying users from profit center {$from_pc} to profit center {$to_pc}");

            $users_data = $copy_from_pc->users
                ->map(function ($item) use ($copy_to_pc) {
                    return [
                        'user_id' => $item->id,
                        'counterparty_id' => $copy_to_pc->id,
                    ];
                })->toArray();

            $this->info(count($users_data) . ' users to profit center mapping found.');

            if (count($users_data) > 0) {

                if ($this->option('actual')) {

                    foreach ($users_data as $datum) {
                        $this->info("Copying users id {$datum['user_id']} to {$to_pc}");
                        UsersProfitCenter::updateOrCreate($datum);
                    }

                    $this->info("Total of " . count($users_data) . " users copied.");

                }
            }

            $this->info("End");

        }

        if (in_array('products', $master_data_to_copy)) {
            $this->info("Copying products from profit center {$from_pc} to profit center {$to_pc}");

            $products_data = $copy_from_pc->products
                ->map(function ($item) use ($copy_to_pc) {
                    return [
                        'product_id' => $item->id,
                        'counterparty_id' => $copy_to_pc->id,
                    ];
                })->toArray();

            $this->info(count($products_data) . ' products to profit center mapping found.');

            if (count($products_data) > 0) {

                if ($this->option('actual')) {

                    foreach ($products_data as $datum) {
                        $this->info("Copying products id {$datum['product_id']} to {$to_pc}");
                        ProductsProfitCenter::updateOrCreate($datum);
                    }

                    $this->info("Total of " . count($products_data) . " products copied.");

                }
            }

            $this->info("End");
        }
    }
}
