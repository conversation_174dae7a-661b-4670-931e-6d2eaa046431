<?php

namespace App\Console\Commands;

use App\Models\Currency;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateCurrencyNameLang extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:currency-name-lang';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To update translation columns in master_currency';

    const LIST_DISTINCT_VALUE = [
        'IDR' => [
            'id' => 'RUPIAH',
        ]
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $currencies = Currency::all();

        $updatedCount = 0;

        DB::transaction(function() use (&$updatedCount, $currencies){

            $list_distinct_value = self::LIST_DISTINCT_VALUE;

            foreach($currencies as $currency){

                $currency_name = $currency->name;

                if(in_array($currency->code, array_keys($list_distinct_value))){
                    $distinct_values = $list_distinct_value[$currency->code];
                    $name_lang_en = isset($distinct_values['en']) ? $distinct_values['en'] : $currency_name;
                    $name_lang_id = isset($distinct_values['id']) ? $distinct_values['id'] : $currency_name;
                }else{
                    $name_lang_en = $name_lang_id = $currency_name;
                }

                $currency->update([
                    'name_lang_en' => $name_lang_en,
                    'name_lang_id' => $name_lang_id,
                ]);

                $updatedCount++;
            }
        });

        $this->info("Total updated currencies: {$updatedCount}\n");
    }
}
