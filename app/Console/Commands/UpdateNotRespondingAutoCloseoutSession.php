<?php

namespace App\Console\Commands;

use App\Models\QueueJobSession;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateNotRespondingAutoCloseoutSession extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:auto-closeout-session';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set in-progress sessions that not responding more than 30 mins to error';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        QueueJobSession::where('status', QueueJobSession::IN_PROGRESS)
            ->where('action', QueueJobSession::CONTRACT_CLOSEOUT)
            ->where('created_at', '<', Carbon::now()->subMinutes(30))
            ->update([
                'status' => QueueJobSession::ERROR,
                'error_message' => 'Contract closeout job not responding, automatically set to error'
            ]);

        QueueJobSession::where('status', QueueJobSession::IN_PROGRESS)
            ->where('action', QueueJobSession::CONTRACT_CREATION)
            ->where('created_at', '<', Carbon::now()->subMinutes(30))
            ->update([
                'status' => QueueJobSession::ERROR,
                'error_message' => 'Contract creation job not responding, automatically set to error'
            ]);
    }
}
