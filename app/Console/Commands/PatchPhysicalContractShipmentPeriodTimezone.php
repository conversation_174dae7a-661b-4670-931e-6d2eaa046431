<?php

namespace App\Console\Commands;

use App\Models\PhysicalContract;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PatchPhysicalContractShipmentPeriodTimezone extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:shipment-period {--actual} { --contract-number= : Contract number to patch eg.SCF/DTS/2211/SOY0001,SCF/DTS/2211/SOY0002}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch physical contract shipment period from/to change it to no timezone';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $contract_numbers = $this->option('contract-number');
        $actual = $this->option('actual');

        if ( $contract_numbers !== null ) {
            $contract_numbers = explode(',', $contract_numbers);
        }

        $years = ['2024', '2023', '2022', '2021', '2020', '2019', '2018', '2017', '2016', '2015', '2014', '2013'];

        foreach ( $years as $year ) {

            $this->info("Processing contract date year {$year}");
            Log::info("Processing contract date year {$year}");

            DB::transaction(function () use ($contract_numbers, $year, $actual) {

                if ( $contract_numbers !== null && is_array($contract_numbers) ){
                    $contracts = PhysicalContract::whereRaw('YEAR(contract_date) = ' . $year)
                        ->whereIn('contract_number', $contract_numbers)
                        ->whereNotNull('shipment_date_from')
                        ->whereNotNull('shipment_date_to')
                        ->orderBy('id', 'DESC')
                        ->get();
                }else{
                    $contracts = PhysicalContract::whereRaw('YEAR(contract_date) = ' . $year)
                        ->whereNotNull('shipment_date_from')
                        ->whereNotNull('shipment_date_to')
                        ->orderBy('id', 'DESC')
                        ->get();
                }

                if ($contracts->count() == 0) {
                    $this->error("No contracts found.");
                    Log::info("No contracts found.");
                }
                else{
                    Auth::onceUsingId(1);

                    $total_contracts_count = count($contracts);
                    $this->info("Patching " . $total_contracts_count . " contracts");
                    Log::info("Patching " . $total_contracts_count . " contracts");
                }

                foreach ( $contracts as $index => $contract ) {

                    $this->info("Patching {$index} of {$total_contracts_count} - {$contract->contract_number}");
                    Log::info("Patching {$index} of {$total_contracts_count} - {$contract->contract_number}");

                    // determine which original timezone is the contract
                    $timezone = self::resolveTimezone($contract->shipment_date_from);

                    if ( $timezone == null ){
                        $this->error("Unable to determine timezone. Skip");
                        Log::info("Unable to determine timezone. Skip");
                        continue;
                    }else{
                        $this->info("Timezone determined as " . $timezone);
                        Log::info("Timezone determined as " . $timezone);
                    }

                    // update new shipment period
                    $new_shipment_date_from = Carbon::parse($contract->shipment_date_from, 'UTC')->tz($timezone)->startOfDay();
                    $new_shipment_date_to = Carbon::parse($contract->shipment_date_to, 'UTC')->tz($timezone)->endOfDay();

                    $this->info("Updating {$contract->contract_number} to {$new_shipment_date_from->toDateTimeString()} - {$new_shipment_date_to->toDateTimeString()}");
                    Log::info("Updating {$contract->contract_number} to {$new_shipment_date_from->toDateTimeString()} - {$new_shipment_date_to->toDateTimeString()}");

                    if ( $actual ){
                        $contract->shipment_date_from = $new_shipment_date_from;
                        $contract->shipment_date_to = $new_shipment_date_to;

                        $contract->save();
                    }

                }

            });
        }

    }

    public static function resolveTimezone($datetime_utc_start_of_day) {

        $malaysia_timezone = Carbon::parse($datetime_utc_start_of_day, 'UTC')->tz('Asia/Kuala_Lumpur')->isStartOfDay();
        $indonesia_timezone = Carbon::parse($datetime_utc_start_of_day, 'UTC')->tz('Asia/Jakarta')->isStartOfDay();
        $portmoresby_timezone = Carbon::parse($datetime_utc_start_of_day, 'UTC')->tz('Pacific/Port_Moresby')->isStartOfDay();
        $guadalcanal_timezone = Carbon::parse($datetime_utc_start_of_day, 'UTC')->tz('Pacific/Guadalcanal')->isStartOfDay();

        if ( $malaysia_timezone ) {
            return 'Asia/Kuala_Lumpur';
        }
        else if ($indonesia_timezone) {
            return 'Asia/Jakarta';
        }
        else if ($portmoresby_timezone) {
            return 'Pacific/Port_Moresby';
        }
        else if ($guadalcanal_timezone) {
            return 'Pacific/Guadalcanal';
        }
        else{
            return null;
        }

    }
}
