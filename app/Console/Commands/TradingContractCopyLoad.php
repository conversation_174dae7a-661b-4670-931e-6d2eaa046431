<?php

namespace App\Console\Commands;

use App\Helpers\ErrorHelper;
use App\Services\TradingContractLoadService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TradingContractCopyLoad extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trading-loads:sync { --contract-number= : Specific contract number to run. If specify Trading contract, will only sync trading contract. If specify first agency contract of the string, will sync all Trading contracts}
                                               { --load-id= : Specific load to sync via its ID }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Sales Trading Contracts\'s loads with Purchase Agency Contract loads';

    private $tradingContractLoadService;

    private $load_id;

    private $contract_number;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(TradingContractLoadService $tradingContractLoadService)
    {
        parent::__construct();
        $this->tradingContractLoadService = $tradingContractLoadService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->line('Begin Copy Trading Loads');

        $this->contract_number = $this->option('contract-number');
        $this->load_id = $this->option('load-id');

        $logs = $this->runScript();

        if (count($logs) > 0) {
            foreach ($logs as $message) {
                $this->info($message);
            }
        } else {
            $this->info('No contracts / loads to process');
        }

        $this->line('End Copy Trading Loads');

        $datetimestring = Carbon::now(config('spot.local_timezone'))->format('Ymd');
        $this->info("Logs can be found in " . storage_path("logs/trading-copy-$datetimestring.log"));
    }

    public function runScript()
    {
        foreach ($this->tradingContractLoadService->getEligibleTradingStringsWithContract($cursor = true, $this->contract_number, $this->load_id) as $string) {

            $this->info("Processing trading string " . $string->chain_id);

            // get related trading contracts
            try {

                $firstContract = $this->tradingContractLoadService->getFirstContractOfTradingString($string);

                $tradingContracts = $this->tradingContractLoadService->getTradingContractsFromString($string);

            } catch (\Exception $e) {
                $this->tradingContractLoadService->log($e->getMessage());
                $this->error($e->getMessage());
                continue;
            }

            // loop through each trading contracts
            foreach ($tradingContracts as $tradingContract) {

                // has contract number as parameter
                if ($this->contract_number) {

                    if ($tradingContract->contract_number == $this->contract_number) {

                        try {

                            $this->tradingContractLoadService->log('(Eligible) Contract :' . $tradingContract->contract_number . ' | With loads from first string contract: ' . $firstContract->contract_number . ' | of String: ' . $string->code);

                            $this->tradingContractLoadService->syncLoads($firstContract, $tradingContract, $this->load_id);

                        } catch (\Exception $e) {
                            Log::error('Trading load exception found: ' . $e->getMessage(), $e->getTrace());
                            ErrorHelper::sentry($e);
                            continue;
                        }
                    }

                    // else just takes all trading contract to sync
                } else {

                    try {

                        $this->tradingContractLoadService->log('(Eligible) Contract :' . $tradingContract->contract_number . ' | With loads from first string contract: ' . $firstContract->contract_number . ' | of String: ' . $string->code);

                        $this->tradingContractLoadService->syncLoads($firstContract, $tradingContract, $this->load_id);

                    } catch (\Exception $e) {
                        Log::error('Trading load exception found: ' . $e->getMessage(), $e->getTrace());
                        ErrorHelper::sentry($e);
                        continue;
                    }
                }
            }
        }

        return $this->tradingContractLoadService->getLogs();
    }

}
