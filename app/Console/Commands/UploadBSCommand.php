<?php

namespace App\Console\Commands;

use App\Helpers\ErrorHelper;
use App\Models\IntegrationSettings;
use App\Repositories\IntegrationLogRepository;
use App\Services\FileManagementService;
use App\Services\IntegrationLogService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UploadBSCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'upload { --bs= : Business Scenario } {--no-exit}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Upload Business Scenario XML and billing text files';

    /** @var \App\Services\FileManagementService */
    private $fileManagementService;

    /** @var \App\Repositories\IntegrationLogRepository */
    private $integrationLogRepository;

    /** @var \App\Services\IntegrationLogService */
    private $integrationLogService;

    /** @var int $files_uploaded */
    private $files_uploaded = 0;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(FileManagementService $fileManagementService, IntegrationLogRepository $integrationLogRepository, IntegrationLogService $integrationLogService)
    {
        parent::__construct();
        $this->fileManagementService = $fileManagementService;
        $this->integrationLogRepository = $integrationLogRepository;
        $this->integrationLogService = $integrationLogService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $start = microtime(true);
        Log::info("Running Upload BS Command");
        $this->integrationLogService->logMessage('== UPLOAD BS COMMAND ==');

        $settings = IntegrationSettings::where('bound', 'outbound');

        if ($bs = $this->option('bs')) {
            $settings->where('code', $bs);
        }

        $this->settings = $settings->get();

        try {

            if ($this->settings->count() == 0) {
                throw new \Exception('No Integration Setting for Outbound', 1);
                exit(1);
            }

            foreach ($this->settings as $setting) {
                $this->uploadFilesFromSetting($setting);
            }

            if ($this->files_uploaded == 0) {
                throw new \Exception("No files to process", 0);
            }

            $this->info("{$this->files_uploaded} Files Uploaded " . ($bs ?? ''));
            Log::info("{$this->files_uploaded} Files Uploaded");

            $this->line("Execution: " . (microtime(true) - $start) . "ms");

        } catch (\Exception $e) {

            if ($e->getCode() > 0) {
                $this->error($e->getMessage());
            } else {
                $this->line($e->getMessage());
            }

            Log::info("Upload BS error: " .$e->getMessage());

            $this->line("Execution: " . (microtime(true) - $start) . "ms");
            if (!$this->option('no-exit')) {
                exit($e->getCode());
            }
        }

    }

    /**
     * Upload files from setting given
     *
     * @param \App\Models\IntegrationSettings $setting
     * @return void
     */
    private function uploadFilesFromSetting($setting)
    {

        // get local storage path
        if ($setting->local_path) {
            $localpath = $setting->local_path;
        } else {
            // fallback
            $localpath = config("integration.localpath")[$setting->stream];
        }

        $files = array_diff(scandir($localpath), ['.', '..']);

        if (count($files) == 0) {
            $this->integrationLogService->logMessage('No files found.');
        }

        $this->uploadFilesIfMatchRegex($localpath, $files, $setting);

    }

    private function uploadFilesIfMatchRegex(string $localpath, array $files, IntegrationSettings $setting)
    {
        $businessScenario = app()->make($setting->class_path);

        $this->integrationLogService->logMessage('--- Business Scenario - ' . get_class($businessScenario) . ' ---');
        $this->integrationLogService->logMessage('Scanning current folder ' . $localpath);
        $this->integrationLogService->logMessage(count($files) . ' files found.');

        // loop through and upload via filename regex
        foreach ($files as $idx => $filename) {

            try {

                $matches = [];

                preg_match($setting->filename_regex, $filename, $matches);
                // filename matches the BS
                if (!empty($matches[0])) {

                    $filepath = $localpath . $filename;

                    $this->integrationLogService->logMessage('>>> Legit uploading file ' . $filepath);

                    $businessScenario->uploadToRemote($filepath);

                    $this->integrationLogService->logMessage('File uploaded.');

                    DB::transaction(function() use ($filename, &$businessScenario){

                        if ( method_exists($businessScenario, 'updateDocumentReleasedByFileName') ) {
                            $this->integrationLogService->logMessage('Updating billing document status to released using file name ' . $filename);
                            $businessScenario->updateDocumentReleasedByFileName($filename);
                        } else {
                            $this->integrationLogService->logMessage('updateDocumentReleasedByFileName function doesnt exist');
                        }

                        $this->integrationLogRepository->updateFileUploaded($filename);
                        $this->integrationLogService->logMessage('Set file ' . $filename . ' to uploaded.');

                    }, 6);

                    $this->integrationLogService->logMessage('Transaction ended.');

                    $this->files_uploaded++;
                }

            } catch (\Throwable $e) {
                Log::error("=== UPLOAD BS COMMAND ERROR ===");
                Log::error($e->getMessage());
                Log::error($e->getTraceAsString());

                $this->integrationLogService->logMessage("ERROR: " . $e->getMessage());
                $this->integrationLogService->logStackTrace($e);

                ErrorHelper::sentry($e);
                continue;
            }

        }
    }
}
