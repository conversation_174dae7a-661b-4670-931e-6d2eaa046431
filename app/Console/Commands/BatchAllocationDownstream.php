<?php

namespace App\Console\Commands;

use App\Models\Contract;
use App\Models\ContractStatus;
use App\Models\DeliverTo;
use App\Models\InventoryLocation;
use App\Repositories\AllocationRepository;
use App\Services\AllocationService;
use App\Services\GeneralContractService;
use App\Services\IntegrationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class BatchAllocationDownstream extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'batch:allocation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Allow users to allocate multiple contracts at one go (Downstream Malaysia only) via excel file';

    private $allocationService;

    private $contractService;

    private $allocationRepository;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->allocationService = app(AllocationService::class);
        $this->contractService = app(GeneralContractService::class);
        $this->allocationRepository = app(AllocationRepository::class);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // read excel file
        $collection = Excel::toCollection(null, storage_path('batch_allocation.xlsx'));

        $data = $collection[0];
        $user = Auth::loginUsingId(1);

        $inventory_locations = InventoryLocation::get()->keyBy('id');
        $deliver_tos = DeliverTo::get()->keyBy('id');
        $contract_numbers = $data->pluck(0);
        unset($contract_numbers[0]);    // unset header

        $contracts = Contract::with('contractable.allocations')->whereIn('contract_number', $contract_numbers->toArray())
            ->get()->pluck('contractable')->keyBy('contract_number');

        $this->info("Total of " . (count($data)-1) . " rows");
        Log::info("Total of " . (count($data)-1) . " rows");

        foreach ( $data as $row_number => $row ){

            try{

                if ( $row_number == 0 ){
                    continue;
                }

                if ( count($row) != 3 ){
                    throw new \Exception('Row must have 3 columns');
                }

                $contract_number = $row[0];
                $inventory_location_id = $row[1];
                $deliver_to_id = $row[2];
                $contractable = $contracts[$contract_number];
                $quantity = $contractable->quantity;
                $inventory_location = $inventory_locations[$inventory_location_id] ?? null;
                $deliver_to = $deliver_tos[$deliver_to_id] ?? null;

                if ( $contractable->status != ContractStatus::TYPE_CONFIRMED ){
                    $this->error('Contract ' . $contract_number . ' status is not confirmed. Skipping');
                    Log::info('Contract ' . $contract_number . ' status is not confirmed. Skipping');
                    continue;
                }

                if ( $inventory_location == null ){
                    $this->error('Contract ' . $contract_number . ' Inventory location ID not found. Skipping');
                    Log::info('Contract ' . $contract_number . ' Inventory location ID not found. Skipping');
                    continue;
                }

                if ( $deliver_to == null ){
                    $this->error('Contract ' . $contract_number . ' Deliver To ID not found. Skipping');
                    Log::info('Contract ' . $contract_number . ' Deliver To ID not found. Skipping');
                    continue;
                }

                $allocations = $contractable->activeAllocations()->get();

                if ( count($allocations) > 0 ){
                    $this->error('Contract ' . $contract_number . ' already got allocation. Skipping');
                    Log::info('Contract ' . $contract_number . ' already got allocation. Skipping');
                    continue;
                }

                DB::beginTransaction();

                Log::info('Allocating ' . $contract_number . ' ' . $quantity . ' MT FOR INVLOC [' .
                    $inventory_location->name . '] DELIVER TO [' . $deliver_to->name . ']');

                $this->info('Allocating ' . $contract_number . ' ' . $quantity . ' MT FOR INVLOC [' .
                    $inventory_location->name . '] DELIVER TO [' . $deliver_to->name . ']');

                // Run allocation
                $this->allocationService->allocate($user, $inventory_location, $deliver_to, $contractable, $quantity, null);

                Log::info('DONE');
                $this->info('DONE');

                DB::commit();

            }catch(\Exception $e){
                DB::rollBack();
                Log::error($e->getMessage());
                Log::error($e->getTraceAsString());
            }

            sleep(3);
        }

    }
}
