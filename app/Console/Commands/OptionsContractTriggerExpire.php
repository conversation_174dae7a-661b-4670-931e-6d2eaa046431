<?php

namespace App\Console\Commands;

use App\Models\ContractStatus;
use App\Models\OptionsContract;
use App\Models\User;
use App\Services\OptionsContractService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

/**
 * Class OptionsContractTriggerExpire
 * @package App\Console\Commands
 */
class OptionsContractTriggerExpire extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'options:trigger-expire {--now= }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scan through options contact to update contract to expired';

    /** @var OptionsContractService */
    protected $optionsContractService;
    protected $now;

    /**
     * ExpireOptionsContract constructor.
     * @param OptionsContractService $optionsContractService
     */
    public function __construct(OptionsContractService $optionsContractService)
    {
        parent::__construct();
        $this->optionsContractService = $optionsContractService;
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {
        // make sure first user is system default user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user available');
        }
        Auth::login($user);

        $this->info('Login as user: ' . $user->email);
        if ($this->option('now')) {
            $this->now = Carbon::parse($this->option('now'), 'Asia/Kuala_Lumpur')->tz('UTC');
        } else {
            $this->now = Carbon::now('UTC');
        }

        $contracts = OptionsContract::where('status', ContractStatus::TYPE_CONFIRMED)
            ->where(function ($q) {
                return $q->where('sub_status', '=', '')->orWhereNull('sub_status');
            })
            ->whereRaw(DB::raw('DATEDIFF("day", expiry_date, ?) > 7'), [$this->now->endOfDay()])
            ->get();

        $this->info('Total contract are going to expire: ' . $contracts->count());

        foreach ($contracts as $contract) {
            DB::beginTransaction();
            try {
                $this->info($contract->contract_number . ' - ' . $contract->expiry_date);
                $errors = [];
                $this->optionsContractService->changeAction($contract, OptionsContract::ACTION_EXPIRED, $errors);
                if (!empty($errors)) {
                    throw new \Exception(implode(',', $errors));
                }
                DB::commit();
            } catch (\Exception $e) {
                $this->error($e->getMessage() . $e->getLine() . $e->getFile());
                DB::rollBack();
            }

        }
    }
}
