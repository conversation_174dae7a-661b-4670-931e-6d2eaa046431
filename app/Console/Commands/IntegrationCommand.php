<?php

namespace App\Console\Commands;

use App\Models\IntegrationSettings;
use App\Services\IntegrationService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class IntegrationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'integration:run { --stream= : upstream | downstream }
                                            { --bound= : inbound | outbound }
                                            { --bs= : business scenario code eg: BS400 or GtmUpstreamBillingService }
                                            { --profit-center= : (billing) profit center code eg: SD, AP }
                                            { --no-encryption : (billing) no encryption needed }
                                            { --invoice= : (billing - outbound) process only the specified invoice number }
                                            { --cron : Run only jobs enabled for cron }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run SAP SEMUA PI integration';

    /** @var App\Services\IntegrationService */
    private $service;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(IntegrationService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // * Console requires login, else somewhere check user will fail
        Auth::loginUsingId(1);
        Log::info("Running Integration:Run Command");

        $stream = $this->option('stream');

        $bound = $this->option('bound');

        $profit_center_code = $this->option('profit-center');

        $no_encryption = $this->option('no-encryption');

        $businessScenario = $this->option('bs');

        $options = $this->option();

        $this->line('Initiating Integration');

        $benchmark['start'] = time();
        $this->line("Job started at " . Carbon::now('Asia/Kuala_Lumpur')->toDateTimeString());

        if (!$stream) {
            $stream = ['upstream', 'downstream'];
        } else {
            $stream = [$stream];
        }

        if (!$bound) {
            $bound = [IntegrationSettings::TYPE_INBOUND, IntegrationSettings::TYPE_OUTBOUND];
        } else {
            $bound = [$bound];
        }

        try {

            $bar = $this->output->createProgressBar(count($stream) * count($bound));

            foreach ($stream as $s) {
                foreach ($bound as $b) {
                    $this->line("");
                    $this->line("=== Start processing $s $b $businessScenario ===");

                    $this->service->process($s, $b, $businessScenario, $options, $profit_center_code, $no_encryption);

                    $bar->advance();
                    $this->line("");
                    $this->line("=== Processed $s $b $businessScenario ===");
                }
            }

            $this->line("");
            $this->line('Integration Process Complete');


            $benchmark['end'] = time();
            $this->line("Job completed at " . Carbon::now('Asia/Kuala_Lumpur')->toDateTimeString());

            $this->line("Took " . ($benchmark['end'] - $benchmark['start']) . 's to complete.');

            // if job took more than 1 min, log it
            if ( ($benchmark['end'] - $benchmark['start']) >= 50 ){
                Log::warning('Integration:run ran for more than 50 seconds.');
            }

            exit(0);

        } catch (\Exception $e) {
            if (config('app.env') == 'development' || config('app.env') == 'local') {
                dd($e);
            }
            if (app()->bound('sentry')) {
                app('sentry')->captureException($e);
            }
            exit(1);
        }

    }
}
