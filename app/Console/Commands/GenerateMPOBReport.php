<?php

namespace App\Console\Commands;

use App\Helpers\IntegrationReferenceHelper;
use App\Helpers\DatabaseHelper;
use App\Models\Contract;
use App\Models\ContractStatus;
use App\Models\Counterparty;
use App\Models\DeliverTo;
use App\Models\ExternalSystem;
use App\Models\Incoterms;
use App\Models\LegalEntity;
use App\Models\PackingUnit;
use App\Models\PhysicalContract;
use App\Models\Port;
use App\Models\PricingType;
use App\Models\Product;
use App\Models\User;
use App\Services\FileManagementService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

/**
 * DEPRECATED. DO NOT USE THIS ANYMORE
 * Class GenerateMPOBReport
 * @package App\Console\Commands
 */
class GenerateMPOBReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:mpob-report {--now=} {--legal-entity-code=all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate MPOB report for each legal entity.';

    protected $storage_disk = 'local_root';

    protected $folder = 'integration/mpob';
    protected $remote_path = 'home/mpob/';
    protected $legal_entity_code = 'all';

    const MPOB_DELIVER_TO_FOREIGN = 'MYS-X';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     * @throws \Exception
     */
    public function handle()
    {
        // make sure first user is system default user
        $user = User::first();
        if (!$user) {
            throw new \Exception('No user available');
        }

        Auth::login($user);
        $this->info('Login as user: ' . $user->email);

        if ($this->option('now')) {
            $this->start = Carbon::parse($this->option('now'), 'Asia/Kuala_Lumpur')->startOfDay()->tz('UTC');
            $this->end = Carbon::parse($this->option('now'), 'Asia/Kuala_Lumpur')->endOfDay()->tz('UTC');
        } else {
            // default take the previous day contract
            $this->start = Carbon::now('Asia/Kuala_Lumpur')->subDays(1)->startOfDay()->tz('UTC');
            $this->end = Carbon::now('Asia/Kuala_Lumpur')->subDays(1)->endOfDay()->tz('UTC');
        }

        if ( $this->option('legal-entity-code') != 'all' ){
            $this->legal_entity_code = $this->option('legal-entity-code');
        }

        $this->info('processing..');
        $this->processMPOBReport();
    }

    public function processMPOBReport()
    {
        // criteria
        // 1) external contract
        // 2) physical contract
        // 3) contract status is confirmed

        if ( $this->legal_entity_code == 'all' ){
            $legal_entities = LegalEntity::with('profitCenters')->get();
        }else{
            $legal_entities = LegalEntity::with('profitCenters')->where('code', $this->legal_entity_code)->get();
        }

        $generated_files = [];

        foreach ( $legal_entities as $legal_entity ){

            $data = '';
            $file_name_only = Carbon::now()->timestamp . '_' . $this->start->tz('Asia/Kuala_Lumpur')->toDateString() . '_mpob_report_' . $legal_entity->code . '.txt';
            $filename = $this->folder . '/' . $file_name_only;
            $processed_contract = 0;

            $this->info('Processing legal entity ' . $legal_entity->name);
            $this->info('Filename: ' . $filename);

            Storage::disk($this->storage_disk)->delete($filename);

            PhysicalContract::with(['product', 'incoterms_relationship', 'packingUnit', 'allocations.deliverTo.state', 'allocations.deliverTo.country', 'legalEntity'])
                ->isExternal()
                ->where('legal_entity_id', $legal_entity->id)
                ->status(ContractStatus::STATUSES_AFTER_CONFIRMED)
                ->where('created_at', '>=', $this->start->toDateTimeString())
                ->where('created_at', '<=', $this->end->toDateTimeString())
                ->chunk(100, function ($contracts) use (&$processed_contract, &$data) {

                    foreach ($contracts as $contract) {

                        // need to manually convert MYR to RM because of mapping requirements
                        $currency = ($contract->currency == "MYR") ? 'RM' : $contract->currency;
                        $incoterms_code = '';
                        $packing_unit_code = '';
                        $load_port_code = '';
                        $discharge_port_code = '';
                        $deliver_to_code = ''; // default to the first allocation deliver to location

                        $product_code = IntegrationReferenceHelper::getExternalValue(ExternalSystem::MPOB_REPORTING, Product::class, 'id', $contract->product->id);

                        // incoterms integration reference
                        if (isset($contract->incoterms_relationship->id)) {
                            $incoterms_code = IntegrationReferenceHelper::getExternalValue(
                                ExternalSystem::MPOB_REPORTING,
                                Incoterms::class,
                                'id',
                                $contract->incoterms_relationship->id
                            );
                        }

                        // packing unit integration reference
                        if (isset($contract->packingUnit->id)) {
                            $packing_unit_code = IntegrationReferenceHelper::getExternalValue(
                                ExternalSystem::MPOB_REPORTING,
                                PackingUnit::class,
                                'id',
                                $contract->packingUnit->id
                            );
                        }

                        // load port integration reference
                        if (isset($contract->getProposedLoadPorts()->first()->code)) {
                            $load_port_code = IntegrationReferenceHelper::getExternalValue(
                                ExternalSystem::MPOB_REPORTING,
                                Port::class,
                                'code',
                                $contract->getProposedLoadPorts()->first()->code,
                                ''
                            );
                        }

                        // discharge port integration reference
                        if (isset($contract->getProposedDischargePorts()->first()->code)) {
                            $discharge_port_code = IntegrationReferenceHelper::getExternalValue(
                                ExternalSystem::MPOB_REPORTING,
                                Port::class,
                                'code',
                                $contract->getProposedDischargePorts()->first()->code,
                                ''
                            );
                        }

                        // deliver to integration reference
                        if ($contract->allocations->first()) {

                            $deliver_to = $contract->allocations->first()->deliverTo;

                            if ( $deliver_to->state != null && $deliver_to->state->mpob_code != null ){
                                $deliver_to_code = $deliver_to->state->mpob_code;
                            }else{
                                $deliver_to_code = self::MPOB_DELIVER_TO_FOREIGN;
                            }
                        }

                        // Determine buyer and seller
                        if ( $contract->transaction_type == Contract::TYPE_SALES ){

                            $seller = IntegrationReferenceHelper::getExternalValue(
                                ExternalSystem::MPOB_REPORTING,
                                LegalEntity::class,
                                'id',
                                $contract->legal_entity_id,
                                $contract->legalEntity->name
                            );

                            $seller_license_number = $contract->legalEntity->mpob_license_number ?? '';

                            $buyer = IntegrationReferenceHelper::getExternalValue(
                                ExternalSystem::MPOB_REPORTING,
                                Counterparty::class,
                                'id',
                                $contract->counterparty->id,
                                $contract->counterparty->long_name
                            );

                            $buyer_license_number = $contract->counterparty->mpob_license_number ?? '';

                        }else{

                            $seller = IntegrationReferenceHelper::getExternalValue(
                                ExternalSystem::MPOB_REPORTING,
                                Counterparty::class,
                                'id',
                                $contract->counterparty->id
                            );

                            $seller_license_number = $contract->counterparty->mpob_license_number ?? '';

                            $buyer = IntegrationReferenceHelper::getExternalValue(
                                ExternalSystem::MPOB_REPORTING,
                                LegalEntity::class,
                                'id',
                                $contract->legal_entity_id
                            );
                            $buyer_license_number = $contract->legalEntity->mpob_license_number ?? '';
                        }

                        $row = [
                            Carbon::parse($contract->contract_date)->setTimezone(Auth::user()->timezone)->format('d/m/yy'), // contract date
                            $contract->transaction_type, // transaction type
                            $product_code, // Product Code
                            $packing_unit_code, // packing code
                            strtoupper(substr($buyer, 0, 60)), // buyer name
                            $buyer_license_number, // buyer license number
                            strtoupper(substr($seller, 0, 60)), // seller name,
                            $seller_license_number, // seller license number
                            $contract->contract_number, // contract number
                            number_format($contract->quantity, 4, '.', ''), // contract quantity 4 decimal
                            $currency, // currency but ours is MYR their is RM
                            number_format($contract->getContractPrice(), 4, '.', ''), // unit price 4 decimal
                            $incoterms_code, // incoterms
                            number_format($contract->getContractPrice(), 4, '.', ''), // FOB Equivalent Unit price / Metric Ton
                            str_pad(Carbon::parse($contract->shipment_month)->setTimezone(Auth::user()->timezone)->format('m'), '2', '0', STR_PAD_LEFT), // delivery month
                            intval(Carbon::parse($contract->shipment_month)->setTimezone(Auth::user()->timezone)->format('yy')), // delivery year
                            $deliver_to_code, // place of delivery / collection
                            $load_port_code, // port of loading
                            $discharge_port_code, // port of discharge
                            $contract->product->description . ' ' . $contract->incoterms . ' ' . $contract->getIncotermPortName() // product + incoterms + basis port
                        ];

                        $data .= collect($row)->join(chr(254)) . "\r\n";

                    }

                    $processed_contract += $contracts->count();

                });

            Storage::disk($this->storage_disk)->put($filename, trim($data));
            $generated_files[] = $file_name_only;

            $this->info("Processed ({$processed_contract}) contracts..");

        }

        $this->info('Uploading files to file server..');

        // Upload files to server
        $fileService = new FileManagementService('ftp-dev');

        foreach ($generated_files as $file){
            $this->info('Uploading ' . $file);
            $fileService->uploadFile(storage_path($this->folder . '/' . $file), $this->remote_path . $file);
        }

        $this->info('done..');

    }
}
