<?php

namespace App\Console\Commands;

use App\Models\ContractType;
use App\Models\BillingDocument;
use Illuminate\Console\Command;
use App\Models\DocumentTemplates;
use Illuminate\Support\Facades\DB;
use App\Services\ContractReportService;
use App\Repositories\TemplateFieldsRepository;

class PatchEinvoiceMissingTemplateFieldValue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:einvoice-cms {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch confirmed billing document with missing default template field value';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $updatedCount = 0;

        // need to login as superadmin first.
        \Auth::onceUsingId(1);

        $einv_document_template = DocumentTemplates::where('name', \App\Models\DocumentTemplates::EINVOICE_GENERIC_BILLING_PRINTOUT_NAME)->first();

        $query = BillingDocument::query()->with(['contractable'])
            ->select(DB::raw('billing_documents.*'))
            ->leftJoin('template_field_values', function ($join) use ($einv_document_template) {
                $join->on('billing_documents.id', '=', 'template_field_values.contractable_id')
                    ->where('template_field_values.contractable_type', '=', 'App\Models\BillingDocument')
                    ->where('template_field_values.document_template_id', $einv_document_template->id);
            })
            ->whereNull('template_field_values.id')
            ->where('billing_documents.is_einvoice', true)
            ->whereIn('billing_documents.status', [BillingDocument::INVOICE_STATUS_CONFIRMED, BillingDocument::INVOICE_STATUS_POSTED, BillingDocument::INVOICE_STATUS_RELEASED]);

        $this->info('Total affected rows: ' . $query->count());

        DB::transaction(function() use (&$updatedCount, &$einv_document_template, &$query){

            $contractReportService = app()->make(ContractReportService::class);
            $templateFieldsRepository = app()->make(TemplateFieldsRepository::class);

            $query->chunkById(300, function ($billing_documents) use (&$updatedCount, $einv_document_template, $contractReportService, $templateFieldsRepository) {
                foreach ($billing_documents as $billing_document) {

                    $this->info("Patching Billing Document {$billing_document->reference_number}, ID {$billing_document->id}, Status {$billing_document->status}");

                    if ( $this->option('actual') ) {
                        $data = $contractReportService->getInvoiceTemplateFieldValueOptions($billing_document->contractable, $billing_document, $einv_document_template->id, ContractType::MODEL_TO_TYPE[$billing_document->contractable_type]);

                        $data->transform(function ($value) use ($billing_document) {
                            return $value['value'];
                        });

                        $templateFieldsRepository
                            ->saveTemplateFieldValue(
                                $billing_document,
                                $einv_document_template->id,
                                $data
                        );
                        $updatedCount++;
                    }
                }
            }, 'billing_documents.id');
        });

        $this->info("Created Template Field Value for {$updatedCount} billing documents");
    }
}
