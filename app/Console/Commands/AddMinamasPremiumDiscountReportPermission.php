<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;

class AddMinamasPremiumDiscountReportPermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:add-premium-discount-minamas-report-permission';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Adds new permission for Premium Discount Minamas Report';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Permission::create([
            'name' => 'report-minamas-premium-discount',
            'guard_name' => 'web',
            'category' => 'report-contract'
        ]);

        $this->info('Added Quality Claim Report Permission');
    }
}
