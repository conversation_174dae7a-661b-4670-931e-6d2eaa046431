<?php

namespace App\Console\Commands;

use App\Helpers\ConfigHelper;
use App\Models\Config;
use App\Models\ForexIndexEntry;
use App\Repositories\ForexIndexEntryRepository;
use App\Repositories\ForexIndexRepository;
use App\Repositories\MasterDataRepository;
use App\Services\EmailService;
use App\Services\IntegrationLogService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class IntegrateBloombergForexPriceIndex extends Command
{

    const FOREX_FOLDER = 'data/forex/convert';

    const FOREX_FILENAME_PREFIX = 'forex.txt.';

    const LOCAL_PATH = "integration-test/bloomberg/forex/";

    const FILESYSTEM_DRIVER = 'sftp-bloomberg';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'integrate:bloomberg-forex { --now= : (YYYYMMDD) Set the date for retrieval }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Integrates Bloomberg Forex Price index to Forex Index Master';

    /** @var ForexIndexRepository * */
    protected $forexIndexRepository;

    /** @var ForexIndexEntryRepository * */
    protected $forexIndexEntryRepository;

    /** @var MasterDataRepository * */
    protected $masterDataRepository;

    /**
     * Create a new command instance.
     *
     * @param MasterDataRepository $masterDataRepository
     * @param ForexIndexRepository $forexIndexRepository
     * @param ForexIndexEntryRepository $forexIndexEntryRepository
     */
    public function __construct(
        MasterDataRepository $masterDataRepository,
        ForexIndexRepository $forexIndexRepository,
        ForexIndexEntryRepository $forexIndexEntryRepository
    ) {
        $this->masterDataRepository = $masterDataRepository;
        $this->forexIndexRepository = $forexIndexRepository;
        $this->forexIndexEntryRepository = $forexIndexEntryRepository;
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $now = $this->option('now');
        $verbose = $this->option('verbose');

        Log::info("Running Bloomberg Forex price Index integration");

        try{

            $this->downloadFromSource($now);

            $price_index_dataset = array();
            $price_index_entry_dataset = array();
            $price_information_key = array(
                'settle',
                'high',
                'low',
                'close',
            );

            $data_content_start = "SECURITIES|ERROR CODE|NUM FLDS|PX_LAST|PX_ASK|PX_BID|PX_MID|\n";
            $data_content_end = $date_start = "END-OF-DATA\n";
            $date_end = "END-OF-FILE\n";

            // Get All Files from the folder, currently set to storage/integration-test/bloomberg/forex
            $file_list = File::files(storage_path(self::LOCAL_PATH));

            foreach ($file_list as $integration_file) {
                $file_name = $integration_file->getFilename();

                $string_content = $this->getDataContentFromIntegrationFile($integration_file, $data_content_start);

                // Separate Date Data
                $file_generated_time = $this->getEntryDateFromDataContent($string_content, $date_start, $date_end);
                $price_index_entry_dataset['entry_date'] = $file_generated_time->copy()->setTimezone('UTC')->toDateTimeString();

                $delivery_start = $file_generated_time->copy()->startOfDay()->setTimezone('UTC')->toDateTimeString();
                $delivery_end = $file_generated_time->copy()->endOfDay()->setTimezone('UTC')->toDateTimeString();

                $price_index_entry_dataset['delivery_start_date'] = $delivery_start;
                $price_index_entry_dataset['delivery_end_date'] = $delivery_end;
                // End Separate Date Data

                $forex_data_array = $this->getForexDataFromDataContent($string_content, $data_content_start, $data_content_end);

                $data_sources = $this->masterDataRepository->getExternalSystemsList();
                $price_index_dataset['source_id'] = $data_sources
                    ->where('name', 'Bloomberg') // Hardcoded, Specific for this command
                    ->pluck('id')->first();
                $price_index_dataset['application'] = \App\Models\ForexIndex::APPLICATION_BOTH;
                $price_index_dataset['is_integration'] = 1;
                $price_index_dataset['forward_months'] = 0; // Hardcoded, No data available
                $price_index_entry_dataset['open'] = null; // master_price_index_entry open

                $to_be_updated = collect();
                $to_be_deleted = [];

                // Get all existing forex index
                $existing_forex_index = $this->forexIndexRepository->getForexIndexMaster();
                $existing_forex_index = $existing_forex_index->mapWithKeys(function ($value) {
                    return [$value->code => $value];
                });

                foreach ($forex_data_array as $fd_string) {

                    if ($fd_string !== "") {

                        $fd_array = explode("|", $fd_string);
                        $data_key = strstr($fd_array[0], " ", true);

                        $price_index_dataset['code'] = trim(str_replace('Curncy', '', $fd_array[0]));
                        $price_index_dataset['base_currency'] = (str_split($data_key, 3))[0];
                        $price_index_dataset['target_currency'] = (str_split($data_key, 3))[1];

                        if ($verbose) {
                            $this->info('Inserting ' . $price_index_dataset['code']);
                        }

                        // create price index if doesn't exist
                        if (!isset($existing_forex_index[$price_index_dataset['code']])) {
                            $created_price_index = $this->forexIndexRepository->createForexIndex($price_index_dataset);
                        } else {
                            $created_price_index = $existing_forex_index[$price_index_dataset['code']];
                        }

                        $price_index_entry_dataset['indexable_type'] = get_class($created_price_index);
                        $price_index_entry_dataset['indexable_id'] = $created_price_index->id;

                        unset($fd_array[0], $fd_array[1], $fd_array[2], $fd_array[7]);
                        $fd_array = array_values($fd_array);

                        $price_information = array_combine($price_information_key, $fd_array);

                        // since batch insert won't trigger mutator, we have to multiply ourselves
                        foreach ($price_information as $key => &$price) {
                            $price = $price * ForexIndexEntry::PRICE_DECIMAL_MULTIPLIER;
                        }

                        $price_index_entry_dataset['created_at'] = Carbon::now();

                        $price_index_entry_dataset = array_merge($price_index_entry_dataset, $price_information);

                        $to_be_updated->push($price_index_entry_dataset);

                        if (!in_array($created_price_index->id, $to_be_deleted)) {
                            $to_be_deleted[] = $created_price_index->id;
                        }

                    }

                }

                $loggerService = new IntegrationLogService;

                $this->info('Inserting ' . count($to_be_updated) . ' entries');
                $loggerService->log('Inserting ' . count($to_be_updated) . ' entries',  $file_name);

                $this->forexIndexEntryRepository->batchCreateForexIndexEntry($to_be_updated, $to_be_deleted, $delivery_start, $delivery_end);

                $this->info('Integration completed for ' . $file_name);
                $loggerService->log('Integration completed for ' . $file_name,  $file_name);

                $full_path = storage_path(self::LOCAL_PATH) . $file_name;
                $uploadedFileName = $this->uploadFromLocal($full_path, true);

                $this->info("File archived to $uploadedFileName");
                $loggerService->log('File archived to ' . $uploadedFileName,  $file_name);

                $loggerService->log('Bloomberg integration success with entries count: ' . count($to_be_updated), $file_name, $uploadedFileName);

            }
            echo 'Integration completed' . PHP_EOL;
            Log::info("Bloomberg integration completed.");
            $this->info('Bloomberg integration completed.');

        }catch(\Exception $e){
            Log::error($e->getTraceAsString());
            $this->error($e->getMessage());
            $this->sendAlertEmail('Bloomberg Forex Index General Exception', $e->getMessage() . "<br /><br />" . $e->getTraceAsString());
        }

    }

    public function getDataContentFromIntegrationFile($integration_file, $from_line)
    {
        $file_content = file_get_contents($integration_file);

        // replace newline
        $file_content = str_replace("\r\n", "\n", $file_content);

        $string_content = strstr($file_content, $from_line);
        return $string_content;
    }

    public function getEntryDateFromDataContent($string_content, $from_line, $to_line)
    {
        $date_string = strstr($string_content, $from_line);

        $date_data_sliced = substr($date_string, strlen($from_line));
        $date_data_string = substr($date_data_sliced, 0, strpos($date_data_sliced, $to_line));
        $date_data_string = str_replace("\n", '', $date_data_string);
        $date_data_array = explode('=', $date_data_string);

        // convert to malaysia time to get correct UTC equivalent
        $file_generated_time = Carbon::createFromFormat('D M  d H:i:s e Y', $date_data_array[1])->tz('Asia/Kuala_Lumpur');

        return $file_generated_time;

    }

    public function getForexDataFromDataContent($string_content, $from_line, $to_line)
    {
        $data_string = substr($string_content, strlen($from_line));
        $forex_data_string = substr($data_string, 0, strpos($data_string, $to_line));
        $forex_data_array = explode("\n", $forex_data_string);
        return $forex_data_array;
    }

    public function downloadFromSource($date = null)
    {
        $currentDateString = Carbon::now(config('spot.local_timezone'))->format('Ymd');

        if ($date) {
            $currentDateString = Carbon::parse($date, config('spot.local_timezone'))->format('Ymd');
        }

        $fileService = new \App\Services\FileManagementService(self::FILESYSTEM_DRIVER);

        $full_filename = implode('/', [self::FOREX_FOLDER, self::FOREX_FILENAME_PREFIX . $currentDateString]);

        if ($fileService->exists($full_filename)) {
            $this->info("Initiate download for $full_filename");
            Log::info("Bloomberg: initiate download for $full_filename");

            $localStorage = $fileService->downloadFile($full_filename, self::LOCAL_PATH, [], false);

            $this->info("File Downloaded to $localStorage");
            Log::info("Bloomberg: file Downloaded to $localStorage");

        } else {

            $this->info("File not exists for $full_filename, download skipped");
            Log::info("Bloomberg: file not exists for $full_filename, download skipped");

            $this->sendAlertEmail('Bloomberg File Not Found', 'Bloomberg forex index download not processing. Reason: file missing for ' . $full_filename);

        }
    }

    public function uploadFromLocal($localFilePath, $deleteLocal = true)
    {

        $remote_folder = config('integration.basepath.BLOOMBERG');

        $archiveFileService = new \App\Services\FileManagementService(config('filesystems.default'));

        $uploadedFileName = $archiveFileService->uploadFile($localFilePath, $remote_folder . basename($localFilePath));

        if (file_exists($localFilePath) && $deleteLocal) {
            unlink($localFilePath);
        }

        return $uploadedFileName;

    }

    private function sendAlertEmail($reason, $message) {

        try {

            // try to get bloomberg error notification email key if exists
            $additional_email = ConfigHelper::get(Config::BLOOMBERG_ERROR_NOTIFICATION_EMAIL);
            $email_to = explode(',', $additional_email);

            $email_to = array_filter($email_to);
            $email_to = array_map('trim', $email_to);

        } catch (\Throwable $th) {
        }

        $emailService = new EmailService;

        $body = 'This is a SPOT automated notification.<br /><br />SPOT has encountered a problem downloading forex index from Bloomberg.' . "<br /><br />" . nl2br($message);

        $emailService->sendEmail($email_to, $reason, $body);
        Log::info("Alert email sent!");

    }
}
