<?php

namespace App\Console\Commands\Maintenance;

use App\Exports\AdhocReportExport;
use App\Models\BillingDocument;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;

class GetNotMatchingInvoiceAmount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:invoice-amount { --export : export to excel at storage path }
                                                 { --date-from= : checking starts from which invoice document date }
                                                 { --type= : document type }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check Invoices for which the billing document total amount does not tally with line item total amount';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $exportToExcel = $this->option('export');
        $dateFrom = $this->option('date-from');
        $documentType = $this->option('type');

        $from = Carbon::parse($dateFrom)->startOfDay()->tz('UTC');

        $header = [
            'contract_type',
            'contract_number',
            'contract_status',
            'document_type',
            'document_date',
            'reference_number',
            'status',
            'quantity',
            'unit_price',
            'document_amount_before_tax',
            'document_tax_amount',
            'document_amount_after_tax',
            'document_amount_after_tax_and_knockoff',
            'line_item_knockoff_amount',
            'line_item_amount_before_tax',
            'line_item_amount_before_tax_without_advance_abs',
            'line_item_tax_amount',
            'line_item_amount_after_tax',
            'line_item_amount_after_tax_without_advance_abs',
            'reason',
            'inventory_location_id',
            'tax_id'
        ];

        $payload = [];

        $totalEntries = 0;

        $query = BillingDocument::where('document_date', '>=', $from)
        ->with(['lineItems', 'contractable'])
        ->whereNotNull('cxc_id')
        ->whereNull('cxc_rubber_id')
        ->where('status', '<>', 'VOIDED')
        ->where('contractable_type', '<>', 'App\Models\MonthlyContract')
        ->where('contractable_type', '<>', 'App\Models\OptionsContract')
        ->whereNotIn('type', [BillingDocument::TYPE_CREDIT_NOTE, BillingDocument::TYPE_DEBIT_NOTE]);

        if ($documentType) {
            $query->where('type', $documentType);
        }

        foreach ( $query->cursor() as $idx => $document ) {
            $this->line('Processing entry: ' . ($idx + 1));
            $totalEntries++;

            if (($reason = $this->validate($document)) !== true) {
                $this->warn($reason);
                $line_items = $document->lineItems;

                $payload[] = [
                    'contract_type' => $document->contractable_type,
                    'contract_number' => $document->contractable ? $document->contractable->contract_number : null,
                    'contract_status' => $document->contractable ? $document->contractable->status : null,
                    'document_type' => $document->type,
                    'document_date' => $document->document_date,
                    'reference_number' => $document->reference_number,
                    'status' => $document->status,
                    'quantity' => $document->lineItems->sum('quantity'),
                    'unit_price' => $document->unit_price,
                    'document_amount_before_tax' => $document->amount_before_tax,
                    'document_tax_amount' => $document->tax_amount,
                    'document_amount_after_tax' => $document->amount_after_tax,
                    'document_amount_after_tax_and_knockoff' => $document->amount_after_tax_and_knockoff,
                    'line_item_knockoff_amount' => abs($line_items->where('is_deduct_advance', true)->sum('amount_after_tax')),
                    'line_item_amount_before_tax' => $line_items->sum('amount_before_tax'),
                    'line_item_amount_before_tax_without_advance_abs' => abs($line_items->where('is_deduct_advance', false)->sum('amount_before_tax')),
                    'line_item_tax_amount' => $line_items->sum('tax_amount'),
                    'line_item_amount_after_tax' => $line_items->sum('amount_after_tax'),
                    'line_item_amount_after_tax_without_advance_abs' => abs($line_items->where('is_deduct_advance', false)->sum('amount_after_tax')),
                    'reason' => $reason,
                    'inventory_location_id' => $document->inventory_location_id,
                    'tax_id' => $document->tax_id
                ];
            }

        }

        if (count($payload) == 0) {
            $this->info('No differences found');
            exit(0);
        }

        if ($exportToExcel) {
            $now = Carbon::now()->format('YmdHis');
            Excel::store(new AdhocReportExport($payload, $header), 'non-matching-amount-invoices-' . $now . '.xlsx', 'local_root');
        } else {
            $this->table($header, $payload);
        }

        $this->warn("Total not tally: " . count($payload) . " / $totalEntries (checking condition)" );

        /*
        foreach ( $payload as $p ){

            // dont patch advance invoice first
            if ( $p['document_type'] == 'ADVANCE_INVOICE' ){
                continue;
            }

            BillingDocument::where('reference_number', $p['reference_number'])
                ->update(
                    [
                        'amount_before_tax' => $p['line_item_amount_before_tax_without_advance_abs'],
                        'amount_after_tax' => $p['line_item_amount_before_tax_without_advance_abs'] + $p['line_item_tax_amount'],
                        'tax_amount' => $p['line_item_tax_amount'],
                        'amount_after_tax_and_knockoff' => $p['line_item_amount_before_tax_without_advance_abs'] + $p['line_item_tax_amount'] - $p['line_item_knockoff_amount']
                    ]
                );

        }*/

        exit(0);
    }

    private function validate($document)
    {
        $amtBeforeTaxWithoutAdvance = abs($document->lineItems->where('is_deduct_advance', false)->sum('amount_before_tax'));
        if ( strval($document->amount_before_tax) != strval($amtBeforeTaxWithoutAdvance) ) {
            return $document->reference_number . ' amount before tax does not tally';
        }

        $amtAfterTaxWithoutAdvance = abs($document->lineItems->where('is_deduct_advance', false)->sum('amount_after_tax'));
        if ( strval($document->amount_after_tax) != strval($amtAfterTaxWithoutAdvance) ) {
            return $document->reference_number . ' amount after tax does not tally';
        }

        return true;
    }
}
