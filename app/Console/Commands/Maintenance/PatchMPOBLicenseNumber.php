<?php

namespace App\Console\Commands\Maintenance;

use App\Models\Counterparty;
use Illuminate\Console\Command;
use Rap2hpoutre\FastExcel\FastExcel;

class PatchMPOBLicenseNumber extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:mpob-license {--real} { --validate : validate if correct or wrong}';

    /**
     * The console command description.
     */

    public function handle()
    {
        $list = (new FastExcel())->import(storage_path('temp/mpob_license.xlsx'));

        $table = [];

        foreach ( $list as $item ){

            $id = $item['counterparty_id'];

            $license = trim($item['mpob_license_number']);

            if ( $license != null && strlen($license) > 0 ){

                if ($this->option('validate')) {

                    $this->info('Validating counterparty ' . $id . ' for its license number ' . $license);

                    $counterparty = Counterparty::where('id', $id)->first();

                    if ( $counterparty && $counterparty->mpob_license_number != $license ) {

                        $table[] = [
                            $counterparty->id,
                            $counterparty->name,
                            $license
                        ];
                    }
                    
                } else {

                    $this->info("Updating counterparty " . $id . " with license number " . $license);
    
                    if ( $this->option('real') ){
                        Counterparty::where('id', $id)
                            ->update(
                                ['mpob_license_number' => $license]
                            );
                    }
                }

            }

        }

        if (count($table) > 0) {
            $this->error('Below are entries for counterparties with not matching license');
            $this->table(['counterparty_id', 'counterparty_name', 'mpob_license'], $table);
        }
    }
}
