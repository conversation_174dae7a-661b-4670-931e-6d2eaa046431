<?php

namespace App\Console\Commands\Maintenance;

use App\Models\Calendar;
use App\Models\CalendarHoliday;
use App\Models\Counterparty;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Rap2hpoutre\FastExcel\FastExcel;

class PatchCalendarMaster extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:calendar-master {--real}';

    /**
     * The console command description.
     */

    public function handle()
    {
        $list = (new FastExcel())->import(storage_path('temp/calendar_master.xlsx'));

        $table = [];

        $calendars = collect([]);

        $calendarNames = collect($list)->pluck('parental_calendar')->unique()->toArray();

        if (count($calendarNames) == 0) {
            $this->error('Calendar name not given');
            exit(1);
        }

        foreach ($calendarNames as $calName) {
            $calendars->push(Calendar::firstOrCreate(
                [
                    'name' => $calName
                ],
                [
                    'valid_from' => Carbon::now()->startOfYear(),
                    'valid_to' => Carbon::now()->endOfYear(),
                ]));
        }

        foreach ( $list as $item ){

            $calendar_name = $item['parental_calendar'];
            $holiday_name = trim($item['name']);
            $date = $item['calendar_date'];
            $timezone = config('spot.local_timezone');

            if ( $holiday_name != null && strlen($holiday_name) > 0 ){

                $this->info("Updating calendar " . $calendar_name . " with holiday " . $holiday_name);
    
                if ( $this->option('real') ){

                    $calendar = $calendars->first(function($cal) use ($calendar_name) {
                        return $cal->name == $calendar_name;
                    });

                    CalendarHoliday::updateOrCreate([
                                'calendar_id' => $calendar->id,
                                'name' => $holiday_name,
                            ],
                            [
                                'date_start' => Carbon::parse($date->format('Y-m-d'), $timezone)->startOfDay()->tz('UTC') ,
                                'date_end' => Carbon::parse($date->format('Y-m-d'), $timezone)->endOfDay()->tz('UTC'),
                            ]);
                }
            }

        }

        if (count($table) > 0) {
            $this->error('Below are entries for counterparties with not matching license');
            $this->table(['counterparty_id', 'counterparty_name', 'mpob_license'], $table);
        }
    }
}
