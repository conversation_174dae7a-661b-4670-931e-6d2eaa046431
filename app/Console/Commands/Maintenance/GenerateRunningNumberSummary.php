<?php

namespace App\Console\Commands\Maintenance;

use App\Models\BillingDocument;
use App\Models\IntegrationReference;
use App\Models\LegalEntity;
use App\Services\FileManagementService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Rap2hpoutre\FastExcel\FastExcel;

class GenerateRunningNumberSummary extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'export:running-number';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Print a list of all running numbers';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $billing_documents = BillingDocument::with('contractable')
            ->get();

        $le_map = LegalEntity::get()->mapWithKeys(function($val){
            return [$val->id => $val->name];
        });

        $results = [];

        foreach ( $billing_documents as $bd ){

            $running_number = $this->getBillingDocumentRunningNumber($bd->reference_number);
            $contract = $bd->contractable;
            $le = $le_map[$contract->legal_entity_id];

            $results[] = [
                'legal_entity' => $le,
                'document_type' => $bd->type,
                'running_number' => $running_number,
                'billing_document_number' => $bd->reference_number,
                'contract_number' => $contract->contract_number,
                'status' => $bd->status,
                'contractable_type' => $bd->contractable_type,
                'document_date' => Carbon::parse($bd->document_date, 'UTC')->tz('Asia/Kuala_Lumpur')->toDateString()
            ];

        }

        (new FastExcel($results))->export('storage/running_number_summary.xlsx');
        $this->info("DONE!");
    }


    private function getBillingDocumentRunningNumber($contract_number){

        $matches = [];

        try{

            preg_match('/[\-\/]([a-zA-Z]*)([0]*)(\d+)([a-zA-Z]*)$/', $contract_number,$matches);

            return (int) $matches[3];
        }catch(\Exception $e){
            dd($contract_number, $matches);
        }

    }
}
