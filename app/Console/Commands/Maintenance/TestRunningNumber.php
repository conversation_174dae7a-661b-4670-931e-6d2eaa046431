<?php

namespace App\Console\Commands\Maintenance;

use App\Helpers\ContractNumberHelper;
use App\Models\BillingDocument;
use App\Models\Contract;
use App\Models\ContractFFBCosts;
use App\Models\ContractStatus;
use App\Models\Counterparty;
use App\Models\PhysicalContract;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Rap2hpoutre\FastExcel\FastExcel;

class TestRunningNumber extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:running-number';

    /**
     * The console command description.
     */

    public function handle()
    {
        DB::beginTransaction();

        $contract = PhysicalContract::where('id', 80728)
            ->first();

        $contract2 = PhysicalContract::where('id', 80725)
            ->first();

        for ( $i = 0; $i < 3; $i++ ){

            $contract_number = ContractNumberHelper::generateInvoiceNumber($contract, BillingDocument::TYPE_NORMAL_INVOICE);
            $this->info($contract_number);

            $contract_number = ContractNumberHelper::generateInvoiceNumber($contract2, BillingDocument::TYPE_NORMAL_INVOICE);
            $this->info($contract_number);

        }

        DB::commit();
    }
}
