<?php
namespace App\Console\Commands\Maintenance;

use App\Models\ContractFFBCosts;
use App\Models\FFBCost;
use App\Models\LongTermAgreementContract;
use App\Models\MonthlyContract;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PatchFfbCosts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:ffb-costs';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix mill margin stagerred ffb costs';

    const MILL_MARGIN_STAGGERED_ID = 25;
    const MILL_MARGIN_STANDARD_ID = 24;
    const CPO_SARAWAK_SALES_TAX_CORRECT = 30;
    const CPO_SARAWAK_SALES_TAX_WRONG = 5;
    const TRANSPORT_COST_INCENTIVES = 31;


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    private function updateSst($contracts) {

        $correct_ffb_cost = FFBCost::find(self::CPO_SARAWAK_SALES_TAX_CORRECT);

        foreach ($contracts as $contract) {

            $this->info("Patching contract " . $contract->contract_number . ", ID " . $contract->id);

            try {
                DB::beginTransaction();

                foreach ($contract->ffbCosts as $relation) {

                    // Replace number with PROBLEMATIC mill margin staggered id
                    if ($relation->ffb_cost_id == self::CPO_SARAWAK_SALES_TAX_WRONG) {

                        $relation->delete();

                        $data = [];
                        $data['contractable_type'] = $contract->getClass();
                        $data['contractable_id'] = $contract->getId();
                        $data['cost_name'] = $correct_ffb_cost->name;
                        $data['range_type'] = $correct_ffb_cost->range_type;
                        $data['calculation_operation'] = $correct_ffb_cost->calculation_operation;
                        $data['ffb_cost_id'] = $correct_ffb_cost->getId();
                        $data['description'] = $correct_ffb_cost->description;
                        $data['cost_type'] = $correct_ffb_cost->cost_type;
                        $data['value_type'] = $correct_ffb_cost->getValueType();
                        $data['cost_usage'] = $correct_ffb_cost->getCostUsage();
                        $data['price_index_id'] = $correct_ffb_cost->price_index_id;

                        $contractFFBCost = ContractFFBCosts::firstOrCreate($data);
                        $contractFFBCost->fresh();

                        $this->info("Replaced with new FFB Cost ID " . $contractFFBCost->id);

                        $contractFFBCost->conditionals()->createMany([[
                            'min' => null,
                            'max' => null,
                            'value' => 1,
                            'conditional_type' => $contractFFBCost->getClass(),
                            'conditional_id' => $contractFFBCost->id
                        ]]);


                    }
                }

                DB::commit();

            } catch (\Exception $e) {
                DB::rollBack();
                dd($e);
            }
        }
    }

    private function updateCosts($contracts)
    {

        $correct_ffb_cost = FFBCost::find(self::MILL_MARGIN_STANDARD_ID);

        foreach ($contracts as $contract) {

            $this->info("Patching contract " . $contract->contract_number . ", ID " . $contract->id);

            try {
                DB::beginTransaction();
                $conditionalValue = null;
                $can_continue_patching = false;

                foreach ($contract->ffbCosts as $relation) {

                    // Replace number with PROBLEMATIC mill margin staggered id
                    if ($relation->ffb_cost_id == self::MILL_MARGIN_STAGGERED_ID) {

                        $conditionalValue = $relation->conditionals->first()->value;
                        $is_all_conditional_value_same = true;

                        foreach ( $relation->conditionals  as $conditional ){
                            if ( $conditional->value != $conditionalValue ){
                                $is_all_conditional_value_same = false;
                                break;
                            }
                        }

                        if ( $is_all_conditional_value_same && $conditionalValue !== null ){
                            $this->info("Deleting contract FFB cost ID " . $relation->id);
                            $relation->delete();
                            $can_continue_patching = true;
                        }

                        if ( $conditionalValue === null ){
                            $this->error("CONDITIONAL VALUE IS NULL FOR " . $contract->contract_number . ", FFB COST ID " . $relation->id);
                        }

                    }
                }

                if ($can_continue_patching && $conditionalValue !== null) {
                    // replace id with the correct ffb cost id
                    $data = [];
                    $data['contractable_type'] = $contract->getClass();
                    $data['contractable_id'] = $contract->getId();
                    $data['cost_name'] = $correct_ffb_cost->name;
                    $data['range_type'] = $correct_ffb_cost->range_type;
                    $data['calculation_operation'] = $correct_ffb_cost->calculation_operation;
                    $data['ffb_cost_id'] = $correct_ffb_cost->getId();
                    $data['description'] = $correct_ffb_cost->description;
                    $data['cost_type'] = $correct_ffb_cost->cost_type;
                    $data['value_type'] = $correct_ffb_cost->getValueType();
                    $data['cost_usage'] = $correct_ffb_cost->getCostUsage();
                    $data['price_index_id'] = $correct_ffb_cost->price_index_id;

                    $contractFFBCost = ContractFFBCosts::firstOrCreate($data);
                    $contractFFBCost->fresh();

                    $this->info("Replaced with new FFB Cost ID " . $contractFFBCost->id);

                    $contractFFBCost->conditionals()->createMany([[
                        'min' => null,
                        'max' => null,
                        'value' => $conditionalValue,
                        'conditional_type' => $contractFFBCost->getClass(),
                        'conditional_id' => $contractFFBCost->id
                    ]]);
                }

                DB::commit();

            } catch (\Exception $e) {
                DB::rollBack();
                dd($e);
            }
        }
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        die(); // to prevent accidentally run

        // replace ffb_cost_id with the problematic id
        $monthlyContracts = MonthlyContract::with('ffbCosts.conditionals')
            ->where('status', '!=', 'VOIDED')
            ->wherehas('ffbCosts', function ($q) {
                $q->where('ffb_cost_id', self::CPO_SARAWAK_SALES_TAX_WRONG);
                $q->where('ffb_cost_id', '!=', self::CPO_SARAWAK_SALES_TAX_CORRECT);
            })->where('shipment_month', '>=', '2020-05-01')
            ->get();

        $this->info($monthlyContracts->count() . ' monthly contracts to be patched.');
        //$this->updateCosts($monthlyContracts);
        $this->updateSst($monthlyContracts);

        // replace ffb_cost_id with the problematic id
        $longTermAgreementContracts = LongTermAgreementContract::with('ffbCosts.conditionals')
            ->where('status', '!=', 'VOIDED')
            ->wherehas('ffbCosts', function ($q) {
                $q->where('ffb_cost_id', self::CPO_SARAWAK_SALES_TAX_WRONG);
                $q->where('ffb_cost_id', '!=', self::CPO_SARAWAK_SALES_TAX_CORRECT);
            })->get();

        $this->info($longTermAgreementContracts->count() . ' LTA to be patched.');
//        $this->updateCosts($longTermAgreementContracts);
        $this->updateSst($longTermAgreementContracts);

        // Patching CPO Sarawak set to wrong value type, should be pmt
        $this->info("FINISH!");
    }
}
