<?php

namespace App\Console\Commands\Maintenance;

use App\Models\Counterparty;
use App\Models\PricingType;
use App\Models\Product;
use App\Repositories\ProfitCenterRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PatchPngProfitCenterPricingType extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:png-profit-center-pricing-type';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch PNG Profit Center - Pricing Type';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $codes = ['outright'];

        // getting a list of pricing types
        $pricingTypes = PricingType::whereIn('code', $codes)->pluck('id');

        /** @var ProfitCenterRepository $profitCenterRepo */
        $profitCenterRepo = app()->make(ProfitCenterRepository::class);

        // getting PNG profit centers
        $profitCenters = Counterparty::isPngIntegration()->isInternal()->get();

        if ($pricingTypes->count() == 0) {
            Log::info('[Profit Center - Pricing Type] Pricing type not found..');
            exit;
        }

        Log::info('[Profit Center - Pricing Type] Patching..');

        // Attach pricing type to profit center
        foreach ($profitCenters as $profitCenter) {
            $profitCenterRepo->addPricingTypes($profitCenter, $pricingTypes);
            Log::info("[Profit Center - Pricing Type] Profit Center: " . $profitCenter->name . ', Pricing Type ID: ' . $pricingTypes );
        }

        Log::info('[Profit Center - Pricing Type] Completed..');

    }
}
