<?php
namespace App\Console\Commands\Maintenance;

use App\Models\ContractFFBCosts;
use App\Models\FFBCost;
use App\Models\LongTermAgreementContract;
use App\Models\MonthlyContract;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PatchDuplicateFfbCost extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:duplicate-ffb-costs';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix monthly contract with duplicated ffb costs';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }


    public function handle()
    {

        $result = DB::select(DB::raw('SELECT ffb_cost_id, contractable_type, contractable_id, COUNT(*) AS frequency
  FROM [spot-prd-mirror].[dbo].[contract_ffb_costs]
  WHERE cxc_id is null AND deleted_at is null
  GROUP BY ffb_cost_id, contractable_type, contractable_id
  HAVING COUNT(*) > 1'));

        $result = collect($result);

        $this->info($result->pluck('contractable_id')->unique()->values());

        foreach ( $result as $r ){

            $duplicate = ContractFFBCosts::where('ffb_cost_id', $r->ffb_cost_id)
                ->where('contractable_type', $r->contractable_type)
                ->where('contractable_id', $r->contractable_id)
                ->orderBy('id', 'DESC')
                ->first();

            $this->info("Deleting contract FFB cost ID " . $duplicate->id);
            $duplicate->delete();

        }

        $this->info("FINISH!");
    }
}
