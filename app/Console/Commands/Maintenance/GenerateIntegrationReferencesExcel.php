<?php

namespace App\Console\Commands\Maintenance;

use App\Models\IntegrationReference;
use App\Services\FileManagementService;
use Illuminate\Console\Command;
use Rap2hpoutre\FastExcel\FastExcel;

class GenerateIntegrationReferencesExcel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'export:integration-references';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Print a list of all integration references';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $integratable_types = IntegrationReference::distinct()->select('integratable_type')->get()->pluck('integratable_type');

        foreach ( $integratable_types as $type ){

            $this->info("Generating for " . $type);
            $prefix = str_replace("\\", "_", $type);
            (new FastExcel($this->integrationGenerator($type)))->export($prefix . '_integration_reference_list_' . time() . '.xlsx');

        }

    }

    function integrationGenerator($integratable_type) {

        foreach (IntegrationReference::with(['integratable', 'system'])
                     ->where('integratable_type', $integratable_type)->cursor() as $item) {
            yield [
                'system' => $item->system->name,
                'object' => $item->integratable_type,
                'object_id' => $item->integratable_id,
                'object_description' => $item->integratable->getIdentifier(),
                'type' => $item->type,
                'integration_reference' => $item->reference_no
            ];
        }
    }
}
