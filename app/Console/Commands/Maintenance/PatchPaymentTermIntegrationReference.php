<?php

namespace App\Console\Commands\Maintenance;

use App\Models\IntegrationReference;
use App\Models\PaymentTerm;
use Illuminate\Console\Command;

class PatchPaymentTermIntegrationReference extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:payment-term-integration-reference';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch payment term integration reference to use ID instead of name';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        IntegrationReference::where('integratable_type', PaymentTerm::class)
        ->where('type', 'name')
        ->update(['type' => 'id']);
    }
}
