<?php

namespace App\Console\Commands\Maintenance;

use App\Models\Contract;
use App\Models\Counterparty;
use App\Models\LegalEntity;
use App\Models\PhysicalLoad;
use Illuminate\Console\Command;
use App\Models\PhysicalContract;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PatchMinamasPNGCollectedReceivedQty extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:minamas-png-received-collected {--contract=all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "flip physical load's dispatched_received_quantity and dispatched_collected_quantity for minamas and png contract";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        // Only affecting minamas/png Upstream purchase (sales or downstream not affected)
        // should not also patch if load is already billed. check with LEFT JOIN billing_document_line_items bdli ON bdli.billable_id = loads_physical.id AND bdli.billable_type = 'App\Models\PhysicalLoad'
        $legal_entity_id = LegalEntity::where('is_minamas',true)->orWhere('is_png',true)->pluck('id');
        $profit_center_id = Counterparty::where('is_internal', true)->where('is_upstream',true)->pluck('id');

        $contract_filter = $this->option('contract');

        $list = PhysicalLoad::select(DB::raw('loads_physical.id AS id, dispatched_received_quantity, delivered_collected_quantity, contractable_id, contractable_type, master_inventory_location.is_bulking, master_incoterms.is_require_location'))
            ->join('contracts_physical', 'contracts_physical.id', '=', 'loads_physical.contractable_id')
            ->join('master_inventory_location', 'master_inventory_location.id', '=', 'loads_physical.inventory_location_id')
            ->join('master_incoterms', 'master_incoterms.id', '=', 'contracts_physical.incoterms_id')
            ->leftJoin('billing_document_line_items', function ($join) {
                $join->on('loads_physical.id', '=', 'billing_document_line_items.billable_id')
                     ->where('billing_document_line_items.billable_type', '=', 'App\Models\PhysicalLoad');
            })
            ->whereNull('billing_document_line_items.id') // physical load without billing document line item
            ->where('contracts_physical.transaction_type', Contract::TYPE_PURCHASE) // purchase contract
            ->where('loads_physical.contractable_type', PhysicalContract::class) // physical contract
            ->whereIn('contracts_physical.legal_entity_id', $legal_entity_id) // minamas/png
            ->whereIn('contracts_physical.profit_center_id', $profit_center_id) // upstream
            ->whereColumn('loads_physical.dispatched_received_quantity','<>','loads_physical.delivered_collected_quantity'); // ignore if both column same value

        if ( $contract_filter !== 'all' ){
            $list->where('contracts_physical.contract_number', $contract_filter);
        }

        $list
            ->chunk(1000, function($loads)
            {
                foreach ($loads as $load)
                {

                    // need to update invoiceable_weight and buyer_weight column as well
                    // for ex basis incoterms, invoiceable = collected, buyers weight always = invoiceable (as per updateWeightIfExBasis())
                    // for DEL incoterms, invoiceable = received, buyers weight always = received
                    // make sure behaviour is same as new BS703 behavior!

                    $dispatched_received_quantity = $load->delivered_collected_quantity;
                    $delivered_collected_quantity = $load->dispatched_received_quantity;
                    $isBulking = $load->is_bulking;

                    // is not bulking + DEL
                    if( !$isBulking && !$load->is_require_location){
                        $load->update([
                            'dispatched_received_quantity' => $dispatched_received_quantity,
                            'delivered_collected_quantity' => $delivered_collected_quantity,
                            'invoiceable_quantity' => $load->is_require_location ? $delivered_collected_quantity : $dispatched_received_quantity,
                        ]);
                    }else{
                        $load->update([
                            'dispatched_received_quantity' => $dispatched_received_quantity,
                            'delivered_collected_quantity' => $delivered_collected_quantity,
                            'invoiceable_quantity' => $load->is_require_location ? $delivered_collected_quantity : $dispatched_received_quantity,
                            'buyer_weight' => $load->is_require_location ? $delivered_collected_quantity : $dispatched_received_quantity,
                        ]);
                    }
                }
            });

    }
}
