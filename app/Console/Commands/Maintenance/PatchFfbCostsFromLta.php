<?php
namespace App\Console\Commands\Maintenance;

use App\Models\ContractAdditionalCost;
use App\Models\ContractFFBCosts;
use App\Models\FFBCost;
use App\Models\FFBCostConditioning;
use App\Models\LongTermAgreementContract;
use App\Models\MonthlyContract;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PatchFfbCostsFromLta extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:monthly-ffb-costs {--monthly-contract=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch monthly contract ffb costs by copying a fresh copy from LTA';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $monthly_contract = $this->option('monthly-contract');

        if ( $monthly_contract == null ){
            throw new \Exception('Please provide FFB monthly contract number.');
        }

        $monthly_contract = MonthlyContract::with('longTermAgreement.ffbCosts.conditionals')->where('contract_number', $monthly_contract)->first();

        if ( $monthly_contract == null ){
            throw new \Exception('Contract not found.');
        }

        // Get LTA costs and incentives
        $lta = $monthly_contract->longTermAgreement;
        $lta_costs = $lta->ffbCosts;

        $this->info("Patching costs and incentives for " . $monthly_contract->contract_number);
        $this->info("Using info from LTA " . $lta->contract_number);
        $this->info(count($lta_costs) . ' costs found.');

        DB::transaction(function() use ($lta_costs, $monthly_contract){

            // Delete existing costs for contract
            ContractFFBCosts::where('contractable_type', MonthlyContract::class)
                ->where('contractable_id', $monthly_contract->id)
                ->delete();

            // get FFB Costs & conditionals data
            foreach ($lta_costs as $ffb_cost) {

                $new_ffb_cost = $ffb_cost->toArray();
                unset($new_ffb_cost['id']);
                $new_ffb_cost['contractable_type'] = $monthly_contract->getClass();
                $new_ffb_cost['contractable_id'] = $monthly_contract->id;
                unset($new_ffb_cost['conditionals']);

                $contract_ffb_cost = ContractFFBCosts::create($new_ffb_cost);

                $this->info('Creating FFB Cost ' . $ffb_cost->cost_name);

                foreach ($ffb_cost->conditionals as $conditional) {

                    $conditional = $conditional->getAttributes();
                    unset($conditional['id']);
                    $conditional['conditional_type'] = ContractFFBCosts::class;
                    $conditional['conditional_id'] = $contract_ffb_cost->id;

                    FFBCostConditioning::insert($conditional);

                }

            }

        });


    }
}
