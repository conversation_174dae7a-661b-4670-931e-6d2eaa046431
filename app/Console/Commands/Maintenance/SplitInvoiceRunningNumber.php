<?php

namespace App\Console\Commands\Maintenance;

use App\Models\BillingDocument;
use App\Models\ContractNumber;
use App\Models\ContractType;
use Illuminate\Console\Command;

class SplitInvoiceRunningNumber extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'split:invoice-number';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Split invoice number into 2 series - 1 for external counterparties, 1 for internal billing';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $contract_type = ContractType::where('code', BillingDocument::TYPE_NORMAL_INVOICE)->first();

        // GTM
        $master_contract_numbers_gtm = ContractNumber::where('contract_type_id', $contract_type->id)
            ->whereNotNull('legal_entity_id')
            ->get();

        // Rubber
        $master_contract_numbers_rubber = ContractNumber::where('contract_type_id', $contract_type->id)
            ->whereNotNull('profit_center_id')
            ->get();

        foreach ( $master_contract_numbers_gtm as $cn ){

            // Create a new series with billing_is_internal = true
            $new_cn = $cn->replicate();
            $new_cn->is_internal = true;

            $new_cn->save();

        }

        foreach ( $master_contract_numbers_rubber as $cn ){

            // Create a new series with billing_is_internal = true
            $new_cn = $cn->replicate();
            $new_cn->is_internal = true;

            $new_cn->save();

        }

        $this->info("DONE!");

    }
}
