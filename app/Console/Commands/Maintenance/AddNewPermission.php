<?php

namespace App\Console\Commands\Maintenance;

use App\Models\Calendar;
use App\Models\CalendarHoliday;
use App\Models\Counterparty;
use App\Models\Role;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Rap2hpoutre\FastExcel\FastExcel;
use Spatie\Permission\Models\Permission;

class AddNewPermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:new-permission {--name=} {--category=}';

    /**
     * The console command description.
     */

    public function handle()
    {
        $permission_name = $this->option('name');
        $category = $this->option('category');

        if ( strlen($permission_name) == 0 || strlen($category) == 0 ){
            $this->error('Please enter permission name and category.');
            die();
        }

        $this->info('Creating new permission ' . $permission_name . ' with category ' . $category);

        $permission = Permission::firstOrCreate([
            'name' => $permission_name,
            'guard_name' => 'web',
            'category' => $category
        ]);

        // assign permission to super admin role
        $role = Role::where('name', 'super-admin')->first();
        $role->givePermissionTo($permission_name);

        $this->info('Completed!');

    }
}
