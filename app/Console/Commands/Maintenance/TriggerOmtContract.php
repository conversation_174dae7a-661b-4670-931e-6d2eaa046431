<?php

namespace App\Console\Commands\Maintenance;

use App\Models\ContractStatus;
use App\Services\Integration\Downstream\Outbound\OmtContractTrading;
use Illuminate\Console\Command;
use App\Models\PhysicalContract;
use Illuminate\Support\Facades\DB;
use App\Jobs\ProcessOutboundBusinessScenario;
use App\Services\Integration\Downstream\Outbound\OmtContract;

class TriggerOmtContract extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trigger:omt-contract {--contract= : contract number, single or separated by comma} {--only-upstream} {--only-downstream} {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manually retrigger OMT contract XML for certain contracts';
    protected $isActual;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->isActual = false;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual');

        $contract_numbers_csv = [];

        $file = storage_path('csv-source/manual_trigger_omt_contract_list.csv');
        if(file_exists($file)){
            $row = 0;
            if (($handle = fopen($file, "r")) !== false) {
                while (($data = fgetcsv($handle, null, ',')) !== false) {
                    $row ++;
                    // skip header row
                    if($row == 1){
                        continue;
                    }
                    $contract_numbers_csv[] = trim($data[0]);
                }
                fclose($handle);
            }
        }

        $contract_number_string = $this->option('contract');
        $contract_numbers = [];

        if ( strlen($contract_number_string) > 0 ) {
            $contract_numbers = explode(',', $contract_number_string);
        }

        $contract_numbers = array_merge($contract_numbers_csv, $contract_numbers);

        $contract_numbers = array_unique($contract_numbers);

        $this->info("Total ".count($contract_numbers)." contract numbers");

        foreach($contract_numbers as $contract_number){

            $contract_number = trim($contract_number);

            $contract = PhysicalContract::with(['profitCenter', 'allocations'])
                ->where('contract_number', $contract_number)
                ->first();

            $this->info("Processing contract number: ".$contract_number);

            if ( $contract == null ){
                $this->error('Invalid contract number');
                continue;
            }

            if ( $contract->getClass() != PhysicalContract::class ){
                $this->error("{$contract_number} not physical contract");
                continue;
            }

            if ( !in_array($contract->status, [ContractStatus::TYPE_CONFIRMED]) ){
                $this->error("{$contract_number} status is not confirmed");
                continue;
            }

            if ($contract->isUpstreamContract()) { // upstream

                if ( $this->option('only-downstream') ) {
                    $this->error("{$contract_number} is upstream contract, skip.");
                    continue;
                }

                if($contract->isFollowCreditLimitFlow() && $contract->isFollowS4TradingFlow()) {
                    $this->info("Creating OMT Contract Trading for " . $contract->contract_number);
                    $this->triggerOmtContract((new OmtContractTrading()), $contract);
                }else{
                    $this->error("{$contract_number} not following credit limit check flow or s4 trading flow");
                }

            }else{ // downstream

                if ( $this->option('only-upstream') ) {
                    $this->error("{$contract_number} is downstream contract, skip.");
                    continue;
                }

                $this->info("Creating OMT Contract for " . $contract->contract_number . ". Fixed price: " . $contract->getContractPrice(true, false, false));

                if ( $contract->allocations->sortBy('id')->count() > 0 && count($contract->activeAllocations) == 0 ){

                    // means got allocation done but maybe got deallocated to qty 0
                    $this->triggerOmtContract((new OmtContract()), $contract->allocations->last());

                }else{

                    if ( $contract->inventoryLocation === null ){
                        $this->error("{$contract_number} does not have inventory location set, skip.");
                        continue;
                    }

                    $this->triggerOmtContract((new OmtContract()), $contract);
                }
            }

        }

        $this->info("DONE!");
    }

    private function triggerOmtContract($businessScenario, $taggable)
    {
        if ($this->isActual) {
            ProcessOutboundBusinessScenario::dispatch($businessScenario, $taggable);
        }
    }
}
