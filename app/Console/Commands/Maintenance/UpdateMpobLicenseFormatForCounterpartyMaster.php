<?php

namespace App\Console\Commands\Maintenance;

use App\Models\Counterparty;
use App\Models\LegalEntity;
use App\Models\MasterLicensableMpobLicenses;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateMpobLicenseFormatForCounterpartyMaster extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:mpob-license-new-format';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Existing Master Counterparty and Legal Entity Mpob License from one to one to one to many relationship';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Begin Updating MPOB Licenses data for Licensable Entities');

        $licenseable_entities = [
            new Counterparty(),
            new LegalEntity()
        ];

        foreach ($licenseable_entities as $licenseable) {

            $entity_with_mpob_licenses = $licenseable::whereNotNull('mpob_license_number')->get();

            if ($entity_with_mpob_licenses->isNotEmpty()) {
                $this->info('Updating ' . $licenseable->getTable());

                DB::transaction(function() use ($entity_with_mpob_licenses, $licenseable){

                    foreach ($entity_with_mpob_licenses as $entity) {

                        $this->info('Updating MPOB Licenses data for ' . $entity->name);

                        $data = [
                            'licensable_id' => $entity->id,
                            'licensable_type' => get_class($entity),
                            'mpob_license_number' => $entity->mpob_license_number
                        ];

                        MasterLicensableMpobLicenses::where(function ($q) use ($entity) {
                            $q->where('licensable_id', $entity->id)
                                ->where('licensable_type', get_class($entity));
                        })->delete();

                        MasterLicensableMpobLicenses::insert($data);

                        /**
                         * Remove the MPOB License Column data
                         */
                        $this->info('Removing mpob_license_number Column data from ' . $licenseable->getTable());
                        //$entity->mpob_license_number = null;
                        //$entity->save();
                    }

                }, 3);

            }

            $this->info('End Updating MPOB Licenses data for Licenseable Entities');
        }
        return;
    }
}
