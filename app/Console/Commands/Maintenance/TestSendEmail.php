<?php

namespace App\Console\Commands\Maintenance;

use App\Services\EmailService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class TestSendEmail extends Command
{

    protected $signature = 'maintenance:smtp {--to=}';

    protected $description = 'Test SMTP working or not';

    protected $errors = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $recipient = $this->option('to');

        if ( $recipient == null ){
            $recipient = '<EMAIL>';
        }

        $this->info("SENDING EMAIL..");

        $emailService = new EmailService();
        $emailService->sendEmail(
            $recipient,
            'Test SMTP Working',
            Carbon::now()->toIso8601String() . '<br /><br />If you receive this email, it means that SM<PERSON> is working.');

        $this->info("TEST EMAIL SENT TO " . $recipient);

    }

}
