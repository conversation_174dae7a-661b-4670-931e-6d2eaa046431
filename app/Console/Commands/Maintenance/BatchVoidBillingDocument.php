<?php

namespace App\Console\Commands\Maintenance;

use App\Factories\BillingDocumentFactory;
use App\Helpers\DispatchAuditTrailJobParams;
use App\Interfaces\IAuditTrailActions;
use App\Models\ActionRemarks;
use App\Models\AuditTrail;
use App\Models\BillingDocument;
use App\Repositories\BillingDocumentRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Rap2hpoutre\FastExcel\FastExcel;

class BatchVoidBillingDocument extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'batch:void-billing {--remarks=} {--mock}';

    /**
     * The console command description.
     */

    public function handle()
    {

        $list = (new FastExcel)->import(storage_path('billing_document_to_be_voided.xlsx'));
        $billingDocumentRepository = new BillingDocumentRepository();
        $iAuditTrailActions = app()->make(IAuditTrailActions::class);

        $remarks = $this->option('remarks') ?? 'Voided upon user request using batch voiding tool.';
        $is_mock = $this->option('mock');

        if ( $is_mock ){
            $this->info("THIS IS MOCK MODE. NO ACTUAL CHANGES WILL BE DONE.");
        }

        $this->info(count($list) . ' billing documents to be voided.');

        $user = Auth::loginUsingId(1);

        try{

            foreach ( $list as $l ){

                $number = $l['billing_document_number'];

                if ( strlen($number) == 0 ){
                    continue;
                }

                $this->info("VOIDING BILLING DOCUMENT: " . $number);

                $invoice = $billingDocumentRepository->getInvoices(['reference_number' => $number], false, false, ['contractable', 'lineItems']);

                if ( $invoice == null ){
                    $this->warn('Unable to find invoice with number ' . $number);
                    continue;
                }
                if ( $invoice->status == BillingDocument::INVOICE_STATUS_VOIDED ){
                    $this->warn('Billing document already voided, skip.');
                    continue;
                }

                DB::transaction(function() use ($invoice, $billingDocumentRepository, $iAuditTrailActions, $remarks, $is_mock){

                    $old_data = clone $invoice;

                    $billing_document = BillingDocumentFactory::initFromModel($invoice);

                    $billing_document->validateStatusChange(BillingDocument::INVOICE_STATUS_VOIDED);

                    if ( !$is_mock ){

                        $this->addRemarks(ActionRemarks::TYPE_VOID_BILLING_DOCUMENT, $invoice, $remarks);

                        $billing_document
                            ->unstageFromPosted()
                            ->updateStatus(BillingDocument::INVOICE_STATUS_VOIDED);

                        $this->addAuditTrail($iAuditTrailActions, $invoice, $old_data);

                    }

                    $this->info("SUCCESS");

                }, 3);


            }

        }catch(\Exception $e){
            $this->error($e->getMessage());
        }

    }

    private function addRemarks($type, $model, $remarks) {

        $action_remarks = new ActionRemarks();
        $action_remarks->fill([
            'type' => $type,
            'remarks' => $remarks,
            'created_by' => 1,
        ]);

        $model->actionRemarks()->save($action_remarks);
        return $model;

    }

    private function addAuditTrail($iAuditTrailActions, $invoice, $old_data) {

        $iAuditTrailActions->dispatchJob(
            new DispatchAuditTrailJobParams(
                AuditTrail::CONTRACT_ACTIONS['STATUS']['INVOICE'],
                trans(
                    'audittrail.description.' . AuditTrail::CONTRACT_ACTIONS['STATUS']['INVOICE'],
                    ['document_type' => str_replace("_", " ", $invoice->type), 'reference_number' => $invoice->reference_number, 'new_status' => BillingDocument::INVOICE_STATUS_VOIDED]
                ),
                $invoice->contractable->id,
                get_class($invoice->contractable),
                $old_data->toArray(),
                $invoice->id,
                get_class($invoice)
            )
        );

    }
}
