<?php

namespace App\Console\Commands\Maintenance;

use App\Models\BusinessUnit;
use App\Models\PhysicalContract;
use App\Models\PhysicalLoad;
use Illuminate\Console\Command;

class PatchMinamasFOBLoadsQty extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:minamas-fob-loads-qty {--real}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Copy the dispatched quantity to delivered quantity and invoiceable quantity for Minamas FOB contract only';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // query
        // select lp.contract_number, lp.cxc_mm_id, lp.dispatched_received_quantity, lp.delivered_collected_quantity, lp.invoiceable_quantity
        // from loads_physical lp
        // left join contracts_physical cp on cp.contract_number = lp.contract_number
        // left join master_legal_entity mle on mle.id = cp.legal_entity_id
        // where mle.business_unit_id = 2 and cp.incoterms = 'FOB'
        // and dispatched_received_quantity > 0 and (delivered_collected_quantity = 0 or invoiceable_quantity = 0)

        $minamas = BusinessUnit::where('name', 'Minamas')->first();
        if (!$minamas) {
            throw new \Exception('Minamas business unit not found');
        }

        $loads = PhysicalLoad::whereHasMorph('contractable', [PhysicalContract::class], function ($q) use ($minamas) {
            $q->where('incoterms', 'FOB'); // get FOB contracts
            $q->whereHas('legalEntity', function ($q) use ($minamas) {
                $q->where('business_unit_id', $minamas->id); // get Minamas contracts
            });
        })->where('contractable_type', PhysicalContract::class)
            ->where('dispatched_received_quantity', '>', 0)
            ->where(function ($q) {
                $q->where('delivered_collected_quantity', 0);
                $q->orWhere('invoiceable_quantity', 0);
            })
            ->get();

        $this->info(sprintf('========Total: %s========', $loads->count()));

        $total_updated = 0;
        foreach ($loads as $load) {
            $this->info('Load: ' . $load->reference_no);
            if ($load->delivered_collected_quantity == 0) {
                $load->delivered_collected_quantity = $load->dispatched_received_quantity;
            }
            if ($load->invoiceable_quantity == 0) {
                $load->invoiceable_quantity = $load->dispatched_received_quantity;
            }
            if ($this->option('real')) {
                $load->save();
            }
            ++$total_updated;
        }
        $this->info(sprintf('========Total updated: %s========', $total_updated));
    }
}
