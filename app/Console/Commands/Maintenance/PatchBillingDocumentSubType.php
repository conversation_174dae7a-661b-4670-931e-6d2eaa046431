<?php

namespace App\Console\Commands\Maintenance;

use App\Helpers\DatabaseHelper;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Counterparty;
use App\Models\PhysicalContract;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Rap2hpoutre\FastExcel\FastExcel;

class PatchBillingDocumentSubType extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:billing-subtype';

    /**
     * The console command description.
     */

    public function handle()
    {

        $list = BillingDocument::select(DB::raw('billing_documents.id AS id, reference_number, contractable_type, contractable_id, description'))->whereIn('type', ['CREDIT_NOTE', 'DEBIT_NOTE'])
            ->join('billing_document_line_items', 'billing_documents.id', '=', 'billing_document_line_items.billing_document_id')
            ->join('contracts_physical', 'contracts_physical.id', '=', 'billing_documents.contractable_id')
            ->where('billing_documents.contractable_type', PhysicalContract::class)
            ->whereIn('pricing_type', ['provisional', 'mpob'])
            ->where(function($q){
                $q->whereRaw('UPPER(billing_document_line_items.description) like \'%FIXED PRICE%\'')
                    ->orWhereRaw('UPPER(billing_document_line_items.description) like \'%PRICE ADJUSTMENT%\'')
                    ->orWhereRaw('UPPER(billing_document_line_items.description) like \'%PRICE FIXED%\'')
                    ->orWhereRaw('UPPER(billing_document_line_items.description) like \'%PRICE FIXING%\'')
                    ->orWhereRaw('UPPER(billing_document_line_items.description) like \'%FINAL INVOICE%\'')
                    ->orWhereRaw('UPPER(billing_documents.remarks) like \'%PRICE ADJUSTMENT%\'');
            })
            ->whereNull('sub_type')
            ->get();

        $temp = [];
        $refs = $list->pluck('reference_number')->unique();

        foreach ( $refs as $l ){
            $temp[] = [
                'invoice_number' => $l
            ];
        }

        (new FastExcel($temp))->export(storage_path('patchsubtype.xlsx'));

        $list = $list->pluck('id')->unique();

        foreach ( $list->chunk(2000) as $sublist ){

            BillingDocument::whereIn('id', $sublist->toArray())
                ->update(['sub_type' => 'PRICE_FIXING']);

        }

        $this->info("UPDATED " . count($list) . ' BILLING DOCUMENTS TO PRICE_FIXING SUBTYPE');

    }
}
