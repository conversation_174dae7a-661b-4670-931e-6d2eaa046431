<?php

namespace App\Console\Commands\Maintenance;

use App\Models\Counterparty;
use App\Models\Product;
use App\Repositories\ProfitCenterRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PatchPngProfitCenterProduct extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:png-profit-center-product';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch PNG Profit Center - Product';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $codes = [ 'CPO', 'CPKO', 'PKE', 'RBDPO', 'RBDOLN', 'PFAD', 'RBDSTN', 'RBDSO', 'PMF'];

        // getting a list of products
        $products = Product::whereIn('code', $codes)->pluck('id');

        /** @var ProfitCenterRepository $profitCenterRepo */
        $profitCenterRepo = app()->make(ProfitCenterRepository::class);

        // getting PNG profit centers
        $profitCenters = Counterparty::isPngIntegration()->isInternal()->get();

        if ($products->count() == 0) {
            Log::info('[Profit Center - Product] Product not found..');
            exit;
        }

        Log::info('[Profit Center - Product] Patching..');

        // Attach product to profit center
        foreach ($profitCenters as $profitCenter) {
            $profitCenterRepo->addProducts($profitCenter, $products);
            Log::info("[Profit Center - Product] Profit Center: " . $profitCenter->name . ', Product ID: ' . $products );
        }

        Log::info('[Profit Center - Product] Completed..');

    }
}
