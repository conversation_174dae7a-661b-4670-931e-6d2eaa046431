<?php

namespace App\Console\Commands\Maintenance;

use App\Models\ContractFFBCosts;
use App\Models\Counterparty;
use Illuminate\Console\Command;
use Rap2hpoutre\FastExcel\FastExcel;

class TestStaggeredFFB extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:staggered {--target=}';

    /**
     * The console command description.
     */

    public function handle()
    {
        $balance_to_calculate = $this->option('target');
        $target_value = $this->option('target');
        $value = 0;

        $ffb_cost = ContractFFBCosts::with('conditionals')->find(28283);

        $this->info("FFB COST: " . $ffb_cost->cost_name);
        $this->info("TARGET VALUE: " . $target_value);


        foreach ($ffb_cost->conditionals as $condition) {

            if ($target_value > $condition->max && $condition->max !== 0 ) {

                $value += $condition->value * ($condition->max - $condition->min);
                $balance_to_calculate -= abs($condition->max - $condition->min);

            } else {
                $value += $balance_to_calculate * $condition->value;

                if ( $condition->max == 0 ){
                    $balance_to_calculate = 0;
                }else{
                    $balance_to_calculate -= abs($condition->max - $condition->min);
                }
            }

            $this->info("CONDITION: " . $condition->min . " to " . $condition->max . " = " . $condition->value . '. VALUE = ' . $value);

            if ( $balance_to_calculate <= 0 ){
                break;
            }

        }

        if ( $target_value != 0 ){
            $value = $value / $target_value;
        }

        $this->info("VALUE: " . $value);

    }
}
