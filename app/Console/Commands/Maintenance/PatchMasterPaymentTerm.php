<?php

namespace App\Console\Commands\Maintenance;

use App\Helpers\IntegrationReferenceHelper;
use App\Models\ExternalSystem;
use App\Models\PaymentTerm;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Rap2hpoutre\FastExcel\FastExcel;

/**
 * Class PatchMasterPaymentTerm
 * @package App\Console\Commands\Maintenance
 */
class PatchMasterPaymentTerm extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:master-payment-term {--confirmed}';

    /**
     * The console command description.
     */

    public function handle()
    {
        $confirmed = $this->option('confirmed');
        $uploaded_list = (new FastExcel)->import(storage_path('20201214_master_payment_terms_update.xlsx'));
        $uploaded_list = $uploaded_list->keyBy('id');
        $payment_terms = PaymentTerm::with(['integrate'])->get()->keyBy('id');
        $master_sap_system = ExternalSystem::code(ExternalSystem::SAP)->first();

        DB::beginTransaction();
        try {
            $attributes = ['name', 'contract_description', 'invoice_description', 'is_active', 'due_date_days'];
            foreach ($payment_terms as $payment_term) {
                $excel_data = $uploaded_list[$payment_term->id] ?? null;
                if (!$excel_data) {
                    continue;
                }
                $sap_system = $payment_term->integrate->where('external_system_id', $master_sap_system->id)->first();
                $is_updateable = false;

                foreach ($attributes as $attribute) {
                    if (empty($excel_data[$attribute]) && empty($payment_term->$attribute)) {
                        continue;
                    }

                    // some existing contract_description can next line
                    if (strpos($payment_term->$attribute, "\n") !== false) {
                        continue;
                    }
                    // check data is same or need to update
                    if (trim($excel_data[$attribute]) != trim($payment_term->$attribute)) {
                        $is_updateable = true;
                        $payment_term->$attribute = $excel_data[$attribute];
                        $this->info(sprintf('[%s %s - %s] %s', $payment_term->id, $payment_term->name, $attribute, $payment_term->$attribute));
                    }
                }

                if (!$sap_system && !empty($excel_data['SAP_INTEGRATION_REFERENCE'])) {
                    $this->info(sprintf('INSERT [%s - SAP] %s', $payment_term->id, $excel_data['SAP_INTEGRATION_REFERENCE']));
                    if ($confirmed) {
                        IntegrationReferenceHelper::insertOrUpdate('SAP', PaymentTerm::class, 'id', $payment_term->id, $excel_data['SAP_INTEGRATION_REFERENCE']);
                    }
                }

                if ($is_updateable && $confirmed) {
                    $payment_term->save();
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }

        if (!$confirmed) {
            $this->info('THIS IS MOCK MODE. NO ACTUAL CHANGES WILL BE DONE.');
        } else {
            $this->info('Completed!');
        }
    }
}
