<?php

namespace App\Console\Commands\Maintenance;

use App\Models\Calendar;
use App\Models\CalendarHoliday;
use App\Models\Counterparty;
use App\Models\User;
use App\Models\UserCalendar;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Rap2hpoutre\FastExcel\FastExcel;

class PatchSetCalendarToUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:set-calendar-users';

    /**
     * The console command description.
     */

    public function handle()
    {
        $users = User::all();

        $calendar1 = Calendar::where('id', 5)->first();
        $calendar2 = Calendar::where('id', 8)->first();

        $this->info("Calendar 1: " . $calendar1->name);
        $this->info("Calendar 2: " . $calendar2->name);

        $data = [];

        $this->info("Patching users calendar...");

        foreach ( $users as $user ){
            $data[] = [
                'user_id' => $user->id,
                'calendar_id' => $calendar1->id
            ];
            $data[] = [
                'user_id' => $user->id,
                'calendar_id' => $calendar2->id
            ];
        }

        foreach ( collect($data)->chunk(1000) as $chunk ){
            UserCalendar::insert($chunk->toArray());
        }

        $this->info("DONE!");


    }
}
