<?php

namespace App\Console\Commands\Maintenance;

use App\Models\IntegrationSettings;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;

class PatchIntegrationSettingLocalPath extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:integration-setting-local-path {--dry-run : Dry run insertion}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch integration setting to insert local path if empty';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if (!Schema::hasColumn('integration_settings', 'local_path')) {
            $this->error('Table integration_settings does not have local_path column. Please run migration');
            exit(1);
        }

        $integration_settings = IntegrationSettings::whereNull('local_path')->where('bound', 'outbound')->get();

        if ($integration_settings->count() == 0) {
            $this->warn('No integration_settings entry found with null local_path');
        }

        foreach ($integration_settings as $integration_setting) {
            $this->saveLocalPathToIntegrationSetting($integration_setting);
        }
    }

    private function saveLocalPathToIntegrationSetting(IntegrationSettings $integration_setting)
    {
        $default_upstream_path = config('integration.localpath.upstream');
        $default_downstream_path = config('integration.localpath.downstream');
        $minamas_upstream_path = config('integration.localpath.upstream_minamas');
        $minamas_downstream_path = config('integration.localpath.downstream_minamas');

        $mappings = [
            $default_upstream_path => [['GTM', 'GTM_FI_UPSTREAM', 'SDR_FI_UPSTREAM', 'SDR', 'SDOTPL_FI_UPSTREAM', 'MP_FI_UPSTREAM'], 'upstream'],
            $default_downstream_path => [['GTM', 'SDR'], 'downstream'],
            $minamas_upstream_path => [['MP'], 'upstream'],
            $minamas_downstream_path => [['MP'], 'downstream'],
        ];

        $DOMAINS = 0;
        $DOWNSTREAM_OR_UPSTREAM = 1;

        // domain , upstream/downstream

        foreach ($mappings as $local_path => $mapping) {

            if (!realpath($local_path)) {

                $this->warn('Path: ' . $local_path . ' does not exists. Trying to mkdir');

                if (!$this->option('dry-run')) {
                    mkdir($local_path, 0777, true);
                    $this->info('Path: ' . $local_path . ' created');
                }    
            }

            if ( $mapping[$DOWNSTREAM_OR_UPSTREAM] != $integration_setting->stream ) {
                continue;
            }

            if ( in_array($integration_setting->domain[0], $mapping[$DOMAINS]) ) {
                $this->info('Integration Setting :' . $integration_setting->code . " | local_path : " . $local_path);
                if (!$this->option('dry-run')) {
                    $integration_setting->local_path = $local_path;
                    $integration_setting->save();
                }
            }

        }
    }
}
