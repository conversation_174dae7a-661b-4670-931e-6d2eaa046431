<?php

namespace App\Console\Commands\Maintenance;

use App\Models\PhysicalLoad;
use Illuminate\Console\Command;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class PatchLoadsPhysicalSystemReference extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:loads-physical-system-reference';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Loads Physical patch system_reference';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        // SQL QUERY TO SET system_reference
        /**
             UPDATE loads_physical
                SET system_reference = (reference_no + '_' + contract_number +
                CASE
                WHEN cxc_header_id is not null THEN '_' + cxc_header_id
                ELSE '' END
                +
                CASE
                WHEN cxc_detail_id is not null THEN '_' + cxc_detail_id
                ELSE '' END
                )
         */

		// check if duplicate entries are there
        $duplicatedEntry = PhysicalLoad::selectRaw('reference_no, contract_number, cxc_header_id, cxc_detail_id, count(*)')
            ->groupBy('reference_no', 'contract_number', 'cxc_header_id', 'cxc_detail_id')
            ->having(DB::raw('count(*)'), '>', 1)
            ->count();

        $duplicatedReference = PhysicalLoad::selectRaw('system_reference, count(*)')
            ->groupBy('system_reference')
            ->having(DB::raw('count(*)'), '>', 1)
            ->count();

        if ($duplicatedEntry > 0) {
            $this->warn('Duplicated entries exists');
			exit(0);
        }

        if ($duplicatedReference > 0) {
            $this->error('System Reference exists duplicated entry');
			exit(0);
        }

        $this->info('All in order');

        $this->line('Begin alter table make column unique');

        DB::statement('ALTER TABLE loads_physical ADD UNIQUE (system_reference)');

        $this->info('Table altered');
    }
}
