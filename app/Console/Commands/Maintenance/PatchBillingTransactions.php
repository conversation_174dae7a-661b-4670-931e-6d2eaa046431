<?php
namespace App\Console\Commands\Maintenance;
use App\Models\BillingDocument;
use App\Models\BillingDocumentTransaction;
use App\Models\Contract;
use App\Models\ContractStatus;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PatchBillingTransactions extends Command{

    protected $signature = 'patch:billing-transactions {--contract-number=}';

    protected $description = 'Patch and recalculate billing document transactions.';

    public function handle() {

        $contract_number = $this->option('contract-number');

        if ( $contract_number == null ){
            throw new \Exception('Contract number is required.');
        }

        // find contract
        $contract = Contract::with('contractable')->where('contract_number', $contract_number)->first();

        if ( $contract == null ){
            throw new \Exception('Contract not found.');
        }

        $this->info("Processing {$contract->contract_number}");

        $contract = $contract->contractable;
        $contract->load('billingDocuments');

        try{
            DB::beginTransaction();

            BillingDocumentTransaction::where('contractable_type', get_class($contract))
                ->where('contractable_id', $contract->id)
                ->forceDelete();

            $inserts = $this->processContracts([$contract]);

            if (count($inserts) > 0) {
                $count = count($inserts);
                $this->info("Inserting $count rows... ");

                foreach (collect($inserts)->chunk(250) as $chunk) {
                    BillingDocumentTransaction::insert($chunk->toArray());
                }
            }

            DB::commit();
        }catch(\Exception $e){
            DB::rollBack();
            throw $e;
        }


    }

    private function processContracts($chunks) {

        $inserts = [];

        foreach ( $chunks as $contract ){

            if ( count($contract->billingDocuments) == 0 ){
                Log::info("Skipping contract {$contract->contract_number} as there are no billing documents.");
                continue;
            }

            Log::info("Processing contract " . $contract->contract_number);

            $billing_documents = $contract->billingDocuments->sortBy('created_at');

            // If billing documents dont have any advance invoice, skip contract
            if ( $billing_documents->where('type', BillingDocument::TYPE_ADVANCE_INVOICE )->count() == 0 ){
                continue;
            }

            $advance_balance = 0;
            $advances = collect();

            foreach ( $billing_documents as $billing_document ){

                // If is advance invoice
                if ( $billing_document->type == BillingDocument::TYPE_ADVANCE_INVOICE || $billing_document->type == BillingDocument::TYPE_ADVANCE_VOUCHER ){

                    // if voided or draft, ignore
                    if ( !in_array($billing_document->status, BillingDocument::INVOICE_STATUSES_EFFECTIVE) ){
                        continue;
                    }

                    $inserts[] = [
                        'contractable_type' => get_class($contract),
                        'contractable_id' => $contract->id,
                        'billing_document_id' => $billing_document->id,
                        'checked_out_with_id' => null,
                        'type' => BillingDocumentTransaction::TYPE_ADD_ADVANCE,
                        'amount' => round($billing_document->amount_after_tax * 100, 2),
                        'created_at' => $billing_document->document_date,
                        'updated_at' => $billing_document->document_date
                    ];

                    $advances->push([
                        'balance' => $billing_document->amount_after_tax,
                        'billing_document' => $billing_document
                    ]);

                    echo 'Adding advance ' . number_format($billing_document->amount_after_tax, 2) . ' for ' . $billing_document->reference_number . "\n";

                    $advance_balance += $billing_document->amount_after_tax;

                }else if ( $billing_document->type == BillingDocument::TYPE_ADVANCE_CREDIT_NOTE ||
                    $billing_document->type == BillingDocument::TYPE_NORMAL_INVOICE ||
                    $billing_document->type == BillingDocument::TYPE_VOUCHER ){

                    // if voided, ignore. Advance CN already offset at draft status
                    if ( $billing_document->status == BillingDocument::INVOICE_STATUS_VOIDED ){
                        continue;
                    }

                    $advance_balance -= $billing_document->amount_after_tax;
                    $advance_required = $billing_document->amount_after_tax;

                    // determine offset with
                    if ( $advances->count() > 0 ){

                        echo "INVOICE: " . $billing_document->reference_number . "\n";

                        while ( $advances->count() > 0 && $advance_required > 0 ){

                            $x = $advances->shift();

                            $offset_with = $x['billing_document'];
                            $offset_amount = 0;

                            echo 'Current advance: ' . $offset_with->reference_number . ', ' . number_format($x['balance'], 2) . "\n";

                            if ( $x['balance'] > $advance_required ){
                                $offset_amount = $advance_required;
                                $x['balance'] -= $advance_required;
                            }else{
                                $offset_amount = $x['balance'];
                                $x['balance'] = 0;
                            }

                            echo 'Offset with advance ' . $offset_with->reference_number . ' amount ' . number_format($offset_amount, 2) .
                                ', balance ' . number_format($x['balance'], 2) . "\n";

                            $inserts[] = [
                                'contractable_type' => get_class($contract),
                                'contractable_id' => $contract->id,
                                'billing_document_id' => $billing_document->id,
                                'checked_out_with_id' => $offset_with->id,
                                'type' => BillingDocumentTransaction::TYPE_REDUCE_ADVANCE,
                                'amount' => round($offset_amount * 100, 2),
                                'created_at' => $billing_document->document_date,
                                'updated_at' => $billing_document->document_date
                            ];

                            $advance_required -= $offset_amount;

                            if ( $x['balance'] > 0 ){
                                $advances->prepend($x);
                            }

                        }

                    }

                }

            }

        }

        return $inserts;

    }

}
