<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {

        $schedule->command('test:alive')
            ->timezone('Asia/Kuala_Lumpur')
            ->everyMinute();

        $schedule->command('integration:run --cron --stream=upstream --bound=inbound')
            ->timezone('Asia/Kuala_Lumpur')
            ->everyMinute()
            ->runInBackground()
            ->withoutOverlapping(10);

        $schedule->command('integration:run --cron --stream=upstream_png --bound=inbound')
            ->timezone('Asia/Kuala_Lumpur')
            ->everyMinute()
            ->runInBackground()
            ->withoutOverlapping(10);

        $schedule->command('integration:run --cron --stream=upstream --bound=outbound')
            ->timezone('Asia/Kuala_Lumpur')
            ->everyMinute()
            ->runInBackground()
            ->withoutOverlapping(10);

        $schedule->command('integration:run --cron --stream=upstream_png --bound=outbound')
            ->timezone('Asia/Kuala_Lumpur')
            ->everyMinute()
            ->runInBackground()
            ->withoutOverlapping(10);

        $schedule->command('integration:run --cron --stream=downstream --bound=inbound')
            ->timezone('Asia/Kuala_Lumpur')
            ->everyMinute()
            ->runInBackground()
            ->withoutOverlapping(10);

        $schedule->command('integration:run --cron --stream=downstream --bound=outbound')
            ->timezone('Asia/Kuala_Lumpur')
            ->everyMinute()
            ->runInBackground()
            ->withoutOverlapping(10);

        $schedule->command('upload')
            ->timezone('Asia/Kuala_Lumpur')
            ->everyMinute()
            ->withoutOverlapping(10);

        /*$schedule->command('generate:contract-summary')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('06:00')
            ->withoutOverlapping(10);*/

        $schedule->command('options:trigger-expire')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('00:05')
            ->withoutOverlapping(10);

        $schedule->command('options:trigger-expiry-notification 5')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('00:30')
            ->withoutOverlapping(10);

        $schedule->command('generate:contract-pricing')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('11:00')
            ->withoutOverlapping(10);

        $schedule->command('generate:outstanding-contract')
            ->timezone('Asia/Kuala_Lumpur')
            ->monthlyOn(1, '00:00')
            ->withoutOverlapping(10);

        $schedule->command('system:cleanup')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('05:00')
            ->withoutOverlapping(10);

        $schedule->command('fulfillment:run')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('03:00')
            ->withoutOverlapping(10);

        $schedule->command('integrate:bloomberg-forex')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('10:00')
            ->withoutOverlapping(10);

        $schedule->command('coru:outstanding-contracts')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('06:00')
            ->withoutOverlapping(10);

        $schedule->command('coru:loads-not-invoice 24')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('20:00')
            ->withoutOverlapping(10);

        $schedule->command('tradesparent:export')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('12:30')
            ->withoutOverlapping(10);

        // 12 am LCT
        // only for certain LE and PC. Need to update PhysicalContractService checkLctExportEligible() if any changes to LE/PC limitation.
        $schedule->command('lct:export-data --upload --status=confirmed --transaction-type=S --limit-le=S --limit-le=T --limit-le=B --limit-pc-in-le=KB --limit-le=D --limit-le=I')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('00:00')
            ->withoutOverlapping(10);

        $schedule->command('lct:export-data --upload --status=voided --transaction-type=S --limit-le=S --limit-le=T --limit-le=B --limit-pc-in-le=KB --limit-le=D --limit-le=I')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('00:00')
            ->withoutOverlapping(10);

        // 12 pm LCT
        $schedule->command('lct:export-data --upload --status=confirmed --transaction-type=S --limit-le=S --limit-le=T --limit-le=B --limit-pc-in-le=KB --limit-le=D --limit-le=I')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('12:00')
            ->withoutOverlapping(10);

        $schedule->command('lct:export-data --upload --status=voided --transaction-type=S --limit-le=S --limit-le=T --limit-le=B --limit-pc-in-le=KB --limit-le=D --limit-le=I')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('12:00')
            ->withoutOverlapping(10);

        /*$schedule->command('lct:export-data --upload --from=2021-01-01 --until=now --status=confirmed-only --transaction-type=S --limit-le=S --limit-le=T --limit-le=B --limit-pc-in-le=KB')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('12:50')
            ->withoutOverlapping(10);*/

        /*$schedule->command('trading-loads:sync')
            ->timezone('Asia/Kuala_Lumpur')
            ->dailyAt('06:00')
            ->withoutOverlapping();*/

        $schedule->command('housekeeping:auto-closeout-session')
            ->timezone('Asia/Kuala_Lumpur')
            ->daily()
            ->withoutOverlapping(10);

        $schedule->command('update:auto-closeout-session')
            ->everyMinute()
            ->withoutOverlapping(10);
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
