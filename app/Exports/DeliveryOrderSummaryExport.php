<?php


namespace App\Exports;


use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class DeliveryOrderSummaryExport implements FromArray, WithHeadings, WithStrictNullComparison
{
    use Exportable;

    private $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    function array(): array
    {
        $data = [];

        foreach ($this->data as $d) {

            $data[] = [
                $d['product'],
                $d['contract_number'],
                $d['contract_currency'],
                $d['contract_date'],
                $d['contract_status'],
                $d['incoterms'],
                $d['contract_type'],
                $d['counterparty'],
                $d['counterparty_type'],
                $d['inventory_location'],
                $d['transport_mode'],
                $d['legal_entity'],
                $d['local_international'],
                $d['packing_unit'],
                $d['shipment_month'],
                $d['deliver_to_receive_from_code'],
                $d['trader'],
                $d['transport_mode'],
                $d['delivery_order_number'],
                $d['do_in_system'],
                $d['do_status'],
                $d['contract_quantity'],
                $d['contract_quantity_uom'],
                $d['do_quantity'],
                $d['do_quantity_uom'],
                $d['dispatched_received_quantity'],
                $d['scheduled_quantity'],
                $d['delivered_collected_quantity'],
                $d['fulfilled_quantity'],
                $d['do_quantity'] !== null ? ($d['do_quantity'] - $d['fulfilled_quantity']) : '-',
                $d['do_quantity'] !== null ? round(($d['fulfilled_quantity'] / $d['do_quantity']) * 100, 2) : '-',
            ];
        }
        return $data;
    }

    public function headings(): array
    {

        return [
            'PRODUCT CODE',
            'CONTRACT NUMBER',
            'CURRENCY',
            'CONTRACT DATE',
            'CONTRACT STATUS',
            'INCOTERMS',
            'CONTRACT TYPE',
            'COUNTERPARTY',
            'COUNTERPARTY TYPE',
            'STOCK LOCATION',
            'MODE OF TRANSPORT',
            'LEGAL ENTITY',
            'LOCAL/INTERNATIONAL',
            'PACKING UNIT',
            'SHIPMENT MONTH',
            'DELIVER TO/RECEIVE FROM',
            'TRADER',
            'TRANSPORT MODE',
            'DO NUMBER',
            'DO IN SPOT?',
            'DO STATUS',
            'CONTRACT QTY',
            'CONTRACT QTY UOM',
            'DO QTY',
            'DO QTY UOM',
            'DISPATCHED/RECEIVED QTY',
            'SCHEDULED QTY',
            'DELIVERED/COLLECTED QTY',
            'FULFILLED QTY',
            'FULFILLED - DO QTY',
            'DO COMPLETION %'
        ];
    }
}
