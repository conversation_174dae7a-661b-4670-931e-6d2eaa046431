<?php

namespace App\Exports;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class BankDraftReportExport extends DefaultValueBinder implements WithCustomValueBinder, FromCollection, WithHeadings, WithStrictNullComparison, WithColumnFormatting, WithMapping, ShouldAutoSize
{

    use Exportable;

    private $payloads;

    public function __construct($payloads)
    {
        $this->payloads = $payloads;
    }

    function collection()
    {
        return $this->payloads;
    }

    public function headings(): array
    {
        return [
            'ARBGID',
            'ARBANKREF',
            'MILLREF',
            'IVLOCID',
            'BANK',
            'ARBGCURRENCY',
            'ARBGAMT',
            'DOCDATE',
            'RECEIVEDATE',
            'ARALLOCAMT',
            'BANKINDATE',
            'ARBGREMARKS',
            'CPID',
            'CREATETIME',
            'CREATEBY',
            'UPDATETIME',
            'UPDATEBY',
            'CONTRACTNO',
            'MILLNAME',
            'BANKNAME',
            'BUYERNAME',
            'IVITEMID',
        ];


    }
    public function columnFormats(): array
    {
        // TODO: Implement columnFormats() method.
        return [
            'B' => NumberFormat::FORMAT_TEXT,
            'C' => NumberFormat::FORMAT_TEXT,
        ];
    }


    public function map($row): array
    {
        return [
            'ARBGID' => $row->cxc_id ?? $row->id,
            'ARBANKREF' => (string) $row->bank_reference_no . '',
            'MILLREF' => $row->payment_id,
            'IVLOCID' => $row->inventory_location_id,
            'BANK' => $row->bank_code,
            'ARBGCURRENCY' => $row->contractable ? $row->contractable->currency : null,
            'ARBGAMT' => $row->total_payment_amount,
            'DOCDATE' => $row->doc_reference_date ? Carbon::parse($row->doc_reference_date)->format('d-M-Y') : null,
            'RECEIVEDATE' => $row->transaction_date ? Carbon::parse($row->transaction_date)->format('d-M-Y') : null,
            'ARALLOCAMT' => $row->payment_allocated_amount,
            'BANKINDATE' => $row->bank_in_date ? Carbon::parse($row->bank_in_date)->format('d-M-Y') : null,
            'ARBGREMARKS' => '',
            'CPID' => $row->counterparty_id,
            'CREATETIME' => $row->created_at ? Carbon::parse($row->created_at)->format('d-M-Y H:i:s') : null,
            'CREATEBY' => 'system',
            'UPDATETIME' => $row->updated_at ? Carbon::parse($row->updated_at)->format('d-M-Y H:i:s') : null,
            'UPDATEBY' => 'system',
            'CONTRACTNO' => $row->contract_number,
            'MILLNAME' => $row->mill ? $row->mill->short_name : null,
            'BANKNAME' => $row->bank ? $row->bank->name : null,
            'BUYERNAME' => $row->contractable ? ($row->contractable->counterparty ? $row->contractable->counterparty->name : null) : null,
            'IVITEMID' => $row->contractable ? $row->contractable->product_code : null
        ];
    }

    // force to handle certain columns as string
    public function bindValue(Cell $cell, $value)
    {
        if ($cell->getColumn() == 'B' || $cell->getColumn() == 'C') {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);
            return true;
        }

        // else return default behavior
        return parent::bindValue($cell, $value);
    }

}
