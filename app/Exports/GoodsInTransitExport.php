<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class GoodsInTransitExport implements FromArray, WithHeadings, WithStrictNullComparison, WithColumnFormatting
{

    use Exportable;

    private $goodsInTransitList;

    public function __construct(array $goodsInTransitList)
    {
        $this->goodsInTransitList = $goodsInTransitList;
    }

    function array(): array
    {
        return $this->goodsInTransitList;
    }

    public function headings(): array
    {
        return [
            'Type',
            'GIT Reference No',
            'Contract Date',
            'Counterparty',
            'Contract Number',
            'Payment Voucher No.',
            'Payment Voucher Status',
            'Vessel',
            'BL/Date',
            'Discharge Date',
            'Port of Discharge',
            'Quantity',
            'Amount',
            'Status (GIT)',
            'SAP Doc No (GIT)',
            'Posted On (GIT)',
            'Status (GI)',
            'SAP Doc No (GI)',
            'Posted On (GI)'
        ];
    }

    public function columnFormats(): array
    {
        // TODO: Implement columnFormats() method.
        return [
            'C' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'I' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'J' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ];
    }
}
