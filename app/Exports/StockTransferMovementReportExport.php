<?php

namespace App\Exports;

use App\Repositories\ReportRepository;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Events\AfterSheet;

class StockTransferMovementReportExport implements FromArray, WithHeadings, WithStrictNullComparison, WithCustomStartCell, WithEvents, ShouldAutoSize
{
    use Exportable;
    protected $reportRepository;
    protected $filters;

    public function __construct($filters)
    {
        $this->filters = $filters;
        $this->reportRepository = new ReportRepository();
    }

    public function headings(): array
    {
        return [
            'Commodity',
            'Transfer Out Date',
            'Seller',
            'Buyer',
            'Location From',
            'Location To',
            'Transfer Out Doc#',
            'Transfer In Doc#',
            'Internal Ticket Deal Ref',
            'Short Ticket Ref',
            'Long Ticket Ref',
            'Ship Month',
            'Ship Start',
            'Ship End',
            'BASIS',
            'Ctr Price',
            'Transfer In Qty',
            'Transfer Out Qty',
            'Variance'
        ];
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event){
                $event->sheet->getDelegate()->mergeCells('A1:S1');
                $cell = $event->sheet->getDelegate()->getCell('A1');

                $cell->setValue('Stock Transfer Daily Movement Listing');
                $cell->getStyle()->applyFromArray(
                    [
                        'font' => [
                            'bold' => true,
                            'size' => 18
                        ],
                        'alignment' => [
                            'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                        ],
                    ]
                );

                // Bold header
                $event->sheet->getStyle('A2:S2')->applyFromArray([
                    'font' => [
                        'bold' => true
                    ]
                ]);
            }
        ];
    }

    /**
     * @return array
     */
    public function array(): array
    {
        return $this->reportRepository->getStockTransferMovementData(Auth::user(), $this->filters);
    }

    /**
     * @return string
     */
    public function startCell(): string
    {
        return 'A2';
    }
}
