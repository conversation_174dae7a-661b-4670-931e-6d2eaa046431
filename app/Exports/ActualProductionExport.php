<?php


namespace App\Exports;


use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class ActualProductionExport implements FromArray, WithHeadings, WithStrictNullComparison
{
    use Exportable;

    private $production_actual;

    public function __construct(array $production_actual)
    {
        $this->production_actual = $production_actual;
    }

    function array(): array
    {
        return $this->production_actual;
    }

    public function headings(): array
    {
        return [
            'Reference No',
            'Mill',
            'Start Date',
            'End Date',
            'XML Date',
            'Product Code',
            'Quantity'
        ];
    }
}
