<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Rap2hpoutre\FastExcel\FastExcel;

class DailyLoadDispatch implements FromArray, WithHeadings, WithStrictNullComparison, WithColumnFormatting
{
    use Exportable;

    private $loads;

    public function __construct(array $loads)
    {
        $this->loads = $loads;
    }

    function array(): array
    {
        return $this->loads;
    }

    public function headings(): array
    {
        return [
            '#',
            'Commodity',
            'Dispatch Date',
            'Legal Entity',
            'Region',
            'Profit Center',
            'Inventory Location',
            'DO #',
            'Driver',
            'Buyer Name',
            'Deliver To',
            'Contract Number',
            'Discharge Port',
            'Shipment Start',
            'Shipment End',
            'Shipment Month',
            'Vehicle',
            'Contract Basis',
            'Contract Price',
            'Provisional Price',
            'Dispatched (MT)',
            'Delivered (MT)',
            'Variance (MT)',
            'Legal Entity MPOB License',
            'Counterparty MPOB License'
        ];
    }

    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'N' => NumberFormat::FORMAT_DATE_DATETIME,
            'O' => NumberFormat::FORMAT_DATE_DATETIME,
        ];
    }
}
