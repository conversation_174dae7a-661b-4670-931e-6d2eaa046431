<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class FFBIntakeReport implements FromArray, WithStrictNullComparison, WithHeadings, WithEvents
{
    use Exportable;

    private $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    function array(): array
    {
        return $this->data;
    }

    public function headings() : array
    {
        return [
            'Mill/Location',
            'Counterparty',
            'Contract Number',
            'Quantity (MT)',
            'Average Mill OER',
            'Average Mill KER',
            'Total Partial',
            'Total Final Payment',
            'Total Adjustment',
            'Total Payment',
            'Total Tax Amount',
            'Total Incl Tax',
            'RM/MT'
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $cellRange = 'A1:M1'; // All headers
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setSize(12)->setBold(true);
                $event->sheet->getDelegate()->getStyle($cellRange)->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setARGB('9E8578');

                $remarks = count($this->data);
                $cellRange = "A$remarks";
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setBold(true);
            },
        ];
    }


}
