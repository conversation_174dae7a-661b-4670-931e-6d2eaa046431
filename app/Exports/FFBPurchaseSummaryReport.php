<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class FFBPurchaseSummaryReport implements FromArray, WithStrictNullComparison, WithEvents, WithColumnFormatting
{
    use Exportable;

    private $loads;

    public function __construct(array $loads)
    {
        $this->loads = $loads;
    }

    function array(): array
    {
        return $this->loads;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {

                $cellRange = 'A1:E1'; // All headers
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setSize(14)->setBold(true);
                $event->sheet->getDelegate()->mergeCells($cellRange)->getStyle($cellRange)->getAlignment()
                    ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                $cellRange = 'A2:E2';
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setSize(14)->setBold(true);
                $event->sheet->getDelegate()->mergeCells($cellRange)->getStyle($cellRange)->getAlignment()
                    ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                $cellRange = 'A3:E3';
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setBold(true);

                $cellRange = 'A4:E4';
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setBold(true);

                $cellRange = 'A5:E5';
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setBold(true);

                $cellRange = 'A7:E7';
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setBold(true);
            },
        ];
    }

    /**
     * @inheritDoc
     */
    public function columnFormats() : array
    {
        return [
            'C' => NumberFormat::FORMAT_NUMBER_00,
            'D' => NumberFormat::FORMAT_NUMBER_00,
            'E' => NumberFormat::FORMAT_NUMBER_00
        ];
    }
}
