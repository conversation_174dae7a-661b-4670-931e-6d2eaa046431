<?php


namespace App\Exports;


use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class MpobMonthlyReportExport extends DefaultValueBinder implements FromCollection, WithHeadings, ShouldAutoSize, WithColumnFormatting, WithCustomValueBinder
{
    use Exportable;
    private $payload;

    public function __construct($payload)
    {
        $this->payload = $payload;
    }

    function collection()
    {
        return $this->payload;
    }

    public function headings(): array
    {
        return [
            'Legal Entity Name',
            'Legal Entity MPOB License',
            'Transaction Type',
            'Counterparty Name',
            'Internal Counterparty',
            'Counterparty MPOB License',
            'Product Code',
            'Total Contract Quantity',
            'Quantity Uom'
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_TEXT,
            'F' => NumberFormat::FORMAT_TEXT
        ];
    }

    public function bindValue(Cell $cell, $value)
    {
        if ( $cell->getColumn() == 'B' || $cell->getColumn() == 'F' ){
            $cell->setValueExplicit($value, DataType::TYPE_STRING);
            return true;
        }

        return parent::bindValue($cell, $value);
    }
}
