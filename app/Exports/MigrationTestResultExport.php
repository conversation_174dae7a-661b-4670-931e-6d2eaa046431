<?php
/**
 * Created by PhpStorm.
 * User: sooling.tee
 * Date: 2019-03-21
 * Time: 11:01
 */

namespace App\Exports;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

/**
 * Class ContractExport
 * @package App\Exports
 */
class MigrationTestResultExport implements FromArray, WithStrictNullComparison, WithHeadings
{
    use Exportable;

    protected $data;

    public function setData($data){
        $this->data = $data;
        return $this;
    }
    /**
     * @return array
     */
    public function array(): array
    {
        return $this->data;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Class',
            'Object ID',
            'Description',
            'Event',
            'Value 1',
            'Value 2',
            'Difference'
        ];
    }
}
