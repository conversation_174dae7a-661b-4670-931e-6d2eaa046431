<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class AdhocReportExport implements FromArray, WithHeadings, WithStrictNullComparison
{

    use Exportable;

    private $payloads;
    private $headings;

    public function __construct(array $payloads, array $headings)
    {
        $this->payloads = $payloads;
        $this->headings = $headings;
    }

    function array(): array
    {
        return $this->payloads;
    }

    public function headings(): array
    {
        return $this->headings;
    }
}
