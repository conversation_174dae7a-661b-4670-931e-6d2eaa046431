<?php

namespace App\Exports;

use App\Helpers\NumberHelper;
use App\Models\FuturesContract;
use App\Models\Incoterms;
use App\Models\TransportMode;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class OpenContract implements FromArray, WithHeadings, WithEvents, ShouldAutoSize, WithCustomStartCell, WithStrictNullComparison, WithColumnFormatting
{

    const FORMAT_NAME = 'overall-open-contracts';

    use Exportable;

    private $contracts;

    public function __construct($contracts)
    {
        $this->contracts = $contracts;
    }

    function array(): array
    {
        return $this->contracts->map(function ($contract) {

            $port = null;
            $optionalPort = null;

            $carryForwardLand = 0;
            $carryForwardSea = 0;

            if ($incoterm = $contract->incoterms_relationship ) {
                if ($incoterm->port_basis == Incoterms::BASIS_LOAD_PORT){
                    $port = $contract->load_port_name ?? null;
                    $optionalPort = $contract->discharge_port_name ?? null;
                } else {
                    $port = $contract->discharge_port_name ?? null;
                    $optionalPort = $contract->load_port_name ?? null;
                }
            }

            if ($contract->mode_of_transport == TransportMode::TYPE_LAND) {
                $carryForwardLand = $contract->quantity - $contract->contractable->loads->sum('fulfilled_quantity');
            } else {
                $carryForwardSea = $contract->quantity - $contract->contractable->loads->sum('fulfilled_quantity');
            }

            return [
                $contract->legal_entity_name,
                $contract->profit_center_code,
                $contract->product_code,
                Carbon::parse($contract->contract_date)->tz(Auth::user()->timezone)->toDateString(),
                $contract->contractable->incoterms,
                $contract->contract_number,
                $contract->contractable->seller_reference,
                $contract->contractable->buyer_reference,
                $contract->transaction_type == 'S' ? $contract->profit_center_name : $contract->counterparty_name,
                $contract->transaction_type == 'S' ? $contract->counterparty_name : $contract->profit_center_name,
                Carbon::parse($contract->shipment_month)->toDateString(),
                Carbon::parse($contract->shipment_date_from)->toDateString(),
                Carbon::parse($contract->shipment_date_to)->toDateString(),
                !$contract->contractable->getClass() == FuturesContract::class && $contract->deliver_to_name ? $contract->deliver_to_name : $port,
                $optionalPort,
                $contract->currency,
                $contract->base_price - $contract->discount + $contract->premium,
                $contract->final_price,
                $contract->inventory_location_name,
                $contract->quantity,
                $contract->allocated_quantity,
                $contract->contractable->loads->sum('delivered_collected_quantity'),
                $contract->contractable->loads->sum('invoiceable_quantity'),
                $carryForwardLand,
                $contract->mode_of_transport == TransportMode::TYPE_LAND && $contract->quantity > 0 ? NumberHelper::prettify($contract->contractable->loads->sum('invoiceable_quantity') / $contract->quantity * 100, 2) . '%' : 0,
                $contract->contractable->billOfLadings ? $contract->contractable->billOfLadings->sum('quantity') :  0,
                $carryForwardSea,
                $contract->mode_of_transport == TransportMode::TYPE_SEA && $contract->quantity > 0 ? NumberHelper::prettify($contract->contractable->loads->sum('invoiceable_quantity') / $contract->quantity * 100, 2) . '%' : 0,
                $contract->contractable->trader ? $contract->contractable->trader->name : null,
            ];

        })->toArray();
    }

    public function headings(): array
    {
        return [
            'Legal Entity',
            'Profit Center',
            'Commodity',
            'Ctr Date',     // D
            'Ctr Basis',
            'Ctr Reference',
            'Seller Reference',
            'Buyer Reference',
            'Seller',
            'Buyer',
            'Shipment Month',       // K
            'Shipment Start Date',      // L
            'Shipment End Date',        // M
            'Deliver To',
            'Optional Port',
            'Currency',
            'Prov Price Include BPB',
            'Price',
            'Stock Location',
            'Contract Qty',
            'Alloc Qty',
            'Released Qty',
            'Fulfilled Qty',
            'CF (Local)',
            '% Complete (Local)',
            'BL Qty',
            'CF (Ocean)',
            '% Complete (Ocean)',
            'Trader',
        ];
    }

    public function startCell(): string
    {
        return 'A2';
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event){
                $event->sheet->getDelegate()->mergeCells('A1:AC1');
                $cell = $event->sheet->getDelegate()->getCell('A1');

                $cell->setValue('OVERALL OPEN CONTRACT');
                $cell->getStyle()->applyFromArray(
                    [
                        'font' => [
                            'bold' => true,
                            'size' => 18
                        ],
                        'alignment' => [
                            'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                        ],
                    ]
                );

                // Bold header
                $event->sheet->getStyle('A2:AC2')->applyFromArray([
                    'font' => [
                        'bold' => true
                    ]
                ]);
            }
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'K' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'L' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'M' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ];
    }
}
