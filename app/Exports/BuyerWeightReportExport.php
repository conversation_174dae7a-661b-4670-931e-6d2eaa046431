<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\WithMapping;

class BuyerWeightReportExport implements WithHeadings, WithStrictNullComparison, FromArray
{

    use Exportable;

    private $results;
    private $otherData;

    public function __construct($results, $otherData)
    {
        $this->results = $results;
        $this->otherData = $otherData;
    }

    public function array(): array
    {
        $data = [];
        $user = Auth::user();

        $inventoryLocations = $this->otherData['inventory_locations']->keyBy('id');
        $products = $this->otherData['products']->keyBy('id');
        $profitCenters = $this->otherData['profit_centers']->keyBy('id');

        foreach ($this->results as $result) {

            $data[] = [
                $result->contractable->contract_number,
                Carbon::parse($result->contractable->shipment_month)->format(config('spot.date')),
                $result->reference_no,
                Carbon::parse($result->dispatch_receive_datetime, 'UTC')->tz($user->timezone)->format(config('spot.datetime')),
                $inventoryLocations[$result->inventory_location_id]->name,
                $products[$result->product_id]->code,
                $profitCenters[$result->contractable->profit_center_id]->name
            ];
        }
        return $data;
    }

    public function headings(): array
    {

        $header = [
            'Contract Number',
            'Shipment Month',
            'Reference No',
            'Dispatch Received',
            'Inventory Location',
            'Product',
            'Profit Center'
        ];

        return $header;
    }
}
