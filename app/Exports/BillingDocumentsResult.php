<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class BillingDocumentsResult implements FromArray, WithHeadings, WithStrictNullComparison, WithColumnFormatting
{

    use Exportable;

    private $billingDocuments;

    public function __construct(array $billingDocuments)
    {
        $this->billingDocuments = $billingDocuments;
    }

    function array(): array
    {
        return $this->billingDocuments;
    }

    public function headings(): array
    {
        return [
            'Commodity',
            'Stock Location',
            'Bill To',
            'Billed Qty',
            'Amount Exclude Tax',
            'Amount',
            'Amount After Offset',
            'Contract Type',
            'Contract Ref',
            'Pricing Status',
            'Pricing Type',
            'Contract Price',
            'Contract Status',
            'Doc Number',
            'Document Created At',
            'Document Date',
            'Batch Ref',
            'Doc Type',
            'Doc Status',
            'Profit Center',
            'UOM',
            'Contract Qty',
            'Exchange Rate',
            'Buyer Reference',
            'Tax Code',
            'Tax Rate',
            'Contract Basis',
            'Contract Date',
            'Provisional Price',
            'Is Transport Cost',
            'GL Account Code',
            'Currency',
            'Posted By',
            'SST PMT',
            'Payment Terms',
            'Released Date',    // AJ
            'SAP Customer Code (Bill To)',  // AK
            'SAP-ID Customer Code (Bill To)',  // AL
            'Delivered To',
        ];
    }

    public function columnFormats(): array
    {
        // TODO: Implement columnFormats() method.
        return [
            'O' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'P' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'AB' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'AJ' => NumberFormat::FORMAT_DATE_DATETIME,
            'AK' => NumberFormat::FORMAT_TEXT,
            'AL' => NumberFormat::FORMAT_TEXT,
            'AM' => NumberFormat::FORMAT_TEXT,
        ];
    }
}
