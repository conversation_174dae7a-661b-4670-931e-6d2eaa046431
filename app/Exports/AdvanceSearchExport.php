<?php

/**
 * Created by <PERSON>p<PERSON>tor<PERSON>.
 * User: sooling.tee
 * Date: 2019-03-21
 * Time: 11:01
 */

namespace App\Exports;

use App\Models\Contract;
use App\Models\ContractType;
use App\Models\RubberContract;
use App\Services\AdvanceSearchService;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Query\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

/**
 * Class ContractExport
 * @package App\Exports
 */
class AdvanceSearchExport implements FromQuery, WithStrictNullComparison, ShouldAutoSize, WithChunkReading, WithMapping, WithHeadings
{
    use Exportable;

    protected $input;

    public function __construct($input)
    {
        $this->input = $input;
    }

    public function query()
    {
        $advanceSearchService = new AdvanceSearchService();

        $query = $advanceSearchService
            ->setInput($this->input)
            ->setIsQueryOnly(true)
            ->setPerPage($this->input['per_page'])
            ->validateSearchFilters()
            ->validateLogicalOperator()
            ->setModel()
            ->getSearchableFiltersForModel()
            ->searchContracts()
            ->getQuery();

        return $query;
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function map($row): array
    {
        $allocations = $row->allocations;
        $inventoryLocationNames = '';

        if ($allocations != null && count($allocations) > 0) {
            $inventoryLocations = $allocations->pluck('inventoryLocation')->pluck('name');
            $inventoryLocationNames = implode(", ", $inventoryLocations->toArray());
        }

        return [
            $row->product_code,
            ContractType::MODEL_TO_TYPE[$row->getClass()],
            $row->contract_date,
            $row->shipment_month,
            $row->contract_number,
            $row->legal_entity_name,
            $row->profit_center_name,
            $inventoryLocationNames,
            $row->transaction_type == Contract::TYPE_SALES ? $row->profit_center_name : $row->counterparty_name,
            $row->transaction_type == Contract::TYPE_SALES ? $row->counterparty_name : $row->profit_center_name,
            $row->incoterms,
            method_exists($row, 'getProposedLoadPortsAsIs') ? $row->getProposedLoadPortsAsIs() : null,
            method_exists($row, 'getProposedDischargePortsAsIs') ? $row->getProposedDischargePortsAsIs() : null,
            $row->quantity,
            $row->getClass() == RubberContract::class ? $row->currencyUnit->code : $row->currency,
            $row->base_price,
            $row->fixed_price,
            $row->premium,
            $row->discount,
            $row->getClass() == RubberContract::class ? $row->price : $row->final_price,
            $row->pricing_status,
            $row->pricing_type,
            $row->status,
            $row->trader->name,
            !empty($row->seller_reference) ? $row->seller_reference : '-',
            !empty($row->buyer_reference) ? $row->buyer_reference : '-',
            $row->createdBy->name,
        ];
    }

    public function headings(): array
    {
        return [
            'product',
            'contract type',
            'contract date',
            'shipment month',
            'contract number',
            'legal entity',
            'profit center',
            'inventory location',
            'seller',
            'buyer',
            'incoterms',
            'load port',
            'discharge port',
            'quantity',
            'currency',
            'provisional price',
            'fixed price',
            'premium',
            'discount',
            'final price',
            'pricing status',
            'pricing type',
            'status',
            'trader',
            'seller reference',
            'buyer reference',
            'created by'
        ];
    }
}
