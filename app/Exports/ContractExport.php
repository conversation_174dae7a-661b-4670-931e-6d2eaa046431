<?php
/**
 * Created by PhpStorm.
 * User: sooling.tee
 * Date: 2019-03-21
 * Time: 11:01
 */

namespace App\Exports;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;

/**
 * Class ContractExport
 * @package App\Exports
 */
class ContractExport implements FromView
{
    use Exportable;

    private $view;
    private $data;

    /**
     * @param $view
     * @return $this
     */
    public function forView($view)
    {
        $this->view = $view;
        return $this;
    }

    /**
     * @param $data
     * @return $this
     */
    public function forData($data)
    {
        $this->data = $data;
        return $this;
    }

    /**
     * @return View
     */
    public function view(): View
    {
        return view($this->view, [
            'data' => $this->data,
            'hide_checkbox' => true,
            'hide_action' => true
        ]);
    }
}
