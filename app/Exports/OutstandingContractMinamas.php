<?php

namespace App\Exports;

use App\Models\PricingType;
use App\Models\VesselNominationContract;
use App\Repositories\UomConversionRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class OutstandingContractMinamas implements FromArray, WithHeadings, WithEvents, ShouldAutoSize, WithCustomStartCell, WithStrictNullComparison, WithColumnFormatting
{

    const FORMAT_NAME = 'outstanding-overall-minamas';

    use Exportable;

    private $contracts;
    private $vessel_nominations;
    private $uom_conversion_repository;

    public function __construct($contracts)
    {
        $this->contracts = $contracts;
        $this->vessel_nominations = collect();
        $this->uom_conversion_repository = new UomConversionRepository();
    }

    function array(): array
    {
        foreach ($this->contracts->groupBy('contractable_type') as $contractable_type => $contracts) {
            foreach ($contracts->chunk(2000) as $c) {
                $vessel_nomination_contracts = VesselNominationContract::with('vesselNomination', 'vesselNomination.vessel', 'contractable')
                    ->where('contractable_type', $contractable_type)
                    ->whereIn('contractable_id', $c->pluck('contractable_id'))
                    ->get()
                    ->groupBy('contractable.contract_number');
                $this->vessel_nominations = $this->vessel_nominations->merge($vessel_nomination_contracts);

                $vessel_nomination_contracts = VesselNominationContract::with('vesselNomination', 'vesselNomination.vessel', 'contractable')
                    ->where('contractable_type', $contractable_type)
                    ->whereIn('contractable_id', $c->pluck('contractable_id'))
                    ->get()
                    ->groupBy('contractable.contract_number');
                $this->vessel_nominations = $this->vessel_nominations->merge($vessel_nomination_contracts);
            }
        }

        $this->contracts->loadMissing(['contractable.billingDocuments.lineItems']);

        return $this->contracts->map(function ($contract) {

            if (Carbon::parse($contract->shipment_month)->lessThan(Carbon::now()->startOfMonth())) {
                $type = 'OUTSTANDING';
            } else {
                $type = 'OPEN';
            }

            $vessel_nomination = $this->vessel_nominations[$contract->contract_number] ?? null;
            $vessels = null;
            $nomination_status = null;
            $nomination_date = null;
            if ($vessel_nomination) {
                $vessels = $vessel_nomination->pluck('vesselNomination.vessel.name')->implode(', ');
                $nomination_date = $vessel_nomination->pluck('vesselNomination.created_at')->map(function ($d) {
                    return Carbon::parse($d)->tz(Auth::user()->timezone)->toDateString();
                })->implode(', ');
                $nomination_status = 'confirmed';
            }

            $valuation_price = $contract->pricing_status == PricingType::STATUS_FULLY_PRICED ? $contract->final_price : NULL;
            if ($valuation_price && $contract->base_price_uom != $contract->quantity_uom) {
                $valuation_price = $this->uom_conversion_repository->doUomConversionRaw($contract->quantity_uom, $contract->base_price_uom, $contract->product_id, $contract->final_price);
            }


            $fulfill_qty = 0;

            if ($contract->allocated_quantity > 0) {
                $fulfill_qty = $contract->load_invoiceable_quantity;
            } else if ($contract->contract_load_quantity > 0) {
                $fulfill_qty = $contract->contract_load_quantity;
            }

            return [
                $type,
                $contract->contractable_id,
                $contract->contract_number,
                Date::timestampToExcel(Carbon::parse($contract->contract_date)->timestamp),
                strtoupper($contract->objective),
                $contract->legal_entity_code,
                $contract->business_unit_name,
                $contract->profit_center_code,
                $contract->transaction_type == 'S' ? 'SHORT' : 'LONG',
                $contract->product_code,
                $contract->specification_group_name,
                $contract->incoterms,
                $contract->load_port_name,
                Carbon::parse($contract->shipment_month)->format('M Y'),
                Date::timestampToExcel(strtotime(Carbon::parse($contract->shipment_date_from)->toDateString())),
                Date::timestampToExcel(strtotime(Carbon::parse($contract->shipment_date_to)->toDateString())),
                $contract->quantity,    // contract qty
                $fulfill_qty,    // fulfilled qty land
                $contract->bill_of_ladings_quantity ?? 0,   // fulfilled qty sea
                $contract->contractable->getBilledQty(),   // invoiced qty
                $contract->load_tolerance_percent,
                0,
                $valuation_price,
                $contract->pricing_status == PricingType::STATUS_FULLY_PRICED ? $contract->final_price : NULL,
                $contract->pricing_type == PricingType::TYPE_OUTRIGHT ? 0 : $contract->final_base_price, // base + premium - discount
                $contract->discount,
                $contract->premium,
                $contract->currency,
                $contract->currency,
                $contract->forex_rate,
                $contract->quantity_uom,
                $contract->status,
                $contract->incoterms,
                $contract->counterparty_long_name,
                $contract->broker_name,
                $contract->trader_name,
                $vessels,
                $nomination_status,
                $nomination_date,
                $contract->payment_terms_name,
                $contract->packing_unit,
                $contract->pricing_type
            ];

        })->toArray();
    }

    public function headings(): array
    {
        return [
            'CXC_EXTRACT',
            'CONTRACT_ID',
            'CONTRACT_REF',
            'CONTRACT_DATE',
            'CONTRACT_TYPE',
            'LEGAL_ENTITY',
            'COMPANY',
            'PROFIT_CENTER',
            'LONG_SHORT',
            'COMMODITY',
            'PRODUCT',
            'INCOTERM',
            'LOCATION',
            'PERIOD',
            'SHIPMENT_START',
            'SHIPMENT_END',
            'CONTRACT_QUANTITY',
            'FULLFILLED_QUANTITY (LOAD)',
            'FULLFILLED_QUANTITY (BL)',
            'INVOICED_QUANTITY',
            'TOLERANCE_PERCENT',
            'LOTS_QUANTITY',
            'VALUATION_PRICE',
            'ORIGINAL_PRICE',
            'PROVISIONAL_PRICE',
            'DISCOUNT_VALUE',
            'PREMIUM_VALUE',
            'VALUATION_CURRENCY',
            'ORIGINAL_CURRENCY',
            'EXCHANGE_RATE',
            'UOM',
            'CONTRACT_STATUS',
            'CONTRACT_TERM',
            'COUNTERPARTY',
            'BROKER',
            'TRADER_NAME',
            'VESSEL',
            'NOMINATION_STATUS',
            'NOMINATION_DATE',
            'PAYMENT_TERM',
            'PACKING_UNIT',
            'PRICING_TYPE'
        ];
    }

    public function startCell(): string
    {
        return 'A1';
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event){
                // Bold header
                $event->sheet->getStyle('A1:AP1')->applyFromArray([
                    'font' => [
                        'bold' => true
                    ]
                ]);
            }
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'N' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'O' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'P' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ];
    }
}
