<?php

namespace App\Exports;

use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class PremiumDiscountExport implements FromArray, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    const FORMAT_NAME = 'premium-discount-report';

    use Exportable;

    private $contracts;

    public function __construct($contracts)
    {
        $this->contracts = $contracts;
    }

    function array(): array
    {

        return $this->contracts->map(function ($contract) {

            $unit_price = $contract->getContractPrice(false, false, false);
            $unit_price_with_premium_discount = $contract->getContractPrice(false, false, true);

            return [
                $contract->contract_number,
                Carbon::parse($contract->contract_date)->tz(Auth::user()->timezone)->format('d-M-Y H:i:s'),
                $contract->product->code,
                $contract->quantity,
                $contract->counterparty->name,
                !empty($contract->contractTerms) ? $contract->contractTerms->name : '-',
                $contract->currency,
                $unit_price_with_premium_discount,
                $unit_price,
                $contract->getPremiumDiscount()
            ];
        })->toArray();
    }

    public function headings(): array
    {
        return [
            'Contract Number',
            'Contract Date',
            'Commodity',
            'Contract Quantity',
            'Counterparty',
            "Contract Terms",
            "Currency",
            "Contract Unit Price After Premium Discount",
            'Contract Unit Price Before Premium Discount',
            'Premium / Discount',
        ];
    }

}
