<?php

namespace App\Exports;

use App\Models\ContractType;
use App\Models\FuturesContract;
use App\Models\OptionsContract;
use App\Models\PricingType;
use App\Models\RubberContract;
use App\Models\TradingChain;
use App\Models\TransportMode;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class GeneralTradeContract implements FromCollection, WithHeadings, WithEvents, ShouldAutoSize, WithCustomStartCell, WithStrictNullComparison, WithColumnFormatting
{

    const FORMAT_NAME = 'general-trade-contracts';

    use Exportable;

    private $contracts;

    public function __construct($contracts)
    {
        $this->contracts = $contracts;
    }

    function collection()
    {
        $payload = collect();

        foreach($this->retrieveContractsToArray($this->contracts) as $row) {
            $payload->push($row);
        }

        // sort by contract number column
        $payload = $payload->sortBy(function ($val, $key) {
            return $val[8];
        });

        return $payload;
    }

    private function retrieveContractsToArray($contracts)
    {
        $contract_balance_quantity = [];

        $all_contract_ids = $contracts->mapToGroups(function($val){
            return [$val->contractable_type => $val->contractable_id];
        });

        $trading_chains = [];

        foreach ( $all_contract_ids as $type => $ids ){

            foreach ( $ids->chunk(1000) as $ids_chunk ){

                $chains = TradingChain::whereIn('contractable_id', $ids_chunk->toArray())
                    ->where('contractable_type', $type)
                    ->get();

                foreach ( $chains as $c ){
                    $trading_chains[$c->contractable_type][$c->contractable_id] = $c->chain_id;
                }
            }

        }

        $trading_chain_data = [];

        foreach ( $trading_chains as $contractable_type => $list ){

            foreach ( collect($list)->chunk(1000) as $ids_chunk ){

                $data = TradingChain::with(['contractable' => function ($query) {
                    $query->select('id', 'contract_number');
                }])
                ->whereIn('chain_id', $ids_chunk->toArray())
                ->orderBy('order', 'ASC')->get();

                foreach ( $data as $d ){
                    $trading_chain_data[$d->chain_id][] = $d->contractable->contract_number;
                }
            }

        }

        foreach ($contracts as $contract) {
            $trading_chain_details = [];

            // Trying to get the trading chain details
            if ( isset($trading_chains[$contract->contractable_type][$contract->contractable_id]) ){

                // First we resolve to get trading chain ID from contract
                $trading_chain_id = $trading_chains[$contract->contractable_type][$contract->contractable_id];

                // Using the trading chain ID, we get the list of contracts in that chain.
                // Only process if the trading chain has 2 or more contracts
                if ( isset($trading_chain_data[$trading_chain_id]) && count($trading_chain_data[$trading_chain_id]) >= 2 ){

                    $contract_list_in_chain = $trading_chain_data[$trading_chain_id];

                    foreach ( $contract_list_in_chain as $c ){
                        $trading_chain_details[] = $c;
                    }

                }

            }

            $load_port = $contract->load_port_name;
            $discharge_port = $contract->discharge_port_name;

            if (empty($discharge_port) && !empty($contract->proposed_discharge_ports)) {
                if (is_string($contract->proposed_discharge_ports)) {
                    $to_array = json_decode($contract->proposed_discharge_ports, true);
                    if (is_string($to_array)) {
                        $to_array = json_decode($to_array, true);
                    }
                    $discharge_port = implode(',', Arr::pluck($to_array, 'name'));
                }
            }

            if (empty($load_port) && !empty($contract->proposed_load_ports)) {
                if (is_string($contract->proposed_load_ports)) {
                    $to_array = json_decode($contract->proposed_load_ports, true);
                    if (is_string($to_array)) {
                        $to_array = json_decode($to_array, true);
                    }
                    $load_port = implode(',', Arr::pluck($to_array, 'name'));
                }
            }

            $region = $contract->inventory_location_region;
            $deliver_to = '-';

            if (!empty($contract->allocation_deliver_to_name)) {
                $deliver_to = $contract->allocation_deliver_to_name;
            } else {
                $deliver_to = $contract->deliver_to_name;
            }

            $inventory_location = $contract->inventory_location_code;
            $tender_point = $contract->tender_point;

            if ($contract->mode_of_transport == TransportMode::TYPE_SEA) {
                $total_load_quantity = $contract->bill_of_ladings_quantity;
                $percent_of_fulfilled = $total_load_quantity > 0 ? ($total_load_quantity / $contract->quantity) * 100 : 0;
            } else {
                $percent_of_fulfilled = 0;
                if ($contract->load_invoiceable_quantity > 0) {
                    $percent_of_fulfilled = ($contract->load_invoiceable_quantity / $contract->allocated_quantity) * 100;
                } elseif ($contract->contract_load_quantity > 0) {
                    $percent_of_fulfilled = ($contract->contract_load_quantity / $contract->quantity) * 100;
                }
            }

            $contractable = $contract->contractable;
            $contract_price = 0;

            if ( in_array($contract->contractable_type, [FuturesContract::class, OptionsContract::class])||
                ( !in_array($contract->contractable_type, [FuturesContract::class, OptionsContract::class]) && $contract->pricing_status == PricingType::STATUS_FULLY_PRICED ) ){

                $contract_price = $contract->fixed_price - $contract->discount + $contract->premium;

            }else{

                if ( $contract->pricing_type == PricingType::TYPE_MPOB ){
                    $contract_price = $contract->base_price - $contract->discount + $contract->premium;
                }

            }

            $contract_number = $contract->contract_number;

            if ($contract->contractable_type == RubberContract::class &&
                $contractable->is_long_term == true) {
                $contract_number .= ' (L)';
            }

            $broker_account_number = $contract->getBrokerAccountNumber();

            $object = [
                $contract->product_code,
                null,
                strtoupper(ContractType::MODEL_TO_TYPE[$contract->contractable_type] ?? null),
                ucfirst($contractable->objective),
                Carbon::parse($contract->contract_date)->toDateString(),
                Carbon::parse($contract->shipment_month)->format('M Y'),
                Carbon::parse($contractable->shipment_date_from)->toDateString(),
                Carbon::parse($contractable->shipment_date_to)->toDateString(),
                $contract_number,
                $contract->seller_reference,
                $contract->buyer_reference,
                $contract->broker_reference,
                $contract->legal_entity_name,
                $contract->profit_center_code,
                strtoupper($region),
                $inventory_location,
                $contract->transaction_type == 'S' ? $contract->profit_center_code : $contract->counterparty_name,
                $contract->transaction_type == 'S' ? $contract->counterparty_name : $contract->profit_center_code,
                $contract->incoterms,
                $discharge_port,
                $deliver_to,
                $contract->broker_name,
                $contract->allocated_quantity,
                $contract->currency,
                $contract_price,
                $contract->pricing_type == PricingType::TYPE_OUTRIGHT ? 0 : $contract->base_price - $contract->discount + $contract->premium, //TODO: futures & options, pricing_type is NULL, please confirm this logic
                strtoupper($contract->pricing_type),
                ucfirst($contract->pricing_status),
                $contract->price_index_name ?? 'N/A',
                ucfirst($contract->status),
                $tender_point,
                $contract->trader_name,
                round($percent_of_fulfilled, 2),
                $load_port ?? 'Not Applicable',
                $discharge_port ?? 'Not Applicable',
                $contract->specification_group_name ?? 'N/A',
                $contract->forex_rate,
                $contract->rspo_status,
                $broker_account_number,
                $trading_chain_details[0] ?? null,
                $trading_chain_details[1] ?? null,
                $trading_chain_details[2] ?? null,
                $trading_chain_details[3] ?? null,
                $contract->contract_term ?? null,
            ];

            // Ignore unallocated contract quantities, which will be covered in the next section.
            if ( $contract->allocated_quantity > 0 ){
                yield $object;
            }

            if ( !isset($contract_balance_quantity[$contract->contract_number]) ){
                $contract_balance_quantity[$contract->contract_number] = [
                    'balance' => $contract->quantity,
                    'object' => $object
                ];
            }
            // allocated quantity is support up to 4 decimal points (*10000)
            // PHP doesn't have a decimal datatype
            // Because floating point arithmetic != real number arithmetic. An illustration of the difference due to imprecision is, for some floats a and b, (a+b)-b != a. This applies to any language using floats.
            $contract_balance_quantity[$contract->contract_number]['balance'] = round($contract_balance_quantity[$contract->contract_number]['balance'] - $contract->allocated_quantity, 4);

        }

        // Handle unallocated
        foreach ($contract_balance_quantity as $contract_number => $val ){

            if ( $val['balance'] > 0 ){
                $val['object'][22] = $val['balance'];
                $val['object'][15] = '(unallocated)';
                yield $val['object'];
            }
        }

    }

    public function headings(): array
    {
        return [
            'Commodity',
            'Tags',
            'Ctr Type',
            'Position Type',
            'Ctr Date',
            'Shipment Month',
            'Ship Start Date',
            'Ship End Date',
            'Contract Ref',
            'Seller Ref',
            'Buyer Ref',
            'Broker Ref',
            'Legal Entity',
            'Profit Center',
            'Region',
            'Stock Loc',
            'Seller',
            'Buyer',
            'Ctr Basis',
            'Port Basis',
            'Deliver To/Received From',
            'Broker',
            'Ctr Qty',
            'Ctr Curr',
            'Ctr Price',
            'Prov Price (incl. PBP)',
            'Pricing Type',
            'Pricing Status',
            'Price Index',
            'Ctr Status',
            'Tender Point',
            'Trader',
            '% Fulfilled',
            'Load Port',
            'Discharge Port',
            'Specifications',
            'Forex Rate',
            'RSPO status',
            'Broker Account Number',
            'Trading Chain 1st Ctr',
            'Trading Chain 2nd Ctr',
            'Trading Chain 3rd Ctr',
            'Trading Chain 4th Ctr',
            'Contract Term',
        ];
    }

    public function startCell(): string
    {
        return 'A2';
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event){
                $event->sheet->getDelegate()->mergeCells('A1:AR1');
                $cell = $event->sheet->getDelegate()->getCell('A1');

                $cell->setValue('GENERAL TRADES LIST  REPORT');
                $cell->getStyle()->applyFromArray(
                    [
                        'font' => [
                            'bold' => true,
                            'size' => 18
                        ],
                        'alignment' => [
                            'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                        ],
                    ]
                );

                // Bold header
                $event->sheet->getStyle('A2:AR2')->applyFromArray([
                    'font' => [
                        'bold' => true
                    ]
                ]);
            }
        ];
    }

    public function columnFormats(): array
    {
        return [
            'AM' => NumberFormat::FORMAT_TEXT,
        ];
    }
}
