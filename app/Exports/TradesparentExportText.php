<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class TradesparentExportText
{

    protected $delimiter = "\t";

    protected $enclosure = '';

    public function __construct($viewName)
    {
        $this->viewName = $viewName;
    }

    public function getPayload()
    {
        return DB::table($this->viewName)->get();
    }

    public function headings(): array
    {
        return [
            'CXC_EXTRACT',
            'CONTRACT_ID',
            'CONTRACT_REF',
            'CONTRACT_DATE',
            'CONTRACT_TYPE',
            'LEGAL_ENTITY',
            'COMPANY',
            'PROFIT_CENTER',
            'LONG_SHORT',
            'COMMODITY',
            'PRODUCT',
            'INCOTERM',
            'LOCATION',
            'PERIOD',
            'SHIPMENT_START',
            'SHIPMENT_END',
            'CONTRACT_QUANTITY',
            'ALLOCATED_QUANTITY',
            'FULFILLED_QUANTITY',
            'TOLERANCE_QUANTITY',
            'LOTS_QUANTITY',
            'VALUATION_PRICE',
            'ORIGINAL_PRICE',
            'PROVISIONAL_PRICE',
            'DISCOUNT_VALUE',
            'VALUATION_CURRENCY',
            'ORIGINAL_CURRENCY',
            'EXCHANGE_RATE',
            'UOM',
            'CONTRACT_STATUS',
            'CONTRACT_TERM',
            'COUNTERPARTY',
            'COUNTERPARTY_INTERNAL',
            'BROKER',
            'BROKER_ACCOUNT',
            'TRADER_NAME',
            'VESSEL',
            'NOMINATION_STATUS',
            'NOMINAITON_DATE',
            'PAYMENT_TERM',
            'PACKING_UNIT',
            'PAYMENT_STATUS',
            'TRADE_TYPE',
            'PRICING_TYPE',
            'PROV_PRICE_INDEX_NAME',
        ];
    }

    public function store($filepath)
    {
        return $this->writeCollectionToFile( $this->getPayload(),  $filepath);
    }

    public function writeCollectionToFile(Collection $collection, string $filepath): bool
    {
        try {

            $file = fopen($filepath, 'w');

            // HEADER (remove header)
            //fwrite($file, $this->arrayToLine($this->headings()));

            if ($collection->count() == 0) {
                fclose($file);
                return true;
            }

            // BODY
            foreach ($this->collectionToLine($collection) as $line) {
                fwrite($file, $line);
            }

            fclose($file);

            return true;

        } catch (\Throwable $e) {

            Log::error($e->getMessage());
            return false;
        }

    }

    private function collectionToLine(Collection $collection)
    {
        foreach ($collection as $row) {
            yield $this->arrayToLine((array) $row);
        }
    }

    private function arrayToLine($array)
    {

        // additional handling
        $array['LOCATION'] = $this->joinProposedPorts($array['LOCATION'] ?? []);

        return implode($this->delimiter, $array) . PHP_EOL;
    }


    private function joinProposedPorts($string)
    {
        $decoded_ports = $string;

        // non json string does not contain bracket
        if (is_string($string) && strpos($string, '[') === false) {
            return $string;
        }

        // turn it into assoc array
        while (is_string($decoded_ports)) {
            $decoded_ports = json_decode($decoded_ports, true);
        }

        if( !empty($decoded_ports) ) {
            return collect($decoded_ports)->pluck('NAME')->join('/');
        }

    }

}
