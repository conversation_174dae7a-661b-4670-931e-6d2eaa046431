<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class LoadMatchingExport implements FromArray, WithHeadings, WithStrictNullComparison
{

    use Exportable;

    private $loads;

    public function __construct(array $loads)
    {
        $this->loads = $loads;
    }

    function array(): array
    {
        return $this->loads;
    }

    public function headings(): array
    {
        return [
            "customer",
            "contract",
            "basisx_ex_millb_delivered",
            "location",
            "ticket_no",
            "do_date",
            "consignmentno",
            "driver",
            "vehicle",
            "mill_wt",
            "buyer_wt",
            "contract_status",
            "reason",
        ];
    }
}
