<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Rap2hpoutre\FastExcel\FastExcel;

class DailyLoadDispatchSummary implements FromArray, WithHeadings, WithStrictNullComparison
{
    use Exportable;

    private $loads;
    private $products;

    public function __construct(array $loads, array $products)
    {
        $this->loads = $loads;
        $this->products = $products;
    }

    function array(): array
    {
        return $this->loads;
    }

    public function headings(): array
    {

        $header = [
            'Legal Entity',
            'Profit Center',
            'Inventory Location',
            'Inventory Location Code',
            'Total Dispatched',
        ];

        if (count($this->products) > 0) {
            foreach ($this->products as $product_code => $value) {
                array_push($header, $product_code);
            }
        }

        return $header;
    }
}
