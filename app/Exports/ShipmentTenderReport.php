<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class ShipmentTenderReport implements FromArray, WithHeadings, WithStrictNullComparison
{

    use Exportable;

    private $contracts;

    public function __construct(array $contracts)
    {
        $this->contracts = $contracts;
    }

    function array(): array
    {
        return $this->contracts;
    }

    public function headings(): array
    {
        return [
            'Contract Date',
            'Contract Ref',
            'Profit Center',
            'Counterparty',
            'Broker',
            'Contract Qty',
            'Product',
            'Price',
            'Payment Term',
            'Shipment Month',
            'Contract Basis',
            'Final Dest Country',
            'Vessel',
            'Vessel IMO No.',
            'Load Port',
            'Discharge Port',
            'Load Port ETA',
            'Destination ETA',
            'Tender / BL Qty',
            'BL Date',
            'Tender Created At',
            'Type of Payment',
            'LC No.',
            'LC Received Date',
            'LC Expiry Date',
            'LC Bank',
            'Invoice No.',
            'Invoice Date',
            'Invoice Amount',
        ];
    }
}
