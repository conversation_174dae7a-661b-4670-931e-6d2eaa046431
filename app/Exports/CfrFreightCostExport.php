<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class CfrFreightCostExport implements FromArray, WithHeadings, WithStrictNullComparison, WithColumnFormatting
{

    use Exportable;

    private $billingDocuments;

    public function __construct(array $billingDocuments)
    {
        $this->billingDocuments = $billingDocuments;
    }

    function array(): array
    {
        return $this->billingDocuments;
    }

    public function headings(): array
    {
        return [
            'Document Date',
            'Counterparty',
            'Vessel',
            'BL/Date',
            'Discharge Date',
            'Port of Discharge',
            'Contract',
            'Invoice No.',
            'Invoice Status',
            'Status (CFR)',
            'Batch Reference No',
            'SAP Doc No (Deferred)',
            'Posted On (Deferred)',
            'Remarks (Deferred)',
            'SAP Doc No (Recognized)',
            'Posted On (Recognized)',
            'Remarks (Recognized)',
        ];
    }

    public function columnFormats(): array
    {
        // TODO: Implement columnFormats() method.
        return [
            'A' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'D' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'E' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'M' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'P' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ];
    }
}
