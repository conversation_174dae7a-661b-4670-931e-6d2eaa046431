<?php

namespace App\Exports;

use App\Models\FuturesContract;
use App\Models\OptionsContract;
use App\Models\PhysicalContract;
use App\Models\PricingType;
use App\Models\RubberContract;
use App\Models\NonBmdFuturesContract;
use App\Models\TradingChain;
use App\Models\TransportMode;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class OutstandingContract implements FromArray, WithHeadings, WithEvents, ShouldAutoSize, WithCustomStartCell, WithStrictNullComparison, WithColumnFormatting
{

    const FORMAT_NAME = 'outstanding-overall';

    use Exportable;

    private $contracts;

    public function __construct($contracts)
    {
        $this->contracts = $contracts;
    }

    function array(): array
    {
        $all_contract_ids = $this->contracts->mapToGroups(function($val){
            return [$val->contractable_type => $val->contractable_id];
        });

        $trading_chains = [];

        foreach ( $all_contract_ids as $type => $ids ){

            foreach ( $ids->chunk(1000) as $ids_chunk ){

                $chains = TradingChain::whereIn('contractable_id', $ids_chunk->toArray())
                    ->where('contractable_type', $type)
                    ->get();

                foreach ( $chains as $c ){
                    $trading_chains[$c->contractable_type][$c->contractable_id] = $c->chain_id;
                }
            }

        }

        $trading_chain_data = [];

        foreach ( $trading_chains as $contractable_type => $list ){

            $data = TradingChain::with(['contractable' => function ($query) {
                $query->select('id', 'contract_number');
            }])
            ->whereIn('chain_id', array_values($list))
            ->orderBy('order', 'ASC')->get();

            foreach ( $data as $d ){
                $trading_chain_data[$d->chain_id][] = $d->contractable->contract_number;
            }

        }

        // Determine allocation status for each contracts
        $allocation_status = [];

        foreach ( $this->contracts as $contract ){

            if ( !isset($allocation_status[$contract->contract_number]) ){
                $allocation_status[$contract->contract_number] = [
                    'contract_quantity' => 0,
                    'allocated' => 0,
                    'row_count' => 0
                ];
            }

            $allocation_status[$contract->contract_number]['contract_quantity'] = $contract->quantity;
            $allocation_status[$contract->contract_number]['allocated'] += $contract->allocated_quantity;
            $allocation_status[$contract->contract_number]['row_count']++;

        }

        // keep track if the unallocated qty already displayed at 1 row, dont display it anymore
        $contract_unallocated_qty_finance_displayed = [];

        return $this->contracts->map(function ($contract) use ($trading_chains, $trading_chain_data, $allocation_status, &$contract_unallocated_qty_finance_displayed){

            $port = null;
            $optionalPort = null;

            $trading_chain_details = [];

            // Trying to get the trading chain details
            if ( isset($trading_chains[$contract->contractable_type][$contract->contractable_id]) ){

                // First we resolve to get trading chain ID from contract
                $trading_chain_id = $trading_chains[$contract->contractable_type][$contract->contractable_id];

                // Using the trading chain ID, we get the list of contracts in that chain.
                // Only process if the trading chain has 2 or more contracts
                if ( isset($trading_chain_data[$trading_chain_id]) && count($trading_chain_data[$trading_chain_id]) >= 2 ){

                    $contract_list_in_chain = $trading_chain_data[$trading_chain_id];

                    foreach ( $contract_list_in_chain as $c ){
                        $trading_chain_details[] = $c;
                    }

                }

            }

            $carryForwardLand = 0;
            $carryForwardSea = 0;
            $carryForwardLandFinance = 0;

            // if got multiple rows, only show on 1 row for finance unalloc qty
            $contract_unallocated_qty_finance = $allocation_status[$contract->contract_number]['contract_quantity'] - $allocation_status[$contract->contract_number]['allocated'];

            if ( !isset($contract_unallocated_qty_finance_displayed[$contract->contract_number]) ){
                $contract_unallocated_qty_finance_displayed[$contract->contract_number] = 1;
            }else{
                $contract_unallocated_qty_finance = 0;
            }

            // add proposed discharge
            if ($contract->mode_of_transport == TransportMode::TYPE_SEA) {
                $carryForwardSea = $contract->quantity - $contract->bill_of_ladings_quantity;
            } else {
                if ($contract->allocated_quantity > 0) {
                    $carryForwardLand = $contract->allocated_quantity - $contract->load_invoiceable_quantity;
                } elseif ($contract->contract_load_quantity > 0) {
                    // to fixed TH trading contract
                    // dont have allocation but got loads
                    $carryForwardLand = $contract->quantity - $contract->contract_load_quantity;
                }

                // Finance carry forward numbers
                if ( $contract->allocated_quantity == 0 && $contract->contract_load_quantity == 0 ){
                    $carryForwardLandFinance = $contract->quantity;
                }
                else if ( $contract->allocated_quantity == 0 && $contract->contract_load_quantity > 0 ){
                    $carryForwardLandFinance = $contract->quantity - $contract->contract_load_quantity;
                }
                else{

                    $carryForwardLandFinance = $contract->allocated_quantity - $contract->load_invoiceable_quantity;

                    if ( $allocation_status[$contract->contract_number]['row_count'] == 1 &&
                        $allocation_status[$contract->contract_number]['allocated'] < $allocation_status[$contract->contract_number]['contract_quantity'] ) {

                        $unallocated_quantity = $allocation_status[$contract->contract_number]['contract_quantity'] - $allocation_status[$contract->contract_number]['allocated'];
                        $carryForwardLandFinance += $unallocated_quantity;

                    }else if ( $allocation_status[$contract->contract_number]['row_count'] > 1 &&
                        $allocation_status[$contract->contract_number]['allocated'] < $allocation_status[$contract->contract_number]['contract_quantity'] ) {

                        $carryForwardLandFinance += $contract_unallocated_qty_finance;
                    }

                }

            }

            $ocean_percentage = 0;
            $land_percentage = 0;
            if ($contract->quantity > 0) {
                if ($contract->mode_of_transport == TransportMode::TYPE_SEA) {
                    $ocean_percentage = $contract->quantity > 0 ? round(($contract->bill_of_ladings_quantity / $contract->quantity) * 100, 2) : 0;
                } else {
                    if ($contract->allocated_quantity > 0) {
                        $land_percentage = round(($contract->load_invoiceable_quantity / $contract->allocated_quantity) * 100, 2);
                    } else if ($contract->contract_load_quantity > 0) {
                        // to fixed TH trading contract
                        // dont have allocation but got loads
                        $land_percentage = round(($contract->contract_load_quantity / $contract->quantity) * 100, 2);
                    }
                }
            }

            $fulfill_qty = 0;
            $fulfill_qty_contract = $contract->contract_load_quantity;

            if ($contract->allocated_quantity > 0) {
                $fulfill_qty = $contract->load_invoiceable_quantity;
            } else if ($contract->contract_load_quantity > 0) {
                $fulfill_qty = $contract->contract_load_quantity;
            }

            $deliver_to = '-';

            if (!empty($contract->allocation_deliver_to_name)) {
                $deliver_to = $contract->allocation_deliver_to_name;
            } else {
                $deliver_to = $contract->deliver_to_name;
            }

            $discharge_port = $contract->discharge_port_name;
            if (empty($discharge_port) && !empty($contract->proposed_discharge_ports)) {
                if (is_string($contract->proposed_discharge_ports)) {
                    $to_array = json_decode($contract->proposed_discharge_ports, true);
                    if (is_string($to_array)) {
                        $to_array = json_decode($to_array, true);
                    }
                    $discharge_port = implode(',', Arr::pluck($to_array, 'name'));
                }
            }

            $dispatched_quantity = $contract->dispatched_received_quantity;
            $balance_dispatch_quantity = $contract->allocated_quantity - $dispatched_quantity;

            $contract_number = $contract->contract_number;

            if ($contract->contractable_type == RubberContract::class &&
                $contract->contractable->is_long_term == true) {
                $contract_number .= ' (L)';
            }

            $broker_account_number = $contract->getBrokerAccountNumber();
            $broker_reference = '';

            if(in_array($contract->contractable_type, [FuturesContract::class, NonBmdFuturesContract::class, PhysicalContract::class])) {
                $broker_reference = $contract->broker_reference;
            }

            return [
                $contract->legal_entity_name,
                $contract->profit_center_code,
                $contract->product_code,
                Date::timestampToExcel(Carbon::parse($contract->contract_date)->timestamp),
                $contract->incoterms,
                $contract_number,
                $contract->seller_reference,
                $contract->buyer_reference,
                $contract->transaction_type == 'S' ? $contract->profit_center_name : $contract->counterparty_long_name, // TODO: please check on their sample looks like using legal entity name
                $contract->transaction_type == 'S' ? $contract->counterparty_long_name : $contract->profit_center_name,
                Carbon::parse($contract->shipment_month)->format('M Y'),
                Date::timestampToExcel(strtotime(Carbon::parse($contract->shipment_date_from)->toDateString())),
                Date::timestampToExcel(strtotime(Carbon::parse($contract->shipment_date_to)->toDateString())),
                $deliver_to,
                $discharge_port,
                $contract->currency,
                $contract->base_price - $contract->discount + $contract->premium,
                $contract->pricing_status == PricingType::STATUS_FULLY_PRICED ? $contract->fixed_price : $contract->base_price,
                strtoupper(in_array($contract->contractable_type, [FuturesContract::class, OptionsContract::class, NonBmdFuturesContract::class]) ? PricingType::TYPE_OUTRIGHT : $contract->pricing_type),
                $contract->inventory_location_region ?? 'Unallocated',
                $contract->inventory_location_name ?? 'Unallocated',
                $contract->quantity,
                $contract->allocated_quantity,
                $contract->allocated_quantity,
                $fulfill_qty,
                $carryForwardLand,
                $land_percentage,
                $contract->bill_of_ladings_quantity ?? 0,
                $carryForwardSea,
                $ocean_percentage,
                $contract->trader_name,
                $contract->forex_rate,
                $contract->rspo_status,
                $dispatched_quantity,
                $balance_dispatch_quantity,
                $fulfill_qty_contract,
                $contract_unallocated_qty_finance,
                $carryForwardLandFinance,
                $trading_chain_details[0] ?? null,
                $trading_chain_details[1] ?? null,
                $trading_chain_details[2] ?? null,
                $trading_chain_details[3] ?? null,
                $contract->specification_group_name ?? '-',
                $contract->packing_unit ?? '-',
                $contract->payment_terms_name ?? '-',
                $broker_reference,
                $broker_account_number,
                $contract->pricing_status == PricingType::STATUS_FULLY_PRICED ? ($contract->fixed_price - $contract->discount + $contract->premium) : 0,
                $contract->contract_term ?? null
            ];

        })->toArray();
    }

    public function headings(): array
    {
        return [
            'Legal Entity',
            'Profit Center',
            'Commodity',
            'Ctr Date',
            'Ctr Basis',
            'Ctr Reference',
            'Seller Ref',
            'Buyer Ref',
            'Seller',
            'Buyer',
            'Ship Month',
            'Ship Start Date',
            'Ship End Date',
            'Deliver To',
            'Optional Port',
            'Currency',
            'Prov. Price Include PBP',
            'Price',
            'Price Type',
            'Region',
            'Stock Location',
            'Contract Qty',
            'Alloc Qty',
            'Released Qty',
            'Fulfill Qty',
            'CF (Local)',
            '% Complete (Local)',
            'BL Qty',
            'CF (Ocean)',
            '% Complete (Ocean)',
            'Trader',
            'Forex Rate',
            'RSPO status',
            'Dispatched Qty (MT)',
            'Balance Dispatch Qty (MT)',
            'Fulfill Qty (Ctr)',
            'Contract Unallocated Qty (Finance)',
            'CF (Finance)',
            'Trading Chain 1st Ctr',
            'Trading Chain 2nd Ctr',
            'Trading Chain 3rd Ctr',
            'Trading Chain 4th Ctr',
            'Product Specification',
            'Packing Unit',
            'Payment Term',
            'Broker Ref',
            'Broker Account Number',
            'Fixed Price Include PBP',
            'Contract Term'
        ];
    }

    public function startCell(): string
    {
        return 'A2';
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event){
                $event->sheet->getDelegate()->mergeCells('A1:AW1');
                $cell = $event->sheet->getDelegate()->getCell('A1');

                $cell->setValue('OVERALL OUTSTANDING CONTRACT');
                $cell->getStyle()->applyFromArray(
                    [
                        'font' => [
                            'bold' => true,
                            'size' => 18
                        ],
                        'alignment' => [
                            'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                        ],
                    ]
                );

                // Bold header
                $event->sheet->getStyle('A2:AW2')->applyFromArray([
                    'font' => [
                        'bold' => true
                    ]
                ]);
            }
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'K' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'L' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'M' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'AT' => NumberFormat::FORMAT_TEXT,
            'AU' => NumberFormat::FORMAT_TEXT,
        ];
    }
}
