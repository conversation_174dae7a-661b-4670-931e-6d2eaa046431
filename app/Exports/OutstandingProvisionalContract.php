<?php

namespace App\Exports;

use App\Helpers\NumberHelper;
use App\Models\FuturesContract;
use App\Models\Incoterms;
use App\Models\TransportMode;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Events\AfterSheet;

class OutstandingProvisionalContract implements FromArray, WithHeadings, WithEvents, ShouldAutoSize, WithCustomStartCell, WithStrictNullComparison
{

    const FORMAT_NAME = 'outstanding-provisional-price-fixing';

    use Exportable;

    private $contracts;

    public function __construct($contracts)
    {
        $this->contracts = $contracts;
    }

    function array(): array
    {
        return $this->contracts->map(function ($contract) {

            if ($contract->mode_of_transport == TransportMode::TYPE_SEA) {
                $fulfilled_qty = $contract->bill_of_ladings_quantity;
            } else {
                $fulfilled_qty = $contract->load_invoiceable_quantity;
            }
            return [
                $contract->legal_entity_name,
                $contract->profit_center_code,
                Carbon::parse($contract->shipment_month)->format('M Y'),
                Carbon::parse($contract->contract_date)->format('d-M-Y'),
                $contract->contract_number,
                ucfirst($contract->status),
                $contract->seller_reference,
                $contract->buyer_reference,
                $contract->currency,
                round($contract->base_price + $contract->premium - $contract->discount, 2),
                $contract->pricing_type,
                $contract->quantity,
                $contract->unpriced_quantity,
                $contract->priced_quantity,
                $fulfilled_qty,
                $contract->loads->filter(function($l) { return $l->isBilled(); })->sum(function ($b) {
                    if ($b->adjusted_quantity) {
                        return $b->adjusted_quantity;
                    }
                    return $b->invoiceable_quantity;
                }),
                $contract->trader_name,
            ];

        })->toArray();
    }

    public function headings(): array
    {
        return [
            'Legal Entity',
            'Profit Center',
            'Shipment Month',
            'Contract Date',
            'Contract Ref',
            'Contract Status',
            'Seller Ref',
            'Buyer Ref',
            'Contract Currency',
            'Provisional Price',
            'Pricing Type',
            'Contract Qty',
            'Unpriced Ctr Qty',
            'Priced Ctr Qty',
            'Fulfilled Qty',
            'Invoiced Qty',
            'Trader',
        ];
    }

    public function startCell(): string
    {
        return 'A2';
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event){
                $event->sheet->getDelegate()->mergeCells('A1:Q1');
                $cell = $event->sheet->getDelegate()->getCell('A1');

                $cell->setValue('OUTSTANDING PROVISIONAL PRICE FIXING REPORT');
                $cell->getStyle()->applyFromArray(
                    [
                        'font' => [
                            'bold' => true,
                            'size' => 18
                        ],
                        'alignment' => [
                            'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                        ],
                    ]
                );

                // Bold header
                $event->sheet->getStyle('A2:Q2')->applyFromArray([
                    'font' => [
                        'bold' => true
                    ]
                ]);
            }
        ];
    }
}
