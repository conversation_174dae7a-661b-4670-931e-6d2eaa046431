<?php

namespace App\Exports;

use App\Helpers\NumberHelper;
use App\Models\FuturesContract;
use App\Models\Incoterms;
use App\Models\TransportMode;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Events\AfterSheet;

class OutstandingStockTransferContract implements FromArray, WithHeadings, WithEvents, ShouldAutoSize, WithCustomStartCell, WithStrictNullComparison
{

    const FORMAT_NAME = 'outstanding-stock-transfer';

    use Exportable;

    private $contracts;

    public function __construct($contracts)
    {
        $this->contracts = $contracts;
    }

    function array(): array
    {
        return $this->contracts->map(function ($contract) {

            $headerContract = $contract->contractable->stockTransferTicketHeader;

            if ( $headerContract == null ){
                return [];
            }

            $oppositeContract = $headerContract->stockTransferContracts->filter(function($c) use ($contract) { return $c->contract_number != $contract->contract_number; })->first();

            return [
                $contract->product_code,
                Carbon::parse($contract->shipment_month)->format('M Y'),
                Carbon::parse($contract->shipment_date_from)->tz(Auth::user()->timezone)->format('M d, Y'),
                Carbon::parse($contract->shipment_date_to)->tz(Auth::user()->timezone)->format('M d, Y'),
                $contract->contract_header_reference,
                $contract->contract_number,
                $oppositeContract->contract_number,
                Carbon::parse($contract->contract_date)->format('M d, Y'),
                $contract->profit_center_name,
                $contract->counterparty_name,
                null,
                Carbon::parse($contract->contract_date)->format('M d, Y'),
                $contract->inventory_location_region ? strtoupper($contract->inventory_location_region) : 'Unallocated',
                $contract->inventory_location_name ?? 'Unallocated',
                $contract->allocation_deliver_to_name ?? 'Unallocated',
                $contract->incoterms,
                $contract->load_port_name,
                $contract->discharge_port_name,
                $contract->currency,
                null,
                $contract->quantity,
                $contract->allocated_quantity,
                $contract->load_invoiceable_quantity,
                $contract->allocated_quantity - $contract->load_invoiceable_quantity,
                $contract->quantity > 0 ? round($contract->load_invoiceable_quantity / $contract->quantity * 100 ,2)  : 0,
                ucfirst($contract->status),
                $contract->trader_name,
            ];

        })->toArray();
    }

    public function headings(): array
    {
        return [
            'Commodity',
            'Shipment Month',
            'Ship Start Date',
            'Ship End Date',
            'Deal Ticket Ref',
            'Short Ticket Ref',
            'Long Ticket Ref',
            'Ctr Date',
            'Seller',
            'Buyer',
            'Movt Order Ref',
            'Order Date',
            'Region',
            'Location From',
            'Location To',
            'INCOTERM',
            'Load Port',
            'Discharge Port',
            'Currency',
            'Price',
            'Contract Qty',
            'Alloc Qty',
            'Fulfill Qty',
            'Outstanding Qty',
            '% Complete',
            'Status',
            'Trader',
        ];
    }

    public function startCell(): string
    {
        return 'A2';
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event){
                $event->sheet->getDelegate()->mergeCells('A1:AA1');
                $cell = $event->sheet->getDelegate()->getCell('A1');

                $cell->setValue('OVERALL OUTSTANDING STOCK TRANSFERS');
                $cell->getStyle()->applyFromArray(
                    [
                        'font' => [
                            'bold' => true,
                            'size' => 18
                        ],
                        'alignment' => [
                            'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                        ],
                    ]
                );

                // Bold header
                $event->sheet->getStyle('A2:AA2')->applyFromArray([
                    'font' => [
                        'bold' => true
                    ]
                ]);
            }
        ];
    }
}
