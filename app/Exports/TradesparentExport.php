<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

/**
 * ! Not used anymore due to Tradesparent requires tab delimited file  without enclosure but with extension called .csv
 */
class TradesparentExport implements FromCollection, WithHeadings, WithStrictNullComparison, WithCustomCsvSettings
{
    use Exportable;

    private $viewName;

    public function __construct($viewName)
    {
        $this->viewName = $viewName;
    }

    public function collection()
    {
        return DB::table($this->viewName)->get();
    }

    public function headings(): array
    {
        return [
            'CXC_EXTRACT',
            'CONTRACT_ID',
            'CONTRACT_REF',
            'CONTRACT_DATE',
            'CONTRACT_TYPE',
            'LEGAL_ENTITY',
            'COMPANY',
            'PROFIT_CENTER',
            'LONG_SHORT',
            'COMMODITY',
            'PRODUCT',
            'INCOTERM',
            'LOCATION',
            'PERIOD',
            'SHIPMENT_START',
            'SHIPMENT_END',
            'CONTRACT_QUANTITY',
            'ALLOCATED_QUANTITY',
            'FULFILLED_QUANTITY',
            'TOLERANCE_QUANTITY',
            'LOTS_QUANTITY',
            'VALUATION_PRICE',
            'ORIGINAL_PRICE',
            'PROVISIONAL_PRICE',
            'DISCOUNT_VALUE',
            'VALUATION_CURRENCY',
            'ORIGINAL_CURRENCY',
            'EXCHANGE_RATE',
            'UOM',
            'CONTRACT_STATUS',
            'CONTRACT_TERM',
            'COUNTERPARTY',
            'COUNTERPARTY_INTERNAL',
            'BROKER',
            'BROKER_ACCOUNT',
            'TRADER_NAME',
            'VESSEL',
            'NOMINATION_STATUS',
            'NOMINAITON_DATE',
            'PAYMENT_TERM',
            'PACKING_UNIT',
            'PAYMENT_STATUS',
            'TRADE_TYPE',
            'PRICING_TYPE',
            'PROV_PRICE_INDEX_NAME',
        ];
    }

    public function getCsvSettings(): array
    {
        return [
            'enclosure' => ''
        ];
    }
     
    
}
