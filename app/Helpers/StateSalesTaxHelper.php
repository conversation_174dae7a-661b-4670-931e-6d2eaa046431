<?php

namespace App\Helpers;

use App\Interfaces\Contractable;
use App\Models\BillingDocument;
use App\Models\BillOfLading;
use App\Models\PhysicalLoad;
use App\Models\PhysicalLoadAdjustment;
use App\Repositories\PriceIndexRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class StateSalesTaxHelper
{

    /**
     * Function to calculate SST amount Per MT for PK
     */
    public static function calculatePkSstPerUnit($shipment_month)
    {
        $sst = null;

        $from_shipment_month = Carbon::parse($shipment_month, Auth::user()->timezone)->startOfMonth()->tz('UTC');
        $to_shipment_month = Carbon::parse($shipment_month, Auth::user()->timezone)->endOfMonth()->tz('UTC');

        // Takes in CPKO price index
        $price_index = (new PriceIndexRepository())->getPriceIndexForPkSstCalculation();

        if (empty($price_index)) {
            throw new \Exception('Please set up CPKO price index for SST calculation');
        }

        // Gets shipment month average
        $price = $price_index->entries()->where('delivery_start_date', $from_shipment_month->toDateTimeString())
            ->where('delivery_end_date', $to_shipment_month->toDateTimeString())
            ->first();

        if ($price == null) {
            throw new \Exception('CPKO price index not found for SST calculation.');
        }

        $cpko_price = $price->settle;

        // Calculate SST
        $sst_pmt = round(($cpko_price * 0.05 * 0.435) / 1.05, 2);

        return [
            'sst_pmt' => $sst_pmt,
            'cpko_price' => $cpko_price
        ];
    }

    public static function getSSTSettlementDetails(Contractable $contract, $tax = null, $inventory_location_id = null)
    {

        $billing_documents = $contract->billingDocuments;

        $total_quantity = 0;

        $invoices = $billing_documents->filter(function ($value) {
            return $value->type == BillingDocument::TYPE_NORMAL_INVOICE && $value->status != BillingDocument::INVOICE_STATUS_VOIDED;
        })->loadMissing('lineItems.billable');

        $debit_notes = $billing_documents->filter(function ($value) {
            return $value->type == BillingDocument::TYPE_DEBIT_NOTE && $value->status != BillingDocument::INVOICE_STATUS_VOIDED;
        })->loadMissing('lineItems.billable');

        $credit_notes = $billing_documents->filter(function ($value) {
            return $value->type == BillingDocument::TYPE_CREDIT_NOTE && $value->status != BillingDocument::INVOICE_STATUS_VOIDED;
        })->loadMissing('lineItems.billable');

        $loads_with_adjustments = collect([]);

        foreach ($invoices as $invoice) {
            foreach ($invoice->lineItems as $line_item) {

                $invoice_inventory_location_id = !empty($line_item->billable->inventory_location_id)
                    ? $line_item->billable->inventory_location_id
                    : (!empty($line_item->inventory_location_id) ? $line_item->inventory_location_id  : (isset($invoice->inventory_location_id) ? $invoice->inventory_location_id : -1));

                // if inventory location is provided, but doesnt match current invoice inventory location, ignore
                if ( $inventory_location_id != null && $invoice_inventory_location_id != $inventory_location_id ){
                    continue;
                }

                if ($line_item->billable_type == get_class(new PhysicalLoad)) {
                    $quantity = !empty($line_item->billable->adjusted_quantity) ? $line_item->billable->adjusted_quantity : $line_item->billable->invoiceable_quantity;
                    $total_quantity = $total_quantity + $quantity;

                    if ( !empty($line_item->billable->adjusted_quantity) ) {
                        $loads_with_adjustments->push($line_item->billable_id);
                    }

                } else if ($line_item->billable_type == get_class(new BillOfLading)) {
                    $quantity = $line_item->billable->quantity;
                    $total_quantity = $total_quantity + $quantity;
                }
            }
        }

        foreach ($debit_notes as $debit_note) {
            foreach ($debit_note->lineItems as $line_item) {

                // if inventory location is provided, but doesnt match current invoice inventory location, ignore
                if ( $inventory_location_id != null && $debit_note->inventory_location_id != $inventory_location_id ){
                    continue;
                }

                // If adjusted quantity is billed from invoice then ignore
                if ($line_item->billable_type == get_class(new PhysicalLoadAdjustment) && !$loads_with_adjustments->contains($line_item->billable->load_id)) {
                    $total_quantity = $total_quantity + $line_item->billable->adjusted_quantity;
                }
            }
        }

        foreach ($credit_notes as $credit_note) {
            foreach ($credit_note->lineItems as $line_item) {

                // if inventory location is provided, but doesnt match current invoice inventory location, ignore
                if ( $inventory_location_id != null && $credit_note->inventory_location_id != $inventory_location_id ){
                    continue;
                }

                // If adjusted quantity is billed from invoice then ignore
                if ($line_item->billable_type == get_class(new PhysicalLoadAdjustment) && !$loads_with_adjustments->contains($line_item->billable->load_id)) {
                    $total_quantity = $total_quantity + $line_item->billable->adjusted_quantity;
                }
            }
        }

        $temp = self::calculatePkSstPerUnit($contract->shipment_month);

        $cpko_price = $temp['cpko_price'];
        $sst_pmt = $temp['sst_pmt'];
        $sst_amount = 0 - round($total_quantity * $sst_pmt, 2);
        $tax_percentage = 0 - !empty($tax) ? $tax->percentage : 0;
        $tax_amount = 0 - round($sst_amount * $tax_percentage / 100, 2);
        $sst_amount_after_tax = $sst_amount + $tax_amount;

        return [
            'cpko_price' => $cpko_price,
            'sst_value' => $sst_pmt,
            'total_quantity' => $total_quantity,
            'sst_amount' => $sst_amount,
            'tax_percentage' => $tax_percentage,
            'tax_amount' => $tax_amount,
            'sst_amount_after_tax' => $sst_amount_after_tax,
        ];
    }
}
