<?php

namespace App\Helpers;

use App\Interfaces\Contractable;
use App\Models\ContractStatus;
use App\Models\Counterparty;
use App\Models\ExternalSystem;
use App\Models\IntegrationReference;
use App\Models\InventoryLocation;
use App\Models\LegalEntity;

class InventoryLocationHelper
{

    /**
     * This helper is to retrieve the list of inventory location that can be accessed by the contract,
     * which resolves the inventory location belong to its profit center, and inventory location with unknown
     * profit center but belongs to the same legal entity
     *
     * @return Collection
     */
    public static function getInventoryLocationWithDetachedProfitCenter(Contractable $contract, $exclude_bulking = false)
    {
        $profitCenter = $contract->profitCenter;

        if (!$profitCenter) {
            throw new \Exception("Contract does not have profit center, unable to find inventory location");
        }

        $legalEntity = $contract->legalEntity;

        if (!$legalEntity) {
            throw new \Exception("Contract does not have legal entity, unable to find inventory location");
        }

        // For credit limit check flow, and invloc ID is selected in contract non-core fields
        // only apply this when contract is confirmed to allow user to change inventory location when contract is still posted/draft
        if (ConfigHelper::shouldContractUseCreditLimitCheckFlow($contract) && $contract->inventory_location_id !== null && $contract->status === ContractStatus::TYPE_CONFIRMED ) {
            return InventoryLocation::where('id', $contract->inventory_location_id)->get();
        }

        $profitCenterInventoryLocations = $profitCenter->inventoryLocations;

        $inventory_location_query = InventoryLocation::query();

        $inventory_location_query
            ->whereNotIn('id', $profitCenterInventoryLocations->pluck('id')->toArray());

        // * if is minamas, can cross legal entity
        // * 2020-12-07 - update from user, they now want to limit back to their own legal entity instead
        // if ($legalEntity->is_minamas) {
        //     $minamas_legal_entities = LegalEntity::whereHas('businessUnit', function($businessUnitQuery) use ($legalEntity) {
        //         return $businessUnitQuery->where('id', $legalEntity->business_unit_id);
        //     })->get();

        //     $inventory_location_query->whereIn('legal_entity_id', $minamas_legal_entities->pluck('id')->toArray());
        // }

        $inventory_location_query->where('legal_entity_id', $legalEntity->id);

        $legalEntityInventoryLocations = $inventory_location_query->get();

        $inventoryLocations = $profitCenterInventoryLocations->concat($legalEntityInventoryLocations);

        if(ConfigHelper::shouldContractUseCreditLimitCheckFlow($contract) && $exclude_bulking){
            $inventoryLocations = $inventoryLocations->where('is_bulking', false)->values();
        }

        return $inventoryLocations->unique('id')->sortBy('name');

    }

    public static function findContractInventoryLocationFromIntegrationReference(Contractable $contract, $systemCode, $integrationReferenceValue)
    {
        $allAvailableInventoryLocations = self::getInventoryLocationWithDetachedProfitCenter($contract);

        $externalSystem = ExternalSystem::code($systemCode)->first();

        if (!$externalSystem) {
            throw new \Exception("$systemCode is not a valid External System");
        }

        $possibleInventoryLocationsReferences = IntegrationReference::where('integratable_type', InventoryLocation::class)
            ->where('type', 'code')
            ->where('reference_no', $integrationReferenceValue)
            ->where('external_system_id', $externalSystem->id)->get();

        if ($possibleInventoryLocationsReferences->count() == 0) {
            return null;
        }

        // inventory location with similar reference_no
        $possibleInventoryLocations = $possibleInventoryLocationsReferences->map(function($ref){ return $ref->integratable; });

        // inventory location which is shortlisted on what the contract can pick
        $shortListedInventoryLocations = $possibleInventoryLocations->filter(function($ivloc) use ($allAvailableInventoryLocations) {
            return $allAvailableInventoryLocations->contains('id', $ivloc->id);
        });

        // can confirm only 1 means its that ivloc
        if ($shortListedInventoryLocations->count() == 1) {
            return $shortListedInventoryLocations->first();
        }

        // meaning duplicated integration refrences, which we need to check what was used for allocation to back trace
        if ($shortListedInventoryLocations->count() > 1) {

            $allocationsWithMatchingInventoryLocation = $contract->allocations->filter(function($allocation) use ($possibleInventoryLocations) {
                return $possibleInventoryLocations->contains('id', $allocation->inventory_location_id);
            });

            $allocationWithMatchingInventoryLocation =  $allocationsWithMatchingInventoryLocation->groupBy('inventory_location_id')->filter(function($groupedAllocations){
                return $groupedAllocations->sum('quantity') > 0;
            })->flatten()->first();

            return $allocationWithMatchingInventoryLocation ? $allocationWithMatchingInventoryLocation->mill : null;
        }

        return null;

    }

    public static function getInventoryLocationsForProfitCenter(Counterparty $profitCenter)
    {
        $profitCenterInventoryLocations = $profitCenter->inventoryLocations;
        $legalEntityInventoryLocations = $profitCenter->legalEntity->inventoryLocations;

        return $profitCenterInventoryLocations
            ->concat($legalEntityInventoryLocations)
            ->unique(function($ivlocation) {
                return $ivlocation->id;
            });
    }


}
