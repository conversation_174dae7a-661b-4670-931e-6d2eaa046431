<?php

namespace App\Helpers;

use App\Interfaces\Contractable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class SnapshotHelper{
    public static function serializeContractable(Contractable $ctr) {
        return self::build($ctr);
    }

    public static function serializeModel(Model $model) {
        return self::build($model);
    }

    public static function unserialize($data) {
        return self::instance($data);

    }

    public static function unserializeArray($data) {
        return collect($data)->map(function($entry) {
            return self::instance($entry);
        });
    }

    public static function unserializeJson($json) {
        $data = json_decode($json);

        return self::unserialize($data);
    }

    public static function unserializeJsonArray($json) {
        $data = json_decode($json);

        return self::unserializeArray($data);
    }

    private static function instance($entry) {
        $entry = optional($entry);

        $class_fqn = $entry->class;


        $relations = collect(array_keys((array) $entry->data))->filter(function($property) use ($entry) {

            if (is_object($entry->data->$property)) {

                // options contract cast it to object
                if ($property == 'metadata') {
                    return false;
                }
                unset($entry->data->$property);
                return true;
            }

            return false;
        })->map(function($relation_name) use ($class_fqn) {
            $camelCase = Str::camel($relation_name);

            if( method_exists($class_fqn, $camelCase) ) {
                return $camelCase;
            }

            return $relation_name;
        })
        ->filter(function($relation_name) {
            return !($relation_name === 'buyer' || $relation_name === 'seller');
        });

        $class_fqn::unguard();

        $result = new $class_fqn((array) $entry->data);

        $class_fqn::reguard();

        $result->load($relations->toArray());

        return $result;
    }

    private static function build($instance) {
        return collect([
            'class' => get_class($instance),
            'data' => $instance->toArray(),
        ]);
    }
}
