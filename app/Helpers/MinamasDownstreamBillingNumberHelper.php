<?php

namespace App\Helpers;

use App\Interfaces\BillingNumberInterface;
use App\Models\BillingDocument;
use App\Models\PhysicalContract;
use App\Services\ContractNumberService;
use Carbon\Carbon;

class MinamasDownstreamBillingNumberHelper implements BillingNumberInterface
{
    private $contractNumberService;

    const TYPE_TO_FUNCTION = [
        BillingDocument::TYPE_CREDIT_NOTE => 'getCreditNoteFormat',
        BillingDocument::TYPE_ADVANCE_CREDIT_NOTE => 'getCreditNoteFormat',
        BillingDocument::TYPE_DEBIT_NOTE => 'getDebitNoteFormat',
        BillingDocument::TYPE_ADVANCE_INVOICE => 'getInvoiceFormat',
        BillingDocument::TYPE_NORMAL_INVOICE => 'getInvoiceFormat',
        BillingDocument::TYPE_PROFORMA_INVOICE => 'getInvoiceFormat',
        BillingDocument::TYPE_PAYMENT_VOUCHER => 'getPaymentVoucherFormat',
        BillingDocument::TYPE_ADVANCE_PAYMENT_VOUCHER => 'getAdvancePaymentVoucherFormat',
    ];

    const RUNNING_NUMBER_DIGITS = 5;

    public function __construct(ContractNumberService $contractNumberService)
    {
        $this->contractNumberService = $contractNumberService;
    }

    public function generateBillingNumber(PhysicalContract $contract, string $billing_document_type, Carbon $documentDate, $ar_ap_overwrite = null): string
    {
        $legal_entity = $contract->legalEntity;
        $original_billing_document_type = $billing_document_type;

        // Proforma invoice use invoice running number
        if ( $billing_document_type == BillingDocument::TYPE_PROFORMA_INVOICE || $billing_document_type == BillingDocument::TYPE_ADVANCE_INVOICE ){
            $billing_document_type = BillingDocument::TYPE_NORMAL_INVOICE;
            $original_billing_document_type = $billing_document_type;
        }
        if ($billing_document_type == BillingDocument::TYPE_ADVANCE_CREDIT_NOTE) {
            $billing_document_type = BillingDocument::TYPE_CREDIT_NOTE;
            $original_billing_document_type = $billing_document_type;
        }
        if ($billing_document_type == BillingDocument::TYPE_ADVANCE_PAYMENT_VOUCHER) {
            $billing_document_type = BillingDocument::TYPE_PAYMENT_VOUCHER;
            $original_billing_document_type = BillingDocument::TYPE_ADVANCE_PAYMENT_VOUCHER;
        }

        $runningNumber = $this
            ->contractNumberService
            ->allocateContractNumber(false, $billing_document_type, null, null,
                null, null, $legal_entity->id, self::RUNNING_NUMBER_DIGITS);

        return $this->{$this->typeToFunctionMap($original_billing_document_type)}($contract, $runningNumber, $documentDate);
    }

    public function typeToFunctionMap(string $type): string
    {
         if (empty(self::TYPE_TO_FUNCTION[$type])) {
            throw new \Exception('Minamas doesn\'t support billing this document type yet');
        }

        return self::TYPE_TO_FUNCTION[$type];
    }

    public function getCreditNoteFormat(PhysicalContract $contract, string $runningNumber, Carbon $documentDate): string
    {
        $isLocalInternational = $contract->is_international ? 'E' : 'L';

        return "{$contract->legalEntity->code}/ARCN-{$runningNumber}/{$isLocalInternational}";
    }

    public function getDebitNoteFormat(PhysicalContract $contract, string $runningNumber, Carbon $documentDate): string
    {
        $isLocalInternational = $contract->is_international ? 'E' : 'L';

        return "{$contract->legalEntity->code}/ARDN-{$runningNumber}/{$isLocalInternational}";
    }

    public function getInvoiceFormat(PhysicalContract $contract, string $runningNumber, Carbon $documentDate): string
    {
        $isLocalInternational = $contract->is_international ? 'E' : 'L';

        return "{$contract->legalEntity->code}/INV-{$runningNumber}/{$isLocalInternational}";
    }

    public function getPaymentVoucherFormat(PhysicalContract $contract, string $runningNumber, Carbon $documentDate): string
    {
        $isLocalInternational = $contract->is_international ? 'E' : 'L';

        return "{$contract->legalEntity->code}/VCH-{$runningNumber}/{$isLocalInternational}";
    }

    public function getAdvancePaymentVoucherFormat(PhysicalContract $contract, string $runningNumber, Carbon $documentDate): string
    {
        $isLocalInternational = $contract->is_international ? 'E' : 'L';

        return "{$contract->legalEntity->code}/ADVVCH-{$runningNumber}/{$isLocalInternational}";
    }
}
