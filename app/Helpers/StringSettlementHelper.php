<?php

namespace App\Helpers;

use App\Factories\BillingDocumentFactory;
use App\Helpers\ContractNumberHelper;
use App\Models\Account;
use App\Models\BillingDocument;
use App\Models\Contract;
use App\Models\ContractExternal;
use App\Models\ContractString;
use App\Models\PhysicalContract;
use App\Repositories\BankAccountRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class StringSettlementHelper
{
    public static function getLowestPriceInString($contracts)
    {
        $lowest_price = null;

        foreach ($contracts as $contract) {

            if (empty($lowest_price)) {
                $lowest_price = $contract->getContractPrice();
                continue;
            }

            if ($contract->getContractPrice() < $lowest_price) {
                $lowest_price = $contract->getContractPrice();
            }
        }

        return $lowest_price;
    }

    public static function appendBypassData($input)
    {
        $referable_contract = $input['referable_contract'];

        $bypass_data = [];
        if (!empty($input['opposite_contract']) && get_class($input['opposite_contract']) != ContractExternal::class) {
            if ($referable_contract->transaction_type === PhysicalContract::TYPE_PURCHASE) {
                $bypass_data['profit_center_id'] = $referable_contract->counterparty_id;
                $bypass_data['bank_accounts'] = $referable_contract->counterparty->bankAccounts;
            } else {
                $bypass_data['profit_center_id'] = $referable_contract->profit_center_id;
                $bypass_data['bank_accounts'] = $referable_contract->profitCenter->bankAccounts;
            }

            $bill_to = null;
            if ($input['contract_position'] === ContractString::POSITION_HEAD) {
                if ($input['opposite_contract']->transaction_type == PhysicalContract::TYPE_PURCHASE) {
                    $bill_to = $input['opposite_contract']->profitCenter;
                } else {
                    $bill_to = $input['opposite_contract']->counterparty;
                }
            } else {
                if ($input['opposite_contract']->transaction_type == PhysicalContract::TYPE_PURCHASE) {
                    $bill_to = $input['opposite_contract']->counterparty;
                } else {
                    $bill_to = $input['opposite_contract']->profitCenter;
                }
            }
            $bypass_data['bill_to_id'] = $bill_to->id;
            $bypass_data['bill_to_name'] = $bill_to->long_name ?? $bill_to->name;
            $bypass_data['bill_to_address'] = $bill_to->address;
        }

        $input['bypass_data'] = $bypass_data;
    }

    public static function prepareBillingDocument(&$input, $settlement_contracts)
    {
        $data = [];
        self::eInvoicingDocumentTypeUpdate($input);

        $referable_contract = $input['referable_contract'];

        if (!empty($input['opposite_contract'])) {
            $input['bypass_data'] = self::appendBypassData($input);
        }

        $primary_bank_account = null;
        $bank_accounts = !empty($input['bypass_data']) && !empty($input['bypass_data']['bank_accounts']) ? $input['bypass_data']['bank_accounts'] : [];

        foreach ($bank_accounts as $account) {
            if ($account->is_primary && $account->currency == $referable_contract->currency) {
                $primary_bank_account = $account;
            }
        }

        $data['type'] = $input['document_type'];
        $data['contractable_type'] = $referable_contract->getClass();
        $data['contractable_id'] = $referable_contract->id;
        $data['profit_center_id'] = !empty($input['bypass_data']) ? $input['bypass_data']['profit_center_id'] : $referable_contract->profit_center_id;
        $data['status'] = BillingDocument::INVOICE_STATUS_DRAFT;

        $ar_ap_overwrite = BillingDocument::ACCOUNT_AR;
        $data['reference_number'] = ContractNumberHelper::generateInvoiceNumber($referable_contract, $input['document_type'], $ar_ap_overwrite); // force all string to use AR prefix

        $data['document_date'] = Carbon::now(Auth::user()->timezone)->startOfDay()->tz('UTC');
        $data['currency'] = $referable_contract->currency;
        $data['forex_rate'] = $referable_contract->forex_rate;
        $data['created_by'] = Auth::user()->id;

        $data['contract_reference_no'] = $referable_contract->contract_number ?? null;
        if ($referable_contract->root_id != null) {
            $data['internal_contract_reference_no'] = $referable_contract->root->contract_number;
        } else {
            $data['internal_contract_reference_no'] = $referable_contract->contract_number ?? null;
        }

        $counterparty = $referable_contract->counterparty;

        $data['bill_to_id'] = !empty($input['bypass_data']) ? $input['bypass_data']['bill_to_id'] : $counterparty->id;
        $data['bill_to_name'] = !empty($input['bypass_data']) ? $input['bypass_data']['bill_to_name'] : $counterparty->long_name;
        $data['bill_to_address'] = !empty($input['bypass_data']) ? $input['bypass_data']['bill_to_address'] : $counterparty->address;

        $data['unit_price'] = $input['settlement_price'];

        $data['price_uom'] = $referable_contract->base_price_uom ? $referable_contract->base_price_uom : ($referable_contract->quantity_uom ?? 'MT');
        $data['transport_mode'] = $referable_contract->mode_of_transport;

        // use bill_to / who should receive money to resolve primary bank account instead
        if (empty($primary_bank_account)) {
            $bankAccountRepository = new BankAccountRepository();

            if ( in_array($data['type'], BillingDocument::INVOICE_TYPES_AR_RECEIVE_MONEY) ){
                $receiving_money_counterparty = $referable_contract->profit_center_id;
            }else{
                $receiving_money_counterparty = $data['bill_to_id'];
            }

            $primary_bank_account = $bankAccountRepository->getPrimaryBankAccount($receiving_money_counterparty, $data['currency']);
        }

        if (empty($primary_bank_account)) {
            throw new \Exception('No primary bank account found for ' . $referable_contract->contract_number);
        }

        $data['bank_account_id'] = $primary_bank_account->id;
        $data['bank_name'] = $primary_bank_account->bank_name;
        $data['bank_account_name'] = $primary_bank_account->account_name;
        $data['bank_account_number'] = $primary_bank_account->account_number;
        $data['bank_address'] = $primary_bank_account->bank_address;

        //2022-12-02 payment terms code ported from SDOZ, need to do further testing to enable.
        /*
        if ( isset($input['payment_terms']) ){
            $data['payment_terms_id'] = $input['payment_terms']['payment_terms_id'];
            $data['payment_terms_percentage'] = $input['payment_terms']['payment_terms_percentage'];
            $data['payment_terms_description'] = $input['payment_terms']['payment_terms_description'];

            if ( isset($input['payment_due_date']) ){
                $data['payment_due_date'] = $input['payment_due_date'];
            }else{
                $data['payment_due_date'] = $data['document_date']->clone()->addDays($input['payment_terms']['payment_terms_days'])->toDateString();
            }
        }*/

        $data['amount_before_tax'] = $input['amount_before_tax'];
        $data['tax_percentage'] = $input['tax_percentage'];
        $data['tax_amount'] = $input['tax_amount'];
        $data['amount_after_tax'] = $input['amount_after_tax'];
        $data['tax_id'] = $input['tax_id'];

        $data['is_string'] = true;
        $data['is_einvoice'] = $input['is_einvoice'] ?? 0;

        // * defaulted to Sales Income
        /*$gl_account = Account::where('account_code', '1410160')->where('type', Account::TYPE_INC)->first();

        if (empty($gl_account)) {
            throw new \Exception('GL Account 1410160 (Income) not found for String Contracts Billing');
        }*/

        // Temporarily set to IN5100 first while waiting for SAP side to test and setup 1410160
        $gl_account = Account::where('account_code', 'IN5100')->where('type', Account::TYPE_INC)->first();
        if (empty($gl_account)) {
            throw new \Exception('GL Account IN5100 (Income) not found for String Contracts Billing');
        }

        if (!empty($gl_account)) {
            $data['gl_account_id'] = $gl_account->id;
            $data['gl_account_code'] = $gl_account->account_code;
        }

        // WAHHI said string settlement documents can only be at sales contract, so if the settlment falls on a purchase contract,
        // we need to look for the related sales contract (with same profit center) to attach the document to
        // New update for sdotpl: bypass string billing can result in payment voucher. dont need to move payment voucher to sales contract - can remain in purchase
        if ($referable_contract->transaction_type == Contract::TYPE_PURCHASE ) {

            foreach ($settlement_contracts as $contract) {
                if ($contract->transaction_type == Contract::TYPE_SALES && $contract->profit_center_id == $referable_contract->profit_center_id) {
                    $data['contractable_type'] = $contract->getClass();
                    $data['contractable_id'] = $contract->id;

                    $input['referable_contract'] = $contract;

                    // Update tax code as well to follow sales contract
                    $new_tax = $contract->tax;

                    if ( $new_tax !== null ){
                        $data['tax_id'] = $new_tax->id;
                        $data['tax_percentage'] = $new_tax->percentage;
                        $data['tax_amount'] = round($data['amount_before_tax'] * $data['tax_percentage'] / 100, 2);
                        $data['amount_after_tax'] = $data['amount_before_tax'] + $data['tax_amount'];
                    }else{
                        $data['tax_id'] = null;
                        $data['tax_percentage'] = 0;
                        $data['tax_amount'] = 0;
                        $data['amount_after_tax'] = $data['amount_before_tax'] + $data['tax_amount'];
                    }

                    break;
                }
            }
        }
        else if ( $referable_contract->transaction_type == Contract::TYPE_SALES ) {

            // Update tax code as well to follow sales contract
            $new_tax = $referable_contract->tax;

            if ( $new_tax !== null ){
                $data['tax_id'] = $new_tax->id;
                $data['tax_percentage'] = $new_tax->percentage;
                $data['tax_amount'] = round($data['amount_before_tax'] * $data['tax_percentage'] / 100, 2);
                $data['amount_after_tax'] = $data['amount_before_tax'] + $data['tax_amount'];
            }else{
                $data['tax_id'] = null;
                $data['tax_percentage'] = 0;
                $data['tax_amount'] = 0;
                $data['amount_after_tax'] = $data['amount_before_tax'] + $data['tax_amount'];
            }

        }

        $data['amount_after_tax_and_knockoff'] = $data['amount_after_tax'];     // no knockoff for string billing

        return $data;
    }

    public static function prepareLineItem($input)
    {
        $contract = $input['referable_contract'];

        $data = [];
        $data['billing_document_id'] = $input['billing_document']->id;
        $data['product_id'] = $contract->product_id;
        $data['product_code'] = $contract->product_code;
        $data['quantity_uom'] = $contract->quantity_uom;
        $data['currency'] = $contract->currency;
        $data['forex_rate'] = $contract->forex_rate;
        $data['price_uom'] = $contract->base_price_uom ?? $contract->price_uom;
        $data['unit_price'] = $input['settlement_price'];
        $data['created_by'] = Auth::user()->id;

        $data['quantity'] = $contract->quantity;
        $data['tax_percentage'] = $input['tax_percentage'];
        $data['amount_before_tax'] =  $input['billing_document']->type == BillingDocument::TYPE_CREDIT_NOTE ? 0 - $input['amount_before_tax'] : $input['amount_before_tax'];
        $data['tax_amount'] = $input['tax_amount'];
        $data['amount_after_tax'] = $input['billing_document']->type == BillingDocument::TYPE_CREDIT_NOTE ? 0 - $input['amount_after_tax'] : $input['amount_after_tax'];

        $data['description'] = 'String Settlement of ' . $data['quantity'] . ' ' . $data['quantity_uom'] . ' ' .
            $data['product_code'] . ' at ' . $input['settlement_price'] . '/' . $data['price_uom'];

        return $data;
    }

    public static function decorateCircleContractData($data)
    {
        $referable_contract = $data['referable_contract'];
        $settlement_price = ($data['referable_contract']->getContractPrice() - $data['lowest_contract_price']);
        $amount_before_tax = $settlement_price * $data['referable_contract']->quantity;
        $tax_percentage = !empty($data['referable_contract']->tax) ? $data['referable_contract']->tax->percentage : 0;
        $tax_amount = $amount_before_tax * $tax_percentage / 100;
        $amount_after_tax = $amount_before_tax + $tax_amount;

        $payment_terms = self::getPaymentTerms($referable_contract);

        $document_type = null;

        if ($data['referable_contract']->transaction_type === PhysicalContract::TYPE_PURCHASE) {
            if ($settlement_price > 0) {
                $document_type = BillingDocument::TYPE_CREDIT_NOTE;
            } else {
                $document_type = BillingDocument::TYPE_DEBIT_NOTE;
            }
        } else {
            if ($settlement_price > 0) {
                $document_type = BillingDocument::TYPE_DEBIT_NOTE;
            } else {
                $document_type = BillingDocument::TYPE_CREDIT_NOTE;
            }
        }

        return [
            'referable_contract' => $referable_contract,
            'settlement_price' => $settlement_price,
            'amount_before_tax' => $amount_before_tax,
            'tax_percentage' => $tax_percentage,
            'tax_amount' => $tax_amount,
            'amount_after_tax' => $amount_after_tax,
            'document_type' => $document_type,
            'payment_terms' => $payment_terms,
            'tax_id' => !empty($referable_contract) && !empty($referable_contract->tax) ? $referable_contract->tax->id : null,
        ];
    }


    public static function getPaymentTerms($referable_contract) {

        // Determine payment terms
        $payment_terms = $referable_contract->paymentTerms;
        $data = null;

        if ( $payment_terms ){

            // CN/DN will follow invoice payment terms settings for CAD 99/98% payment even though CN/DN billing results in CN/DN
            $billing_document_factory = BillingDocumentFactory::init(BillingDocument::TYPE_NORMAL_INVOICE);
            $billing_document_factory->setBillingDocumentType(BillingDocument::TYPE_NORMAL_INVOICE);

            $data['payment_terms_id'] = $payment_terms->id;
            $data['payment_terms_percentage'] = $billing_document_factory->getPaymentTermsInvoicePercentage($payment_terms);
            $data['payment_terms_description'] = $payment_terms->invoice_description;
            $data['payment_terms_days'] = $payment_terms->due_date_days;

        }

        return $data;


    }


    public static function eInvoicingDocumentTypeUpdate(&$input) {

        $referable_contract = $input['referable_contract'];
        $opposite_contract = null;

        // in the event that the generated CNDN source is from a purchase contract
        if ( $referable_contract->isPurchaseContract() ) {
            // check opposite whether got sales contract
            $contracts_in_string = $referable_contract->stringHeaders->first()->contractsInString
                ->where('is_external', 0)
                ->where('contractable_type', PhysicalContract::class)
                ->where('contractable_id', '!=', $referable_contract->id)
                ->first();

            if ( $contracts_in_string !== null && $contracts_in_string->contractable->isSaleContract() && $referable_contract->legal_entity_id === $contracts_in_string->contractable->legal_entity_id ) {
                $opposite_contract = $contracts_in_string->contractable;
            }
        }

        $is_einvoicing_flow = $referable_contract->isFollowEInvoicingFlow() || ($opposite_contract !== null && $opposite_contract->isFollowEInvoicingFlow());

        if ( $is_einvoicing_flow && $input['document_type'] === BillingDocument::TYPE_DEBIT_NOTE ) {
            $input['document_type'] = BillingDocument::TYPE_NORMAL_INVOICE;
        }

        if ( $is_einvoicing_flow ) {
            $input['is_einvoice'] = 1;
        }

        return $input;
    }

}
