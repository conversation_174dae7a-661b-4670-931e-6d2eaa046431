<?php

namespace App\Helpers;

use App\Interfaces\Contractable;
use App\Models\ContractType;
use App\Models\FuturesContract;
use App\Models\Nsr;
use App\Models\PriceIndex;
use App\Models\PriceIndexEntry;
use App\Repositories\PriceIndexEntryRepository;
use App\Repositories\PriceIndexRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class FuturesContractHelper
{
    public static function getBMDSettlementPriceOptions(Contractable $contract)
    {
        $user = Auth::user();

        $prices = [];

        $prices[] = [
            'id' => 0,
            'label' => '(Contract Price)',
            'price' => $contract->price,
        ];

        $priceIndexRepository = app()->make(PriceIndexRepository::class);
        $priceIndexEntryRepository = app()->make(PriceIndexEntryRepository::class);

        $bmd_settle_price_indexes = $priceIndexRepository->getPriceIndexMaster([
            'product_id' => $contract->product_id,
            'contract_type' => ContractType::MODEL_TO_TYPE[FuturesContract::class],
        ]);

        $entries = $priceIndexEntryRepository->getPriceIndexEntryMaster([
            'indexable_type' => PriceIndex::class,
            'indexable_ids' => $bmd_settle_price_indexes->pluck('id'),
            'delivery_date_from_exact' => Carbon::parse($contract->shipment_month)->timezone($user->timezone)->startOfMonth()->timezone('UTC')->toDateTimeString(),
            'delivery_date_to_exact' => Carbon::parse($contract->shipment_month)->timezone($user->timezone)->endOfMonth()->timezone('UTC')->toDateTimeString(),
        ], true, false, ['indexable']);

        foreach ($entries as $entry) {

            $prices[] = [
                'id' => $entry->id,
                'label' => '(' . $entry->indexable->code . ')',
                'price' => $entry->settle,
            ];
        }

        return $prices;
    }

    public static function getBMDSettlementDetails(Contractable $contract, $settlement_price = 0)
    {

        $nsrs = Nsr::where('contractable_type', $contract->getClass())
            ->where('contractable_id', $contract->getId())
            ->where('status', Nsr::STATUS_CONFIRMED)
            ->get();

        // determine which NSR is over/under collected
        $data = [];

        foreach ($nsrs as $nsr) {

            // If collection weight same as 25MT, skip
            if ($nsr->collection_weight === FuturesContract::MT_IN_ONE_LOT) {
                continue;
            }

            $data[] = [
                'billable_type' => get_class($nsr),
                'billable_id' => $nsr->id,
                'nsr_number' => $nsr->serial_number,
                'collected_quantity' => $nsr->collection_weight,
                'original_quantity' => FuturesContract::MT_IN_ONE_LOT,
                'billing_quantity' => bcsub($nsr->collection_weight, FuturesContract::MT_IN_ONE_LOT, 4),
                'settlement_price' => $settlement_price,
                'settlement_total' => round($settlement_price * (bcsub($nsr->collection_weight, FuturesContract::MT_IN_ONE_LOT, 4)), 2),
            ];
        }

        return $data;
    }

    public static function getBMDSettlementPrice($contract, $input)
    {
        $settlement_price = $contract->getContractPrice();

        if (isset($input['settlement_price_id']) && $input['settlement_price_id'] != 0) {
            $entry = PriceIndexEntry::findOrFail($input['settlement_price_id']);
            $settlement_price = $entry->settle;
        }

        return self::getBMDSettlementDetails($contract, $settlement_price);
    }
}
