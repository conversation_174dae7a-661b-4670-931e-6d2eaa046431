<?php

namespace App\Helpers;

use App\Interfaces\Contractable;
use App\Interfaces\PhysicalContractNumberInterface;
use App\Models\BillingDocument;
use App\Models\ContractType;
use App\Models\Counterparty;
use App\Models\LegalEntity;
use App\Models\PhysicalContract;
use App\Models\PricingType;
use App\Models\Product;
use App\Models\RubberContract;
use App\Repositories\BillingDocumentRepository;
use App\Services\ContractNumberService;
use Carbon\Carbon;

class GtmMyPhysicalContractNumberHelper implements PhysicalContractNumberInterface
{
    private $contractNumberService, $contract;

    public function __construct(ContractNumberService $contractNumberService)
    {
        $this->contractNumberService = $contractNumberService;
    }

    public function setContract(PhysicalContract $contract): PhysicalContractNumberInterface {
        $this->contract = $contract;
        return $this;
    }

    public function getInternalContractNumberForGTM(PhysicalContractNumberParams $params): string
    {
        return strtoupper(
            "{$params->transaction_type}/C-{$params->legal_entity_code}{$params->profit_center_code}/{$params->ship_month_year}/{$params->product_contract_number_reference}{$params->running_number}"
        );
    }

    public function getInternationalContractNumberForGTM(PhysicalContractNumberParams $params): string
    {
        return strtoupper(
            "{$params->transaction_type}/{$params->legal_entity_code}{$params->profit_center_code}/{$params->ship_month_year}/{$params->product_contract_number_reference}{$params->running_number}"
        );
    }

    public function allocateRunningNumber(): string
    {
        $profitCenter = $this->contract->profitCenter;
        $shipmentMonthString = $this->contract->shipment_month;

        $shipmentMonthDate = Carbon::parse($shipmentMonthString);

        $ship_month = $shipmentMonthDate->format('n');
        $ship_year = $shipmentMonthDate->format('Y');

        $running_number = $this->contractNumberService->allocateContractNumber(
            false,
            ContractType::TYPE_PHYSICAL,
            $profitCenter->id,
            null,
            $ship_month,
            $ship_year,
            null,
            4
        );

        return $running_number;
    }


    public function generateContractNumber(): string
    {
        $runningNumber = $this->allocateRunningNumber();

        $params = new PhysicalContractNumberParams(
            $this->contract->transaction_type,
            $this->contract->legalEntity->code,
            $this->contract->profitCenter->code,
            Carbon::parse($this->contract->shipment_month)->format('ym'),
            $this->contract->product->contractNumberReference($this->contract->legalEntity->is_minamas),
            $runningNumber
        );

        $contract_number = null;

        if ($this->contract->is_internal) {
            $contract_number = $this->getInternalContractNumberForGTM($params);
        } else {
            $contract_number = $this->getInternationalContractNumberForGTM($params);
        }

        return $contract_number;
    }

}
