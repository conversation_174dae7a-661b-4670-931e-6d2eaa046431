<?php

namespace App\Helpers;

class NumberHelper
{
    const LOT_TO_MT_CONVERSION = 25;

    /**
     * Numbers will return in right trimmed out zero
     *
     * @param string|int|float|double $number
     * @param integer $decimal
     * @param bool $trailing_zeros remove trailing zeros
     * @return string
     */
    public static function prettify($number, int $decimal = 0, $trailing_zeros = false)
    {
        if (!is_numeric($number)) {
            return $number;
        }

        $result = number_format($number, $decimal);

        if (!$trailing_zeros) {

            $result = preg_replace('/(.*)\.0+$/', '$1', $result);;

            $result = preg_replace('/(.*\.\d*?[1-9])0+$/', '$1', $result);
        }

        return $result;
    }

    public static function convertMTToLot($quantity)
    {
        return round($quantity / self::LOT_TO_MT_CONVERSION);
    }

    /**
     * @param $quantity
     * @return float
     */
    public static function convertLotToMT($quantity)
    {
        return round($quantity * self::LOT_TO_MT_CONVERSION);
    }

    public static function polarize($number, $is_formatted = true, $decimal_points = 2)
    {

        $prefix = "";
        $suffix = "";
        $value = $number;

        if ($number < 0) {
            $prefix = "(";
            $suffix = ")";
        }

        if ($is_formatted) {
            $value = self::prettify(abs($number), $decimal_points, true);
        }

        return $prefix . $value . $suffix;
    }

    public static function convertByteSize($size)
    {
        $unit = array('b', 'kb', 'mb', 'gb', 'tb', 'pb');
        return @round($size / pow(1024, ($i = floor(log($size, 1024)))), 2) . $unit[$i] . ' ';
    }

    public static function numberToRomanRepresentation($number)
    {
        $map = array('M' => 1000, 'CM' => 900, 'D' => 500, 'CD' => 400, 'C' => 100, 'XC' => 90, 'L' => 50, 'XL' => 40, 'X' => 10, 'IX' => 9, 'V' => 5, 'IV' => 4, 'I' => 1);
        $returnValue = '';
        while ($number > 0) {
            foreach ($map as $roman => $int) {
                if ($number >= $int) {
                    $number -= $int;
                    $returnValue .= $roman;
                    break;
                }
            }
        }
        return $returnValue;
    }
}
