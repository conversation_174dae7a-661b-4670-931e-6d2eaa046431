<?php

namespace App\Helpers;

/**
 * Class StringHelper
 * @package App\Helpers
 */
class StringHelper
{
    /**
     * @param $value
     * @return string|string[]|null
     */
    public static function prettify($value)
    {
        if (!is_string($value)) {
            return $value;
        }
        return ucwords(preg_replace('/[^a-zA-Z0-9]/', ' ', $value));
    }

    public static function prettifyFullUpper($value)
    {
        if (!is_string($value)) {
            return $value;
        }
        return strtoupper(preg_replace('/[^a-zA-Z0-9]/', ' ', $value));
    }

    public static function getMultilineMessage($title, $subtitle, $fields)
    {
        $error_str = $title;

        foreach ($fields as $field) {
            $error_str .= "\n" . $subtitle . $field;
        }

        return $error_str;
    }

    public static function stripSpecialChar(string $string)
    {
        return preg_replace('/[^A-Za-z0-9]/', '', $string);
    }

    public static function escapeNewLine($string)
    {
        $string = str_replace("\r\n", "\n", $string);
        $string = str_replace(";", " ", $string);
        return str_replace("\n", "\\n", $string);
    }
}
