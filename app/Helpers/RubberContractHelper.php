<?php

namespace App\Helpers;

use App\Interfaces\Contractable;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\BillOfLading;
use App\Models\PhysicalLoad;
use App\Models\PhysicalLoadAdjustment;
use App\Models\RubberContract;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;

class RubberContractHelper
{

    const CONTRACT_DETAILS_KEYS = [
        'is_international', 'is_internal',
        'shipment_period', 'seller_reference', 'buyer_reference',
        'broker_reference', 'remarks', 'broker_id',
    ];

    const COMMERCIAL_DETAILS_KEYS = [
        'hedged_forex_rate', 'price_index_id', 'forex_index_id',
        'custom_terms', 'payment_terms_id', 'contract_terms_id',
        'cess_inclusive', 'cess_payable',
    ];

    const FULFILMENT_DETAILS_KEYS = [
        'load_port_id', 'discharge_port_id',
        'final_destination_country', 'final_destination_address',
        'estimated_delivery_date', 'mode_of_transport', 'ex_location_id',
        'inventory_location_id',
    ];

    const SPECIFICATION_KEYS = [
        'specification_remarks',
    ];

    const SPECIAL_HANDLING_KEYS = [
        'shipment_period', 'estimated_delivery_date',
    ];

    const NUMBER_ATTRIBUTES_HANDLING = [
        'quantity' => 10000,
        'base_price' => 100,
        'premium' => 100,
        'discount' => 100,
        'final_price' => 100,
        'forex_rate' => 100000,
        'hedged_forex_rate' => 100000,
    ];

    public static function getDataForFormSection($input, $section = null)
    {

        $field_keys = [];

        if (!empty($section)) {

            if ($section === 'contract_details') {

                $field_keys = self::CONTRACT_DETAILS_KEYS;

            } else if ($section === 'commercial_details') {

                $field_keys = self::COMMERCIAL_DETAILS_KEYS;

            } else if ($section === 'fulfilment_details') {

                $field_keys = self::FULFILMENT_DETAILS_KEYS;

            } else if ($section === 'specifications') {

                $field_keys = self::SPECIFICATION_KEYS;

            } else if ($section === 'additional_costs') {

                $field_keys = [];

            } else {

                throw new \Exception('Form not recognise.');

            }

        } else {
            $field_keys = array_merge(
                self::CONTRACT_DETAILS_KEYS,
                self::COMMERCIAL_DETAILS_KEYS,
                self::FULFILMENT_DETAILS_KEYS,
                self::SPECIFICATION_KEYS
            );
        }

        $data = [];
        foreach ($field_keys as $key) {

            if (!in_array($key, self::SPECIAL_HANDLING_KEYS)) {
                $data[$key] = $input[$key] ?? null;
            }

        }

        // Fields that require special handling
        if ($section == 'contract_details' || empty($section)) {
            if (!empty($input['shipment_period'])) {
                $date = explode('-', $input['shipment_period']);
                $data['shipment_date_from'] = Carbon::parse($date[0])->startOfDay()->toDateTimeString();
                $data['shipment_date_to'] = Carbon::parse($date[1])->endOfDay()->toDateTimeString();
            } else {
                $data['shipment_date_from'] = null;
                $data['shipment_date_to'] = null;
            }
        }

        if ($section == 'fulfilment_details' || empty($section)) {

            $data['estimated_delivery_date'] =
            !empty($input['estimated_delivery_date']) ?
            Carbon::parse($input['estimated_delivery_date'])
                ->startOfDay()
                ->toDateTimeString() :
            null;
        }

        if (isset($input['additional_costs'])) {
            $data['additional_costs'] = $input['additional_costs'];
        }

        return $data;
    }

    public static function putTemplateDataIntoContract($template_data)
    {
        $contract = new RubberContract;

        $available_fields = Schema::getColumnListing('rubber_physical_contracts');
        $contract_fields = $contract->getAttributes();
        $template_data_arr = (array) $template_data;

        $fields = array_unique(array_merge(array_keys($template_data_arr), array_keys($contract_fields)));

        // Logic for gettting correct data
        foreach ($fields as $field) {
            $value = null;
            if (!empty($contract_fields[$field])) {
                $value = $contract_fields[$field];
            } else if (!empty($template_data_arr[$field])) {
                $value = $template_data_arr[$field];
            }

            if (!empty($value)) {
                if (isset(self::NUMBER_ATTRIBUTES_HANDLING[$field])) {
                    $value = $value / self::NUMBER_ATTRIBUTES_HANDLING[$field];
                }

                // special handling for fields
                if ($field == 'fulfilment_tolerance_value') {
                    if (!empty($template_data_arr['fulfilment_tolerance_value']) && !empty($template_data_arr['fulfilment_tolerance_type'])) {
                        if ($template_data_arr['fulfilment_tolerance_type'] == 'fixed_value') {
                            $contract->{'load_tolerance_fixed_amount'} =
                            $template_data_arr['fulfilment_tolerance_value'] / self::NUMBER_ATTRIBUTES_HANDLING['load_tolerance_fixed_amount'];
                            $contract->{'load_tolerance_percent'} = null;
                        } else if ($template_data_arr['fulfilment_tolerance_type'] == 'percentage') {
                            $data->{'load_tolerance_fixed_amount'} = null;
                            $data->{'load_tolerance_percent'} =
                            $template_data_arr['fulfilment_tolerance_value'] / self::NUMBER_ATTRIBUTES_HANDLING['load_tolerance_percent'];
                        } else {
                            throw new \Exception('Load tolerance type not recognised');
                        }
                    }
                    continue;
                } else if (!in_array($field, $available_fields)) {
                    continue;
                }

                $contract->{$field} = $value;
                continue;
            }
        }

        return $contract;
    }

    public static function convertQuantity($quantity, $original_type, $requested_type, $drc_percentage)
    {
        if ($original_type === RubberContract::QUANTITY_TYPE_WET && $requested_type === RubberContract::QUANTITY_TYPE_DRY) {
            return round($quantity / 100 * $drc_percentage, 4);
        } else if ($original_type === RubberContract::QUANTITY_TYPE_DRY && $requested_type === RubberContract::QUANTITY_TYPE_WET) {
            return round($quantity / $drc_percentage * 100, 4);
        } else {
            return round($quantity, 4);
        }
    }

    public static function getCESSAmount($quantity_in_kg, $drc = null)
    {
        //TODO: if quantity is not in KG. Do conversion
        if (!empty($drc)) {
            return round(RubberContract::CESS_VALUE * ($quantity_in_kg * $drc / 100), 2);
        } else {
            return round(RubberContract::CESS_VALUE * $quantity_in_kg, 2);
        }
    }

    public static function getCESSValue($drc = null)
    {
        if (!empty($drc)) {
            return RubberContract::CESS_VALUE * ($drc / 100);
        } else {
            return RubberContract::CESS_VALUE;
        }
    }

    public static function getQuantityFromBillable(Contractable $contract, BillingDocumentLineItem $line_item, $quantity_type)
    {
        $billable = $line_item->billable;

        if ( $billable == null ){
            return $line_item->quantity;
        }

        $billable_class = get_class($billable);
        $quantity = $line_item->quantity;

        if ($billable_class === BillOfLading::class) {
            return $quantity;

        } else if (in_array($billable_class, [PhysicalLoad::class, PhysicalLoadAdjustment::class])) {

            if ($quantity_type == $contract->price_type) {
                return $quantity;
            } else {

                $load = $billable_class == PhysicalLoadAdjustment::class ? $billable->physicalLoad : $billable;

                return self::convertQuantity(
                    $quantity,
                    $contract->price_type,
                    $quantity_type,
                    RubberContractHelper::getDrc($contract, $load)
                );
            }

        } else {
            return 'N/A';
        }
    }

    public static function getDrc(RubberContract $contract, PhysicalLoad $load = null) {

        // Always convert rubber billable quantity to price_type. If price_type = dry, convert to dry weight.
        $drc = $contract->dry_content_percentage;

        // for types where price type is dry but quantity is wet (e.g. field latex, use load DRC instead)
        if ( $load != null
            && isset($load->dry_rubber_content)
            && $load->dry_rubber_content != 0 ){

            $drc = $load->dry_rubber_content;
        }

        return $drc;

    }

    public static function determineInvoiceableQuantity($dispatched_received_quantity, $delivered_collected_quantity) {

        //if difference <= 50kg, use supplier weight
        //difference > 50kg, use average of supplier and buyer weight
        $difference = $dispatched_received_quantity - $delivered_collected_quantity;

        if ( $difference <= 50 ){
            return $dispatched_received_quantity;
        }else{
            return ($dispatched_received_quantity + $delivered_collected_quantity) / 2;
        }

    }

}
