<?php

namespace App\Helpers;

use App\Models\Config;

/**
 * Class ProfitCenterHelper
 * @package App\Helpers
 */
class ProfitCenterHelper
{

    public static function get_s4_hana_profit_center_code_list()
    {
        $value = \App\Helpers\ConfigHelper::get(Config::S4_HANA_PROFIT_CENTER_CODE_LIST);

        $values = explode(',', $value);

        foreach ( $values as &$value ){
            $value = strtoupper(trim($value));
        }

        return $values;
    }

}
