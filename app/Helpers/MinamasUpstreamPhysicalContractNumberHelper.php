<?php

namespace App\Helpers;

use App\Interfaces\Contractable;
use App\Interfaces\PhysicalContractNumberInterface;
use App\Models\BillingDocument;
use App\Models\Contract;
use App\Models\ContractType;
use App\Models\Counterparty;
use App\Models\Country;
use App\Models\LegalEntity;
use App\Models\PhysicalContract;
use App\Models\PricingType;
use App\Models\Product;
use App\Models\RubberContract;
use App\Models\TransportMode;
use App\Repositories\BillingDocumentRepository;
use App\Services\ContractNumberService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class MinamasUpstreamPhysicalContractNumberHelper implements PhysicalContractNumberInterface
{
    private $contractNumberService, $contract;

    public function __construct(ContractNumberService $contractNumberService)
    {
        $this->contractNumberService = $contractNumberService;
    }

    public function setContract(PhysicalContract $contract) : PhysicalContractNumberInterface {
        $this->contract = $contract;
        return $this;
    }

    public function allocateRunningNumber(): string
    {
        $legal_entity_id = $this->contract->legal_entity_id;
        $product_id = $this->contract->product_id;

        $running_number = $this->contractNumberService->allocateContractNumber(
            false,
            ContractType::TYPE_PHYSICAL,
            null,
            $product_id,
            null,
            null,
            $legal_entity_id,
            5,
            null,
            $this->contract->is_international,
            $this->contract->transaction_type
        );

        return $running_number;
    }

    public function generateContractNumber(): string
    {
        $runningNumber = $this->allocateRunningNumber();

        $contract_number = null;

        $contract_number = $this
            ->getUpstreamMinamasContractNumber($this->contract, $runningNumber);

        return $contract_number;
    }

    public function getUpstreamMinamasContractNumber(PhysicalContract $contract, string $runningNumber): string
    {
        // If counterparty country is indonesia, use local LOK, else considered export (EKS)
        $internationalPlaceholder = ($contract->is_international ? 'EKS' : 'LOK');

        // Sales has no contract number prefix
        $sales_or_purchase = $contract->transaction_type == Contract::TYPE_SALES ? '' : 'P';

        // Nur adibah: use contract creation date as month/year in contract number. always use jakarta timezone for minamas
        $creation_date = Carbon::parse($contract->created_at, 'Asia/Jakarta');

        $romanMonth = NumberHelper::numberToRomanRepresentation($creation_date->month);
        $shipmentYear = $creation_date->year;

        return strtoupper(
            "{$sales_or_purchase}{$runningNumber}/{$contract->legalEntity->code}/KTR-{$contract->product->contractNumberReference(true)}/{$internationalPlaceholder}/{$romanMonth}/{$shipmentYear}"
        );
    }

}
