<?php

namespace App\Helpers;

use App\Models\ExternalSystem;
use App\Models\IntegrationReference;
use Illuminate\Database\Eloquent\SoftDeletes;

class IntegrationReferenceHelper
{

    /**
     * Get internal value
     *
     * @param string $system_code
     * @param class|string $class
     * @param string $key
     * @param string|int $external_value
     * @param mixed $fallback_value
     * @return void
     */
    public static function getInternalValue($system_code, $class, $key, $external_value, $fallback_value = null)
    {

        if (!class_exists($class)) {
            return $fallback_value;
        }

        $externalSystem = ExternalSystem::code($system_code)->first();

        if (!$externalSystem) {
            return $fallback_value;
        }

        if (!is_string($class)) {
            $class = get_class($class);
        }

        $subject = $externalSystem->integratables()->where('integratable_type', $class)->where('reference_no', $external_value)->first();

        if (!$subject) {
            return $fallback_value;
        }

        if (!$key) {
            return $subject->integratable_type::find($subject->integratable_id);
        }

        return $subject->integratable_type::find($subject->integratable_id)->$key;
    }

    public static function getInternalValueByType($system_code, $class, $key, $external_value, $type, $fallback_value = null)
    {
        if (!class_exists($class)) {
            return $fallback_value;
        }

        $externalSystem = ExternalSystem::code($system_code)->first();

        if (!$externalSystem) {
            return $fallback_value;
        }

        if (!is_string($class)) {
            $class = get_class($class);
        }

        $subject = $externalSystem->integratables()->where('integratable_type', $class)->where('reference_no', $external_value)->where('type', $type)->first();

        if (!$subject) {
            return $fallback_value;
        }

        if (!$key) {
            return $subject->integratable_type::find($subject->integratable_id);
        }

        return $subject->integratable_type::find($subject->integratable_id)->$key;
    }

    public static function getExternalValue($system_code, $class, $key, $internal_value, $fallback_value = null)
    {
        if (!class_exists($class)) {
            return $fallback_value;
        }

        $externalSystem = ExternalSystem::code($system_code)->first();

        if (!$externalSystem) {
            return $fallback_value;
        }

        if (!is_string($class)) {
            $class = get_class($class);
        }

        // only lookup soft deleted when cannot find in non-deleted records.
        $classObj = $class::where($key, $internal_value)->first();

        if ( $classObj === null && in_array(SoftDeletes::class, class_uses($class))) {
            $classObj = $class::withTrashed()->where($key, $internal_value)->first();
        }

        $subject = null;
        if ( $classObj != null ){
            $subject = $externalSystem->integratables()->where('integratable_type', $class)->where('integratable_id', $classObj->id)->first();
        }

        if (!$subject) {
            return $fallback_value;
        }

        return $subject->reference_no;
    }

    /**
     * @param $system_code
     * @param $class
     * @param $key
     * @param $internal_value
     * @param $type
     * @param null $fallback_value
     * @return |null
     */
    public static function getExternalValueByType($system_code, $class, $key, $internal_value, $type, $fallback_value = null)
    {
        if (!class_exists($class)) {
            return $fallback_value;
        }

        $externalSystem = ExternalSystem::code($system_code)->first();

        if (!$externalSystem) {
            return $fallback_value;
        }

        if (!is_string($class)) {
            $class = get_class($class);
        }

        if (in_array(SoftDeletes::class, class_uses($class))) {
            $classObj = $class::withTrashed()->where($key, $internal_value)->first();
        } else {
            $classObj = $class::where($key, $internal_value)->first();
        }

        $subject = null;
        if ( $classObj != null ){
            $subject = $externalSystem->integratables()->where('integratable_type', $class)->where('integratable_id', $classObj->id)->where('type', $type)->first();
        }

        if (!$subject) {
            return $fallback_value;
        }

        return $subject->reference_no;
    }

    /**
     * Insert Reference into Model object
     *
     * @param string $system_code
     * @param class|string $class
     * @param string $key
     * @param string $local_value
     * @param string $external_value
     * @param string $custom_reference
     * @return \App\Models\IntegrationReference|\Exception
     */
    public static function insertOrUpdate(string $system_code, $class, $key, $local_value, $external_value, $custom_reference = null)
    {
        $externalSystem = ExternalSystem::code($system_code)->first();

        if (!$externalSystem) {
            throw new \Exception("External System not found");
        }

        if (!class_exists($class)) {
            throw new \Exception("Class not found");
        }

        $obj = $class::where($key, $local_value)->first();

        if (!$obj) {
            throw new \Exception("Column of $key not found in class $class");
        }

        $integrationReference = IntegrationReference::updateOrCreate(
            [
                'external_system_id' => $externalSystem->id,
                'integratable_type' => $class,
                'integratable_id' => $obj->id,
                'type' => $custom_reference ?? $key,
            ],
            [
                'reference_no' => $external_value,
            ]);

        return $obj->integrate()->save($integrationReference);

    }

    public static function delete(string $system_code, $class, $class_id = null, $key = null)
    {
        $externalSystem = ExternalSystem::code($system_code)->first();

        if (!$externalSystem) {
            throw new \Exception("External System not found");
        }

        if (!class_exists($class)) {
            throw new \Exception("Class not found");
        }

        if (!is_string($class)) {
            $class = get_class($class);
        }

        $query = IntegrationReference::query();

        $query->where(['external_system_id' => $externalSystem->id, 'integratable_type' => $class]);

        if ($class_id) {
            $query->where(['integratable_id' => $class_id]);
        }

        if ($key) {
            $query->where(['type' => $key]);
        }

        $models = $query->get();

        if ($models->count() > 0) {
            $models->map(function ($m) {
                return $m->delete();
            });
        }

    }
}
