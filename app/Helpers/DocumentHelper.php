<?php

namespace App\Helpers;

use App\Factories\BillingDocumentFactory;
use App\Interfaces\Contractable;
use App\Models\BankAccount;
use App\Models\BillingDocument;
use App\Models\BillOfLading;
use App\Models\Contract;
use App\Models\DeliveryOrder;
use App\Models\DocumentTemplates;
use App\Models\FuturesContract;
use App\Models\Incoterms;
use App\Models\LegalEntity;
use App\Models\MasterLegalEntityHistory;
use App\Models\PaymentTerm;
use App\Models\PhysicalContract;
use App\Models\PhysicalLoad;
use App\Models\PhysicalLoadAdjustment;
use App\Models\Port;
use App\Models\PricingType;
use App\Models\QualityBasis;
use App\Models\RspoType;
use App\Models\Tax;
use App\Models\VesselNomination;
use App\Models\WeightBasis;
use App\Repositories\BillingDocumentRepository;
use App\Repositories\UomConversionRepository;
use App\Repositories\UomRepository;
use App\Services\AllocationService;
use App\Services\Billing\InvoiceService;
use App\Services\MasterLegalEntityHistoryService;
use Carbon\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Riskihajar\Terbilang\Facades\Terbilang;

class DocumentHelper
{
    public static function getContractNumber($contract)
    {
        // to handle the split contract
        if ($contract->has_splitted && $contract->root) {
            return $contract->root->contract_number;
        } else {
            return $contract->contract_number;
        }
    }

    public static function getPhone($legal_entity)
    {
        if ($legal_entity->phone_1) {
            return $legal_entity->phone_1;
        }
        if ($legal_entity->phone_2) {
            return $legal_entity->phone_2;
        }
        return "-";
    }

    public static function getPhoneFrom($legal_entity, $number)
    {
        if ($legal_entity->phone_1 && $number == 1) {
            return $legal_entity->phone_1;
        }
        if ($legal_entity->phone_2 && $number == 2) {
            return $legal_entity->phone_2;
        }
        return "-";
    }

    public static function getFax($legal_entity)
    {
        if ($legal_entity->fax_1) {
            return $legal_entity->fax_1;
        }
        if ($legal_entity->fax_2) {
            return $legal_entity->fax_2;
        }
        return "-";
    }

    public static function getFaxFrom($legal_entity, $number)
    {
        if ($legal_entity->fax_1 && $number == 1) {
            return $legal_entity->fax_1;
        }
        if ($legal_entity->fax_2 && $number == 2) {
            return $legal_entity->fax_2;
        }
        return "-";
    }

    public static function getLoadPort(Contractable $contract)
    {
        $loadPort = null;

        if ($contract->load_port_id != null) {
            if ($port = $contract->loadPort) {
                $loadPort = $port->name ?? null;
            }
        } else {
            if ($port = $contract->proposed_load_ports) {
                $loadPort = self::formatPortArrayToString($port);
            }
        }

        return $loadPort;
    }

    public static function getDischargePort(Contractable $contract)
    {
        $dischargePort = null;

        if ($contract->discharge_port_id != null) {
            if ($port = $contract->dischargePort) {
                $dischargePort = $port->name ?? null;
            }
        } else {
            if ($port = $contract->proposed_discharge_ports) {
                $dischargePort = self::formatPortArrayToString($port);
            }
        }

        return $dischargePort;
    }

    public static function getDate($date)
    {
        return Carbon::parse(
          $date,
          'UTC'
        )->tz(\Illuminate\Support\Facades\Auth::user()->timezone)->format("d F Y");
    }

    public static function getTranslatedFormattedDate($date, $locale)
    {
        $date = self::getDate($date);
        // macro-format LL
        $date = Carbon::parse($date)->locale($locale)->isoFormat('LL');

        return $date;
    }

    public static function getIndonesianFormattedDate($date)
    {
        $date = self::getDate($date);
        $month = Carbon::parse($date)->format('F');

        return str_replace($month, trans('date.' . $month, [], 'id'), $date);
    }

    public static function getTransportMode($transport_mode)
    {
        return ucfirst($transport_mode);
    }

    public static function getDeliverTo($deliver_to)
    {
        return ($deliver_to) ? $deliver_to->company_name . " \n " . $deliver_to->address : '-';
    }

    public static function getLogo(LegalEntity $legal_entity, $date = null)
    {
        /** @var MasterLegalEntityHistoryService $historyService */
        $historyService = app(MasterLegalEntityHistoryService::class);
        $old_value = $historyService->determineLegalEntityOldValueByDate($legal_entity->id, MasterLegalEntityHistory::LOGO_FIELD, $date);

        return $old_value ?? $legal_entity->printout_logo ?? 'SDO'; // default to SDO if null
    }

    public static function hasPhysicalLoad($invoice)
    {
        return $invoice->lineItems->whereIn('billable_type',
          [PhysicalLoad::class, PhysicalLoadAdjustment::class])->count() ? "true" : "false";
    }

    public static function getDispatchesFrom(BillingDocument $invoice)
    {
        return $invoice->lineItems->map(function ($lineItem) {
            if ($billable = $lineItem->billable) {
                if ($inventoryLocation = $billable->inventoryLocation) {
                    if ($inventoryLocationName = $inventoryLocation->name) {
                        return $inventoryLocationName;
                    }
                }
            }
        })->unique()->filter(function ($value) {
            return $value != null;
        })->join(',');
    }

    public static function getRemitTo($bank_account, $legal_entity = null)
    {
        if ( ! $bank_account) {
            return '-';
        }

        $swift_code = '-';
        $bank_name = '-';
        if ($bank_relationship = $bank_account->bank_relationship) {
            $branch_no = $bank_relationship->branch_no;
            $swift_code = $bank_relationship->swift_code;
            $bank_name = $bank_relationship->name;
        }

        $remit_to = "Beneficiary Name: " . $bank_account->account_name;
        $remit_to .= "\nBeneficiary Bank Name: " . $bank_name;
        $remit_to .= "\nBank Address: " . $bank_account->bank_address;
        if (isset($legal_entity) && $legal_entity->is_png == true && !empty($branch_no)) {
            $remit_to .= "\nBeneficiary Branch No.: " . $branch_no;
        }
        $remit_to .= "\nBeneficiary Account No.: " . $bank_account->account_number;
        $remit_to .= "\nBeneficiary Swift Code: " . $swift_code;

        return $remit_to;
    }

    public static function getRemitTo2($bank_account)
    {
        if ( ! $bank_account) {
            return '-';
        }

        $bank_name = '-';
        if ($bank_relationship = $bank_account->bank_relationship) {
            $bank_name = $bank_relationship->name;
        }

        $remit_to = $bank_name . "\n";
        $remit_to .= $bank_account->account_name . "\n Account No." . $bank_account->account_number;

        return $remit_to;
    }

    public static function getRemitToMinamasBillingDocument($bank_account)
    {
        if (!$bank_account) {
            return '-';
        }
        $swift_code = '-';
        $bank_name = '-';
        if ($bank_relationship = $bank_account->bank_relationship) {
            $swift_code = $bank_relationship->swift_code;
            $bank_name = $bank_relationship->name;
        }
        $remit_to = $bank_name . ",";
        $remit_to .= "\n" . $bank_account->bank_address;
        $remit_to .= "\n" . $bank_account->currency . " : " . $bank_account->account_number;

        if (!empty ($swift_code)) {
            $remit_to .= " ( " . $swift_code . " )";
        }

        $remit_to .= "\n" . $bank_account->account_name;

        return $remit_to;
    }

    public static function getBasisPort(Contractable $contract, $metaData)
    {
        if ($contract->load_port_id != null) {
            $loadPort = $metaData->load_port->name ?? $contract->loadPort->name ?? null;
            $dischargePort = $metaData->discharge_port->name ?? $contract->dischargePort->name ?? null;
        } else {
            if ($contract->proposed_load_ports) {
                $loadPort = self::formatPortArrayToString($contract->proposed_load_ports);
            }
            if ($contract->proposed_discharge_ports) {
                $dischargePort = self::formatPortArrayToString($contract->proposed_discharge_ports);
            }
        }

        if ($metaData->incoterms->is_require_location && $contract->ex_location_name != null) {
            $basis_port = $contract->ex_location_name; // TODO: CHANGE THIS TO EX_LOCATION_NAME when it's denormalized.
        } else {
            if ( ! isset($metaData->incoterms->basis_port)) {
                $basis_port = $loadPort ?? '-';
            } else {

                if ($metaData->incoterms->basis_port == 'load') {
                    $basis_port = $loadPort ?? '-';
                } else {
                    $basis_port = $dischargePort ?? '-';
                }
            }
        }

        if ($basis_port != "-") {
            return $metaData->incoterms->description . " " . $basis_port;
        } else {
            return $basis_port;
        }
    }

    public static function getBasisPortFromContract(Contractable $contract)
    {
        $incoterms = $contract->incoterms_relationship;
        $load_port = DocumentHelper::getLoadPort($contract);
        $discharge_port = DocumentHelper::getDischargePort($contract);
        // determine basis port
        if (isset($incoterms) && $incoterms->isDeliverBasis()) {
            // Land transport and !is_require_location
            $basis_port = $discharge_port;

        } else {
            //is_require_location
            if ($incoterms->is_require_location && $contract->exLocation != null) {
                $basis_port = $contract->exLocation->name;
            }
            else{
                // !is_require_location or is_require_location but without ex location (FOB)
                if (isset($incoterms->basis_port) && $incoterms->basis_port == Incoterms::BASIS_DISCHARGE_PORT) {
                    $basis_port = $discharge_port ?? '-';
                } else {
                    $basis_port = $load_port ?? '-';
                }
            }

        }
        return $basis_port;
    }

    public static function formatPortArrayToString($ports)
    {
        $ports = is_array($ports) ? $ports : json_decode($ports);
        return ($ports) ? collect($ports)->pluck('name')->join(', ') : null;
    }

    public static function getFormattedPriceForPrintOut($price)
    {
        $display_price = number_format($price, 2);
        if ($price < 0) {
            return "(" . str_replace('-', '', $display_price) . ")";
        } else {
            return $display_price;
        }
    }

    public static function getOffsetTimeZone()
    {
        return Carbon::now(Auth::user()->timezone)->getOffsetString();
    }

    public static function getShipToName($deliver_to)
    {
        return ($deliver_to) ? $deliver_to->company_name : '-';
    }

    public static function getShipToAddress($deliver_to)
    {
        return ($deliver_to) ? $deliver_to->address : '-';
    }

    public static function getShipToTinNo($deliver_to)
    {
        return $deliver_to ? $deliver_to->tin_no : '';
    }

    public static function getShipToCompanyRegistrationNo($deliver_to)
    {
        return $deliver_to ? $deliver_to->company_registration_no : '';
    }

    public static function getShipToPhone($deliver_to)
    {
        return $deliver_to ? $deliver_to->phone : '';
    }

    public static function getShipToFax($deliver_to)
    {
        return $deliver_to ? $deliver_to->fax : '';
    }

    public static function getTotalSpellOut(Contractable $contract, BillingDocument $invoice, $amount, $locale = 'en')
    {
        if(!$contract->currency){
            // StockTransferContract and DeliveryOrder don't have currency column
            throw new \Exception('Currency not found');
        }

        $currency_code = $contract->currency;
        $currency_name = strtoupper($contract->currency_relationship->getTranslationName($locale));
        $number_formatter = new \NumberFormatter($locale, \NumberFormatter::SPELLOUT);
        $number_formatter->setTextAttribute(\NumberFormatter::CURRENCY_CODE, "%spellout-numbering-verbose");

        $arr = explode(".", (string)$amount);

        $dollarPart = $number_formatter->formatCurrency((int)$arr[0], $invoice->currency);
        $centsPart = (!empty($arr[1]) && ((int)$arr[1] != 0)) ? $number_formatter->formatCurrency((int)str_pad($arr[1], 2, "0"),
        $invoice->currency) : '';

        switch ($locale) {
            case 'id':
                $dollarPart = str_replace("KOSONG", "NOL", strtoupper($dollarPart));

                $result = "({$dollarPart}";

                if ( ! empty($centsPart)) {
                    $result .= " {$currency_name} {$centsPart} SEN";
                }else{
                    $result .= " {$currency_name}";
                }

                $result .= ")";
                break;
            case 'en':
                $result = "({$currency_code}: {$dollarPart}";

                if ( ! empty($centsPart)) {
                    $result .= " AND CENTS {$centsPart}";
                }

                $result .= " ONLY)";
                break;
            default:
                throw new \Exception('Invalid Locale Language Code');
        }

        return str_replace("-", " ", strtoupper($result));
    }

    public static function getIncoterms($incoterms)
    {
        if ( ! $incoterms) {
            return '-';
        }
        return $incoterms->description;
    }

    public static function getDestination(Contractable $contract)
    {
        $dischargePort = null;

        if ($contract->discharge_port_id != null) {
            if ($port = $contract->dischargePort) {
                $dischargePort = $port->name;

                if ($country = $port->country) {
                    $dischargePort .= ", " . $country->name;
                }
            }
        } else {
            if ($contract->proposed_discharge_ports) {
                $proposed_discharge_ports = is_array($contract->proposed_discharge_ports) ? $contract->proposed_discharge_ports : json_decode($contract->proposed_discharge_ports);
                $proposed_discharge_ports = !empty($proposed_discharge_ports) ? (object)$proposed_discharge_ports[0] : null;

                if ($proposed_discharge_ports) {
                    if ($port = Port::find($proposed_discharge_ports->id)) {
                        $dischargePort = $port->name;

                        if ($country = $port->country) {
                            $dischargePort .= ", " . $port->country->name;
                        }
                    }
                }
            }
        }
        return $dischargePort ?? '-';
    }

    public static function getDeparture(Contractable $contract)
    {
        $loadPort = null;

        if ($contract->load_port_id != null) {
            if ($port = $contract->loadPort) {
                $loadPort = $port->name;

                if ($country = $port->country) {
                    $loadPort .= ", " . $country->name;
                }
            }
        } else {
            if ($contract->proposed_load_ports) {
                $proposed_load_ports = is_array($contract->proposed_load_ports) ? $contract->proposed_load_ports : json_decode($contract->proposed_load_ports);
                $proposed_load_ports = !empty($proposed_load_ports) ? (object)$proposed_load_ports[0] : null;

                if ($proposed_load_ports) {
                    if ($port = Port::find($proposed_load_ports->id)) {
                        $loadPort = $port->name;

                        if ($country = $port->country) {
                            $loadPort .= ", " . $port->country->name;
                        }
                    }
                }
            }
        }
        return $loadPort ?? '-';
    }

    public static function getShipFromCountry(Contractable $contract)
    {
        $ship_from_country = null;

        if ($contract->load_port_id != null) {
            $port = $contract->loadPort;
            $country = $port->country;
            $ship_from_country = $country->code ?? '-';

        } else {
            if ($contract->proposed_load_ports) {
                $proposed_load_ports = is_array($contract->proposed_load_ports) ? $contract->proposed_load_ports : json_decode($contract->proposed_load_ports);
                $proposed_load_ports = !empty($proposed_load_ports) ? (object)$proposed_load_ports[0] : null;

                if ($proposed_load_ports) {
                    $port = Port::find($proposed_load_ports->id);
                    $country = $port->country;
                    $ship_from_country = $country->code ?? '-';
                }
            }
        }
        return $ship_from_country ?? '-';
    }

    public static function hasAdvanceDeductInvoice($invoice)
    {
        return $invoice->lineItems->where('is_deduct_advance', '=', true)->count() ? "true" : "false";
    }

    public static function getBillToName($billTo, $ori_bill_to_name, $date = null)
    {

        if ($billTo->is_internal) {

            $legalEntity = $billTo->legalEntity;

            if ($legalEntity != null) {

                return self::getLegalEntityName($legalEntity, $date);
            }

        }

        return $ori_bill_to_name;

    }

    public static function getBuyerAndSeller(
        $legalEntity,
        $counterParty,
        $transaction_type,
        $date = null
    ) {
        /** @var MasterLegalEntityHistoryService $historyService */
        $historyService = app(MasterLegalEntityHistoryService::class);

        $counterPartyName = $counterParty->long_name ?? $counterParty->name;

        if ($transaction_type == Contract::TYPE_PURCHASE) {
            $buyer =  self::getLegalEntityName($legalEntity, $date);
            $buyer_address = $legalEntity->address;
            $buyer_tax_registration_number = $legalEntity->tax_registration_number;
            $buyer_company_registration_no = $legalEntity->company_registration_number;
            $buyer_tin_no = $legalEntity->tin_no;
            $buyer_phone = $legalEntity->phone_1;
            $buyer_fax = $legalEntity->fax_1;

            if ($counterParty->is_internal) {
                $seller = self::getLegalEntityName($counterParty->legalEntity, $date);
                $seller_address = $counterParty->legalEntity->address;
                $seller_tax_registration_number = $counterParty->legalEntity->tax_registration_number;
                $seller_company_registration_no = $counterParty->legalEntity->company_registration_number;
                $seller_tin_no = $counterParty->legalEntity->tin_no;
                $seller_phone = $counterParty->legalEntity->phone_1;
                $seller_fax = $counterParty->legalEntity->fax_1;
            } else {
                $seller = $counterPartyName;
                $seller_address = $counterParty->address;
                $seller_tax_registration_number = $counterParty->tax_registration_number;
                $seller_company_registration_no = $counterParty->company_registration_number;
                $seller_tin_no = $counterParty->tin_no;
                $seller_phone = $counterParty->phone;
                $seller_fax = $counterParty->fax;
            }
        } else {
            if ($counterParty->is_internal) {
                $buyer = self::getLegalEntityName($counterParty->legalEntity, $date);
                $buyer_address = $counterParty->legalEntity->address;
                $buyer_tax_registration_number = $counterParty->legalEntity->tax_registration_number;
                $buyer_company_registration_no = $counterParty->legalEntity->company_registration_number;
                $buyer_tin_no = $counterParty->legalEntity->tin_no;
                $buyer_phone = $counterParty->legalEntity->phone_1;
                $buyer_fax = $counterParty->legalEntity->fax_1;
            } else {
                $buyer = $counterPartyName;
                $buyer_address = $counterParty->address;
                $buyer_tax_registration_number = $counterParty->tax_registration_number;
                $buyer_company_registration_no = $counterParty->company_registration_number;
                $buyer_tin_no = $counterParty->tin_no;
                $buyer_phone = $counterParty->phone;
                $buyer_fax = $counterParty->fax;
            }

            $seller = self::getLegalEntityName($legalEntity, $date);
            $seller_address = $legalEntity->address;
            $seller_tax_registration_number = $legalEntity->tax_registration_number;
            $seller_company_registration_no = $legalEntity->company_registration_number;
            $seller_tin_no = $legalEntity->tin_no;
            $seller_phone = $legalEntity->phone_1;
            $seller_fax = $legalEntity->fax_1;
        }

        return [
          'buyer' => $buyer,
          'buyer_address' => $buyer_address ?? '-',
          'buyer_tax_registration_number' => $buyer_tax_registration_number,
          'buyer_company_registration_no' => $buyer_company_registration_no,
          'buyer_tin_no' => $buyer_tin_no,
          'buyer_phone' => $buyer_phone,
          'buyer_fax' => $buyer_fax,
          'seller' => $seller,
          'seller_address' => $seller_address ?? '-',
          'seller_tax_registration_number' => $seller_tax_registration_number,
          'seller_company_registration_no' => $seller_company_registration_no,
          'seller_tin_no' => $seller_tin_no,
          'seller_phone' => $seller_phone,
          'seller_fax' => $seller_fax,
        ];
    }

    public static function getVesselName($invoice, $contract)
    {
        // starting get the tender vesselNomination vessel name
        $invoice_vessel_names = $invoice->lineItems->map(function ($lineItem) {
            if ($lineItem->billable_type === BillOfLading::class) {
                if (isset($lineItem->billable->tender)) {
                    if ($vesselNomination = $lineItem->billable->tender->vesselNomination) {
                        if ($vesselNomination->vessel) {
                            return $vesselNomination->vessel->name;
                        }
                    }
                }
            }
        })->unique()->filter(function ($value) {
            return $value != null;
        })->join(', ');

        // if there is no any tender take the contract vessel nomination
        if (empty($invoice_vessel_names) && $contract->vesselNominations) {
            $invoice_vessel_names = $contract->vesselNominations->map(function ($vesselNomination) {
                if ($vesselNomination->vessel) {
                    return $vesselNomination->vessel->name;
                }
            })->unique()->filter(function ($value) {
                return $value != null;
            })->join(', ');
        }

        return $invoice_vessel_names;
    }

    public static function getContractVesselName($contract)
    {

        $vessel = $contract->vesselNominations->map(function ($vesselNomination) {
            if ($vesselNomination->vessel) {
                return $vesselNomination->vessel->name;
            }
        })->unique()->filter(function ($value) {
            return $value != null;
        })->join(', ');

        return $vessel ?? '-';
    }

    public static function getBillOfLadingDate($invoice)
    {
        return $invoice->lineItems->map(function ($lineItem) {
            if ($lineItem->billable_type === BillOfLading::class) {
                return self::getDate($lineItem->billable->date);
            } else {
                if ($lineItem->billable_type === PhysicalLoadAdjustment::class) {
                    if ( ! empty($lineItem->billable) && ! empty($lineItem->billable->billOfLading)) {
                        return self::getDate($lineItem->billable->billOfLading->date);
                    }
                }
            }
        })->unique()->filter(function ($value) {
            return $value != null;
        })->join(', ');
    }

    public static function getBillOfLadingNo($invoice)
    {
        return $invoice->lineItems->map(function ($lineItem) {

            if ($lineItem->billable_type === BillOfLading::class) {
                return $lineItem->billable_reference_no;
            } else {
                if ($lineItem->billable_type === PhysicalLoadAdjustment::class) {
                    if ( ! empty($lineItem->billable) && ! empty($lineItem->billable->billOfLading)) {
                        return $lineItem->billable->billOfLading->reference_no;
                    }
                }
            }
        })->unique()->filter(function ($value) {
            return $value != null;
        })->join(', ');
    }

    public static function getTotalInvoiceQuantity($invoice)
    {
        $filtered = $invoice->lineItems->filter(function ($value) {
            return $value['description'] != 'GROSS TOTAL';
        });
        return $filtered->sum('quantity');
    }

    public static function getDeliveryMonth($shipment_month, $shipment_period_from, $shipment_period_to)
    {
        $delivery_month = null;

        if ($shipment_period_from && $shipment_period_to) {
            // if no cross month then use shipment month
            if (self::getMonth($shipment_period_from) == self::getMonth($shipment_period_to)) {
                $delivery_month = $shipment_month;
            } else {
                $delivery_month = self::getShipmentPeriods($shipment_period_from, $shipment_period_to);
            }
        } else {
            $delivery_month = $shipment_month;
        }

        return $delivery_month;
    }

    public static function getShipmentPeriods($shipment_period_from, $shipment_period_to)
    {
        return Carbon::parse($shipment_period_from)->format('d F Y') . ' - ' . Carbon::parse($shipment_period_to)->format('d F Y');
    }

    public static function getShipmentPeriodsByFormat($shipment_period_from, $shipment_period_to, $format)
    {
        return Carbon::parse($shipment_period_from)->format($format) . ' - ' . Carbon::parse($shipment_period_to)->format($format);
    }

    public static function getMonth($datetime)
    {
        return Carbon::parse($datetime)->tz(Auth::user()->timezone)->format('m');
    }

    public static function getFirstLineItem($lineItems)
    {
        return $lineItems->where('product_id', '!=', null)->where('product_code', "!=", null)->first();
    }

    public static function convertToAsciiCode($str)
    {
        return str_replace('<', '&#60;', $str);
    }

    public static function getCommodityWithPacking($commodity, $packing)
    {
        if ($packing == "-") {
            return $commodity . ' of EDIBLE GRADE';
        } else {
            return $commodity . ' of EDIBLE GRADE in ' . $packing;
        }
    }

    public static function getCommodityWithBracketPacking($commodity_code, $commodity_description, $packing)
    {
        $data = $commodity_code;

        if (isset($commodity_description) && isset($packing)) {
            $data .= " ($commodity_description in $packing)";
        }

        return $data;
    }

    public static function getCommodityWithPackingAndRspoName($commodity_code, $commodity_description, $packing, $rspo_type)
    {
        $string = $commodity_code;
        if (isset($packing) && $packing !== "-") {
            $string .= ' (' . $commodity_description . ' IN ' . $packing . ')';
        } else {
            $string .= ' (' . $commodity_description . ')';
        }
        if (isset($rspo_type)) {
            if (isset($rspo_type->code) && $rspo_type->code !== RspoType::TYPE_NON_RSPO) {
                $string .= " ($rspo_type->name)";
            }
        }
        return $string;
    }

    public static function getWeightAndQuality($weight, $quality)
    {
        return "$weight and $quality by Independent Surveyor, final at port of loading";
    }

    public static function getInsurance($incoterms)
    {
        if ($incoterms == Incoterms::TYPE_CFR) {
            return "To be covered by buyer";
        } else {
            if ($incoterms == Incoterms::TYPE_CIF) {
                return "To be covered by seller";
            }
        }
        return "-";
    }

    public static function getQuantityWithTolerance(
      $quantity_with_uom,
      $load_tolerance_percent,
      $load_tolerance_fixed_amount
    ) {
        $load_tolerance_percent = NumberHelper::prettify($load_tolerance_percent, 2, true);
        $load_tolerance_fixed_amount = NumberHelper::prettify($load_tolerance_fixed_amount, 2, true);

        $data = $quantity_with_uom;
        if ($load_tolerance_percent > 0) {
            $data .= " +/- $load_tolerance_percent Pct at Seller’s option and at contract price";
        } else {
            if ($load_tolerance_fixed_amount > 0) {
                $data .= " +/- $load_tolerance_fixed_amount MT at Seller’s option and at contract price";
            }
        }
        return $data;
    }

    public static function getPriceForIndiaOrCofcoTemplate($amount, $currency, $quantity_uom)
    {
        $number_formatter = new \NumberFormatter("en", \NumberFormatter::SPELLOUT);
        $number_formatter->setTextAttribute(\NumberFormatter::CURRENCY_CODE, "%spellout-numbering-verbose");

        $arr = explode(".", (string)$amount);

        $dollarPart = $number_formatter->formatCurrency((int)$arr[0], $currency);
        $centsPart = ! empty($arr[1]) ? $number_formatter->formatCurrency((int)str_pad($arr[1], 2, "0"),
        $currency) : '';

        $result = "({$currency}: {$dollarPart}";

        if ( ! empty($centsPart)) {
            $result .= " AND CENTS {$centsPart}";
        }

        $result .= " ONLY)";

        $spellout = str_replace("-", " ", strtoupper($result));
        $price = self::getFormattedPriceForPrintOut($amount);

        return "$currency $price - $spellout PER $quantity_uom";
    }

    public static function getIndiaOrCofcoTemplateTitle($buyer, $broker, $quantity_with_uom, $commodity, $packing_description)
    {
        $data = "We confirm having sold to $buyer";

        if ($broker) {
            $data .= " through $broker";
        }

        $data .= ", $quantity_with_uom  $commodity of Edible Grade in $packing_description  as per the below terms :-";
        return $data;
    }

    public static function getIndiaOrCofcoTemplatePaymentTerm($payment_term, $legal_entity_name, $legal_entity, $profitCenter, $currency)
    {
        if (!$payment_term) {
            return '-';
        }

        if ($payment_term->payment_category === PaymentTerm::PAYMENT_CATEGORY['100% LC']) {
            $bank_details = [];
            $address = str_replace(["\r\n"], ' ', $legal_entity->address);

            $query = BankAccount::with(['bank_relationship'])
                ->where('counterparty_id', $profitCenter->id)
                ->where('currency', $currency)
                ->where('is_active', true);

            $primary_bank_account = (clone $query)->where('is_primary', true)->first();

            if ($primary_bank_account) {
                $bank_details[] = $primary_bank_account->bank_relationship->name .
                    ($primary_bank_account->bank_relationship->swift_code ? " (SWIFT: {$primary_bank_account->bank_relationship->swift_code})" : "");
            } else {
                $active_bank_accounts = (clone $query)->get();

                foreach ($active_bank_accounts as $active_bank_account) {
                    $bank_details[] = $active_bank_account->bank_relationship->name .
                        ($active_bank_account->bank_relationship->swift_code ? " (SWIFT: {$active_bank_account->bank_relationship->swift_code})" : "");
                }
            }

            $bank_details = implode(" or ", $bank_details);

            return "By irrevocable and confirmed letter of credit payable at sight, to be established immediately in favour of sellers through a prime and first class bank by the buyer in favour of $legal_entity_name, {$address} for full 100% of the contract value.\n
                    If the seller does not receive fully operational L/C immediately, buyer is deemed to have defaulted on this contract and seller has the right to declare this contract as null and void and claim any price differential or losses incurred from buyer. Seller has also the option to extend the shipment period by one day for each day of delay, without paying any late shipment penalty or the seller has the option to terminate the contract.\n
                    L/C to be opened, advised and confirmed by a prime and first class bank with expiry date to be 30 days from the shipment date. L/C to be advised directly to {$bank_details}.\n
                    Negotiating bank is allowed to claim direct reimbursement by telegraphic transfer for 100% invoice value on a first class bank in New York, nominated by the issuing bank (the reimbursement bank should be named in the letter of credit).\n
                    L/C to cover the 2% more or less on quantity and value.\n
                    Letter of credit to be freely negotiable. Letter of credit subject to UCPDC,2007 Revision ICC Publications No 600.\n
                    If buyer opt to open a L/C with a usance period, it shall to all intents and purpose provide a sight payment to sellers without any extra charges to the seller.\n
                    Applicable for L/C restricted to bank that has no facility with $legal_entity_name, Negotiation charges and all other bank charges incurred at the restricted negotiating bank to the account of the buyer.
            ";
        } else {
            return $payment_term->contract_description;
        }
    }

    public static function getOtherTerms($payment_term, $legal_entity, $custom_terms)
    {
        if (!$payment_term) {
            return $custom_terms ?? '-';
        }

        $country = ucfirst($legal_entity->countryRelationship ? strtolower($legal_entity->countryRelationship->name) : '');

        if ($payment_term->payment_category === PaymentTerm::PAYMENT_CATEGORY['100% LC']) {
            return "1. All import duties/taxes and licenses at discharge port to be arranged and paid for by the Buyer.\n
                    2. Buyer to ensure all custom paperwork to be done by the Buyer in timely manner and prior to vessel arrival at discharge port.\n
                    3. Commingling allowed provided cargo of the same description and Specifications.\n
                    4. Third party documents acceptable except commercial invoice.\n
                    5. Third party documents like certificate of origin, Bills of lading, surveyor report showing exporter/shipper different from Seller acceptable.\n
                    6. Charter Party/Blank Back/Tanker/Third party bill of lading acceptable.\n
                    7. Partial shipment and partial negotiation allowed.\n
                    8. All documents dated prior to the L/C opening date acceptable.\n
                    9. Apparent spelling errors in L/C text and documents are acceptable for L/C negotiation.\n
                    10. T.T reimbursement allowed.\n
                    11. Insurance to be covered by the Buyer at Buyer’s expenses.\n
                    12. All banking charges in India are for applicant's account. All banking charges outside India are for buyer’s account.\n
                    This contract is governed by Inco terms 2020.\n
                    All other terms and conditions in accordance with FOSFA 80 currently in force exclusion of insurance clause, except where in conflict with above. Arbitration in English Language and $country Law shall be apply.
            ";
        } else {
            return "1. All import duties/taxes and licenses at discharge port to be arranged and paid for by the Buyer.\n
                    2. Buyer to ensure all custom paperwork to be done by the Buyer in timely manner and prior to vessel arrival at discharge port.\n
                    3. Commingling allowed provided cargo of the same description and Specifications.\n
                    4. Third party documents acceptable except commercial invoice.\n
                    5. Third party documents like certificate of origin, Bills of lading, surveyor report showing exporter/shipper different from Seller acceptable.\n
                    6. Charter Party/Blank Back/Tanker/Third party bill of lading acceptable.\n
                    7. Partial shipment and partial negotiation allowed.\n
                    8. T.T reimbursement allowed.\n
                    9. Insurance to be covered by the Buyer at Buyer’s expenses.\n
                    10. All banking charges in India are for applicant's account. All banking charges outside India are for buyer’s account.\n
                    This contract is governed by Inco terms 2020.\n
                    All other terms and conditions in accordance with FOSFA 80 currently in force exclusion of insurance clause, except where in conflict with above. Arbitration, shall be submitted in $country in accordance to the rules of arbitration and appeal in force on the date of contract.
            ";
        }
    }

    public static function getOtherTermsCofco($payment_term, $custom_terms)
    {
        if (!$payment_term) {
            return $custom_terms ?? '-';
        }

        if ($payment_term->payment_category === PaymentTerm::PAYMENT_CATEGORY['100% LC']) {
            return "1. All export duties, taxes, levies, etc., present or future in country of origin/port of shipment shall be for Sellers’ account. All import duties, taxes, levies, etc., present or future in port of discharge/country of destination shall be for Buyers’ account.\n
                    2. Buyer to ensure all custom paperwork to be done by the buyer in timely manner and prior to vessel arrival at discharge port. \n
                    3. Commingling allowed provided cargo of the same description and Specifications.\n
                    4. Third party documents acceptable. \n
                    5. Third party documents like certificate of origin, Bills of lading, surveyor report showing exporter/shipper different from beneficiary acceptable. \n
                    6. Charter Party/Blank Back/Tanker/Third party bill of lading acceptable.\n
                    7. Partial shipment and partial negotiation not allowed.\n
                    8. All documents dated prior to the L/C opening date acceptable.\n
                    9. Apparent spelling errors in L/C text and documents are acceptable for L/C negotiation.\n
                    10. T.T reimbursement allowed.\n
                    11. Insurance to be covered by the buyer at buyers cost and expenses.\n
                    12. All banking charges in India are for applicant's account. All banking charges outside India are for beneficiary's account, with exception of: confirmation charges, amendment charges and discount charges, deferred payment charges, commission and interest charges in case of payment with a usance period all for applicant's account.\n
                    This contract is governed by Inco terms 2020.\n
                    All other terms and conditions in accordance with FOSFA 80 currently in force exclusion of insurance clause, except where in conflict with above.
            ";
        } else {
            return "1. All export duties, taxes, levies, etc., present or future in country of origin/port of shipment shall be for Sellers’ account. All import duties, taxes, levies, etc., present or future in port of discharge/country of destination shall be for Buyers’ account.\n
                    2. Buyer to ensure all custom paperwork to be done by the buyer in timely manner and prior to vessel arrival at discharge port.\n
                    3. Commingling allowed provided cargo of the same description and Specifications.\n
                    4. Third party documents acceptable.\n
                    5. Third party documents like certificate of origin, Bills of lading, surveyor report showing exporter/shipper different from beneficiary acceptable.\n
                    6. Charter Party/Blank Back/Tanker/Third party bill of lading acceptable.\n
                    7. Partial shipment and partial negotiation not allowed.\n
                    8. T.T reimbursement allowed.\n
                    9. Insurance to be covered by the buyer at buyers cost and expenses.\n
                    10. All banking charges in India are for applicant's account. All banking charges outside India are for beneficiary's account, with exception of: confirmation charges, amendment charges and discount charges, deferred payment charges, commission and interest charges in case of payment with a usance period all for applicant's account.\n
                    This contract is governed by Inco terms 2020.\n
                    All other terms and conditions in accordance with FOSFA 80 currently in force exclusion of insurance clause, except where in conflict with above.
            ";
        }
    }

    public static function getBillOfLadingDataFromContract($contract)
    {
        $bill_of_ladings = $contract->billOfLadings;
        $bl_info = '';
        if (isset($bill_of_ladings)) {
            foreach ($bill_of_ladings as $bl) {
                $bl_info .= $bl->reference_no . " DATED " . strtoupper(self::getDate($bl->date)) . "\n";
            }
        } else {
            $bl_info = '-';
        }
        return $bl_info;
    }

    public static function setAmountAsSpellout($invoice_amount, $contract)
    {
        $formatter = new \NumberFormatter("en_US", \NumberFormatter::SPELLOUT);
        $invoice_amount = explode('.', $invoice_amount);
        if (count($invoice_amount) > 1) {
            if (strlen((string)$invoice_amount[1]) == 1) {
                $invoice_amount[1] = $invoice_amount[1] * 10;
            }
            $invoice_amount_words = $contract->currency . ' ' . strtoupper($formatter->format($invoice_amount[0])) . ' AND CENTS ' . strtoupper($formatter->format($invoice_amount[1])) . ' ONLY';
        } else {
            $invoice_amount_words = $contract->currency . ' ' . strtoupper($formatter->format($invoice_amount[0])) . ' ONLY';
        }
        return $invoice_amount_words;
    }

    public static function getAmountBeforeTaxAfterKnockOffNumber($invoice)
    {
        $amount_before_tax = $invoice->amount_before_tax;
        $knockoff_amount = $invoice->lineItems->where('is_deduct_advance', '=', '1')->sum('amount_before_tax');
        $amount = abs($amount_before_tax - abs($knockoff_amount));

        return $amount;
    }

    public static function getAmountBeforeTaxAfterKnockoff($invoice)
    {
        $amount = self::getAmountBeforeTaxAfterKnockOffNumber($invoice);

        return self::getFormattedPriceForPrintOut($amount);
    }

    public static function getTaxAmountAfterKnockoff($invoice)
    {
        $tax_amount = $invoice->tax_amount;
        $knockoff_amount = $invoice->lineItems->where('is_deduct_advance', '=', '1')->sum('tax_amount');
        $amount = abs($tax_amount - abs($knockoff_amount));
        return self::getFormattedPriceForPrintOut($amount);
    }

    public static function getTaxAmountWithoutDeductAdvance($invoice)
    {
        $tax_amount = $invoice->lineItems->where('is_deduct_advance', '!=', '1')->sum('tax_amount');
        return self::getFormattedPriceForPrintOut($tax_amount);
    }

    public static function getSumOfDeductAdvance($invoice)
    {
        $deduct_advance_sum = $invoice->lineItems->where('is_deduct_advance', '=', '1')->sum('amount_after_tax');
        return number_format($deduct_advance_sum, 2);
    }

    public static function getDeductAdvanceInvoicesNumber($invoice)
    {
        $deduct_invoices_number = '';
        $deduct_advance_invoices_id = $invoice->lineItems->where('is_deduct_advance', '=', '1')->pluck('offset_billing_document_id');
        if (empty($deduct_advance_invoices_id)) {
            return $deduct_invoices_number;
        }
        $billingDocumentRepository = new BillingDocumentRepository();
        $invoices = $billingDocumentRepository->getInvoices(['invoice_ids' => $deduct_advance_invoices_id])
        ->pluck('reference_number')->toArray();
        $deduct_invoices_number = implode(',', $invoices);

        return $deduct_invoices_number;
    }

    public static function getLoadAdjustmentFromInvoice($invoice)
    {
        $load_adjustment_text = "-";

        if ($load_adjustment_first_line_items = $invoice->lineItems->where('billable_type',
          PhysicalLoadAdjustment::class)->first()) {
            if ($billable = $load_adjustment_first_line_items->billable) {
                $billing_document = BillingDocument::with('lineItems')
                  ->where('status', '!=', BillingDocument::INVOICE_STATUS_VOIDED)
                  ->whereHasMorph('contractable', [PhysicalContract::class], function ($query) use ($invoice) {
                      $query->where('contract_number', $invoice->contractable->contract_number);
                  })
                  ->whereHas('lineItems', function ($query) use ($billable) {
                      $query->where('billable_type', PhysicalLoad::class)
                        ->where('billable_id', $billable->load_id);
                  })->first();

                if ($billing_document) {
                    $invoice_no = $billing_document->reference_number;
                    $invoice_date = self::getDate($billing_document->document_date);
                    $load_adjustment_text = "BEING QUANTITY - BILLED IN ORIGINAL TAX INVOICE $invoice_no DATED $invoice_date";
                }
            }
        }

        return $load_adjustment_text;
    }

    // This logic only applies to rubber contract printout
    public static function getRubberContractNumber($contract_number, $is_long_term, $parent_id)
    {
        // if contract has parent then don't append L
        if (isset($parent_id) && $parent_id > 0) {
            return $contract_number;
        }

        $last_char = strtoupper(substr($contract_number, -1));

        if ($is_long_term && $last_char != 'L') {
            return $contract_number . "L";
        }

        return $contract_number;
    }

    public static function getLegalEntityExtraInfo($legalEntity)
    {
        // only minamas templates using this function.
        // at jasper side "legal entity extra info" will show below "telephone-facsimile-website" if not null/empty
        return '';
    }

    public static function getQuantityWithUom($contract)
    {
        return NumberHelper::prettify($contract->quantity, 4, false) . ' ' . $contract->quantityUom->description;
    }

    public static function getDeliveryPeriodMonth($delivery_order_month, $delivery_start_date, $delivery_end_date)
    {
        $delivery_month = null;

        if ($delivery_start_date && $delivery_end_date) {
            // if no cross month then use shipment month
            if (self::getMonth($delivery_start_date) == self::getMonth($delivery_end_date)) {
                $delivery_month = $delivery_order_month;
            } else {
                $delivery_month = self::getShipmentPeriods($delivery_start_date, $delivery_end_date);
            }
        } else {
            $delivery_month = $delivery_order_month;
        }

        return $delivery_month;
    }

    public static function getIncotermsBracketPortDescription($incoterms, $port_description)
    {
        return $incoterms . " ($port_description)";
    }

    public static function getMill($contract)
    {
        $allocationService = app(AllocationService::class);
        $allocations = $allocationService->getActiveAllocation($contract);

        $mills = $allocations->map(function ($allocation) {
            return $allocation->mill->name;
        })->unique()->join(', ');

        if ($mills) {
            return $mills;
        } else {
            return '-';
        }
    }

    public static function getWeight($incoterms)
    {
        return (isset($incoterms->weight_basis)) ? WeightBasis::ALL_TYPES [$incoterms->weight_basis] : "-";
    }

    public static function getQuality($incoterms)
    {
        return (isset($incoterms->quality_basis)) ? QualityBasis::ALL_TYPES [$incoterms->quality_basis] : "-";
    }

    public static function getUnitPriceBeforeTaxForMinamas($contract, $currency, $price, $base_price_uom)
    {
        if ( ! self::isAllowedToPrintAmount($contract)) {
            return null;
        }

        $price_type = "";
        if (empty($contract->fixed_price)) {
            if (isset($contract->pricing_type) && $contract->pricing_type == PricingType::PRICING_TYPE_CORE_PROVISIONAL) {
                $price_type = " (" . ucwords($contract->pricing_type) . ")";
            }
        }

        return $currency . " " . number_format($price, 2) . "/" . $base_price_uom->code . $price_type;
    }

    public static function getAmountBeforeTaxMinamas($contract, $currency, $currency_name, $price, $locale = 'en', $show_tax_base = false)
    {
        if ( ! self::isAllowedToPrintAmount($contract)) {
            return null;
        }

        $tax = self::getMinamasTaxApplicableTaxModel($contract);
        $tax_percent = $show_tax_base ? $tax->name : number_format($tax->percentage, 2);

        $number_formatter = new \NumberFormatter($locale, \NumberFormatter::SPELLOUT);
        $number_formatter->setTextAttribute(\NumberFormatter::CURRENCY_CODE, "%spellout-numbering-verbose");

        $price_with_comma =  number_format($price, 2);
        $price_without_comma = number_format($price, 2, '.', '');
        $arr = explode(".", (string)$price_without_comma);

        $dollarPart = $number_formatter->formatCurrency((int)$arr[0], $currency);
        $centsPart = (!empty($arr[1]) && ((int)$arr[1] != 0)) ? $number_formatter->formatCurrency((int)str_pad($arr[1], 2, "0"), $currency) : '';
        $currency_name = ucwords(strtolower($currency_name));

        $data = $currency . ' ' . $price_with_comma;

        switch ($locale) {
            case 'id':
                $dollarPart = str_replace("kosong", "nol", strtolower($dollarPart));

                $data .= ' (Harga tidak termasuk pajak ' . $tax_percent;

                if (!$show_tax_base) {
                    $data .= '%';
                }

                $data .= ')' . " \n " . ucwords($dollarPart);

                if ( ! empty($centsPart)) {
                    $data .= " {$currency_name} ". ucwords($centsPart) ." Sen";
                }else{
                    $data .= " {$currency_name}";
                }

                break;
            case 'en':
                $dollarPart = str_replace("-", " ", $dollarPart);

                $data .= ' (Price not including ' . $tax_percent;

                if (!$show_tax_base) {
                    $data .= '%';
                }

                $data .=  ' tax)' . " \n " . ucwords($dollarPart);

                if ( ! empty($centsPart)) {
                    $centsPart = str_replace("-", " ", $centsPart);
                    $data .= " And Cents ". ucwords($centsPart);
                }

                $data .= " Only ". $currency_name;

                break;
            default:
                throw new \Exception('Invalid Locale Language Code');
        }

        return $data;
    }

    private static function formatSpellOutMinamas($currency, $currency_name, $price, $locale)
    {
        $number_formatter = new \NumberFormatter($locale, \NumberFormatter::SPELLOUT);
        $number_formatter->setTextAttribute(\NumberFormatter::CURRENCY_CODE, "%spellout-numbering-verbose");

        $price_with_comma =  number_format($price, 2);
        $price_without_comma = number_format($price, 2, '.', '');
        $arr = explode(".", (string)$price_without_comma);

        $dollarPart = $number_formatter->formatCurrency((int)$arr[0], $currency);
        $centsPart = (!empty($arr[1]) && ((int)$arr[1] != 0)) ? $number_formatter->formatCurrency((int)str_pad($arr[1], 2, "0"), $currency) : '';
        $currency_name = ucwords(strtolower($currency_name));

        $data = $currency . ' ' . $price_with_comma;

        switch ($locale) {
            case 'id':
                $dollarPart = str_replace("kosong", "nol", strtolower($dollarPart));

                $data .= "\n" . ucwords($dollarPart);

                if ( ! empty($centsPart)) {
                    $data .= " {$currency_name} ". ucwords($centsPart) ." Sen";
                }else{
                    $data .= " {$currency_name}";
                }

                break;
            case 'en':
                $dollarPart = str_replace("-", " ", $dollarPart);

                $data .= "\n" . ucwords($dollarPart);

                if ( ! empty($centsPart)) {
                    $centsPart = str_replace("-", " ", $centsPart);
                    $data .= " And Cents ". ucwords($centsPart);
                }

                $data .= " Only ". $currency_name;

                break;
            default:
                throw new \Exception('Invalid Locale Language Code');
        }

        return $data;
    }

    public static function getTaxBaseMinamas($contract, $currency, $currency_name, $price, $locale = 'en', $show_tax_base = false)
    {
        if ( ! self::isAllowedToPrintAmount($contract) || !$show_tax_base || !$contract->tax_applicable) {
            return null;
        }

        return self::formatSpellOutMinamas($currency, $currency_name, $price, $locale);
    }

    public static function getTaxAmountMinamas($contract, $currency, $currency_name, $price, $locale = 'en')
    {
        if ( ! self::isAllowedToPrintAmount($contract) || !$contract->tax_applicable ) {
            return null;
        }

        return self::formatSpellOutMinamas($currency, $currency_name, $price, $locale);
    }

    public static function getAmountAfterTaxMinamas($contract, $currency, $currency_name, $price, $locale = 'en', $show_tax_base = false)
    {
        if ( ! self::isAllowedToPrintAmount($contract) || ($show_tax_base && !$contract->tax_applicable)) {
            return null;
        }

        $tax = self::getMinamasTaxApplicableTaxModel($contract);
        $tax_percent = $show_tax_base ? $tax->name : number_format($contract->tax->percentage, 2);

        $number_formatter = new \NumberFormatter($locale, \NumberFormatter::SPELLOUT);
        $number_formatter->setTextAttribute(\NumberFormatter::CURRENCY_CODE, "%spellout-numbering-verbose");

        $price_with_comma =  number_format($price, 2);
        $price_without_comma = number_format($price, 2, '.', '');
        $arr = explode(".", (string)$price_without_comma);

        $dollarPart = $number_formatter->formatCurrency((int)$arr[0], $currency);
        $centsPart = (!empty($arr[1]) && ((int)$arr[1] != 0)) ? $number_formatter->formatCurrency((int)str_pad($arr[1], 2, "0"), $currency) : '';
        $currency_name = ucwords(strtolower($currency_name));

        $data = $currency . ' ' . $price_with_comma;

        switch ($locale) {
            case 'id':
                $dollarPart = str_replace("kosong", "nol", strtolower($dollarPart));

                if ($contract->tax_applicable) {
                    $data .= ' (Harga termasuk pajak ' . $tax_percent;

                    if (!$show_tax_base) {
                        $data .= '%';
                    }
                    $data .= ')';
                }
                $data .= " \n " . ucwords($dollarPart);

                if ( ! empty($centsPart)) {
                    $data .= " {$currency_name} ". ucwords($centsPart) ." Sen";
                }else{
                    $data .= " {$currency_name}";
                }
                break;
            case 'en':
                $dollarPart = str_replace("-", " ", $dollarPart);

                if ($contract->tax_applicable) {
                    $data .= ' (Price including ' . $tax_percent;
                    if (!$show_tax_base) {
                        $data .= '%';
                    }
                    $data .= ' tax)';
                }
                $data .= " \n " . ucwords($dollarPart);

                if ( ! empty($centsPart)) {
                    $centsPart = str_replace("-", " ", $centsPart);
                    $data .= " And Cents ". ucwords($centsPart);
                }

                $data .= " Only ". $currency_name;
                break;
            default:
                throw new \Exception('Invalid Locale Language Code');
        }

        return $data;
    }

    public static function isAllowedToPrintAmount($contract)
    {
        if ($contract->pricing_type !== PricingType::PRICING_TYPE_CORE_PROVISIONAL && $contract->pricing_status == PricingType::STATUS_UNPRICED) {
            return false;
        }
        return true;
    }

    public static function getTermOfDelivery($incoterms_code, $basis_port)
    {
        if ($basis_port == '-') {
            return $incoterms_code;
        } else {
            return $incoterms_code . " (" . $basis_port . ")";
        }
    }

    public static function getQuantityWithSpellOut($quantity, $quantity_uom, $base_price_uom, $product_id, $locale = 'en')
    {
        $quantity = self::convertToBasePriceQuantity($quantity_uom, $base_price_uom, $product_id, $quantity);
        $quantity_uom = self::getUomModel($quantity_uom, $base_price_uom, $product_id);
        $number_formatter = new \NumberFormatter($locale, \NumberFormatter::SPELLOUT);
        $number_formatter->setTextAttribute(\NumberFormatter::DEFAULT_RULESET, "%spellout-numbering-verbose");

        $qty = NumberHelper::prettify($quantity, 4, false);

        switch ($locale) {
            case 'id':
                $quantity_spellout = ucwords($number_formatter->format($quantity)) . " ";
                $quantity_spellout = str_replace("Titik", "Koma", $quantity_spellout);
                break;
            case 'en':
                $quantity_spellout = ucwords($number_formatter->format($quantity)) . " Only ";
                break;
            default:
                throw new \Exception('Invalid Locale Language Code');
        }

        return $qty . " " . $quantity_uom->code . " (" . $quantity_spellout . $quantity_uom->description . ") ";
    }

    public static function getDeliveryOrderNo(Contractable $contract, BillingDocument $document = null)
    {
        // Assume valid billable types are only \PhysicalLoads and \BillOfLading
        $billable_line_items = null;

        if ( $document != null ){

            $billable_line_items = $document->lineItems->filter(function ($filter) {
                return $filter->billable_type == PhysicalLoad::class
                    || $filter->billable_type == BillOfLading::class;
            });

        }

        if ( isset($billable_line_items) && $billable_line_items->isNotEmpty() ) {
            switch ($billable_line_items->first()->billable_type) {
                case PhysicalLoad::class:
                    $delivery_orders = $billable_line_items->map(function ($filter) {
                        return $filter->billable->deliveryOrder;
                    })->filter(function ($value) {
                        return !is_null($value) && (isset($value) && $value->status != DeliveryOrder::STATUS_VOIDED);
                    })->pluck('reference_number')
                        ->unique()
                        ->implode(', ');
                    break;
                case BillOfLading::class:
                    $delivery_orders = $billable_line_items->map(function ($filter) {
                        return $filter->billable->tender->vesselNomination->deliveryOrders->where('status', '!=', DeliveryOrder::STATUS_VOIDED);
                    })->filter(function ($value) {
                        return !is_null($value);
                    })->flatten()
                        ->pluck('reference_number')
                        ->unique()
                        ->implode(', ');
                    break;
                default:
                    $delivery_orders = '';
            }

        } else {
            $delivery_orders = $contract->deliveryOrders->where('status', '!=', DeliveryOrder::STATUS_VOIDED)->pluck('reference_number')->implode(', ');
        }
        if (empty($delivery_orders)) {
            return '-';
        }

        return $delivery_orders;
    }

    public static function getSummaryInvoiceDeliveryOrdersNo(Contractable $contract, BillingDocument $invoice)
    {
        if ( !$contract->isMinamasContract() ) {
            return '-';
        }

        $invoices = DocumentHelper::getSummaryInvoicePrintoutBillingDocuments($contract, $invoice, $invoice->type);

        $invoice_delivery_orders = collect();

        // First Get all invoice delivery orders
        $invoices->map(function ($invoice) use ($invoice_delivery_orders) {

            $invoice->loadMissing('lineItems.billable');

            $billable_line_items = $invoice->lineItems->filter(function ($filter) {
                return $filter->billable_type == PhysicalLoad::class
                    || $filter->billable_type == BillOfLading::class;
            });

            if (isset($billable_line_items) && $billable_line_items->isNotEmpty()) {

                // only eager load deliveryOrder if billable types are physical load only
                $unique_billable_types = $billable_line_items->pluck('billable_type')->unique();

                if ( $unique_billable_types->count() === 1 && $unique_billable_types->first() === PhysicalLoad::class ){
                    $billable_line_items->loadMissing('billable.deliveryOrder');
                }

                switch ($billable_line_items->first()->billable_type) {
                    case PhysicalLoad::class:
                        $delivery_orders = $billable_line_items->map(function ($filter) {
                            return $filter->billable->deliveryOrder;
                        })->filter(function ($value) {
                            return !is_null($value) && (isset($value) && $value->status != DeliveryOrder::STATUS_VOIDED);
                        })->pluck('reference_number')
                            ->unique();
                        break;
                    case BillOfLading::class:
                        $delivery_orders = $billable_line_items->map(function ($filter) {
                            return $filter->billable->tender->vesselNomination->deliveryOrders->where('status', '!=', DeliveryOrder::STATUS_VOIDED);
                        })->filter(function ($value) {
                            return !is_null($value);
                        })->flatten()
                            ->pluck('reference_number')
                            ->unique();
                        break;
                    default:
                        $delivery_orders = collect();
                }

                if ($delivery_orders->isNotEmpty()) {
                    $delivery_orders->map(function ($delivery_order_no) use ($invoice_delivery_orders) {
                        $invoice_delivery_orders->push($delivery_order_no);
                    });
                }

            }
        });

        if (isset($invoice_delivery_orders) && $invoice_delivery_orders->isNotEmpty()) {
            // If any invoice delivery order exist, use invoice delivery order
            // Additional unique to handle multiple BL pointing to same VN Case
            $delivery_orders = $invoice_delivery_orders->unique()->implode(', ');
        } else {
            // Else take all from contract level
            $delivery_orders = $contract->deliveryOrders->where('status', '!=', DeliveryOrder::STATUS_VOIDED)->pluck('reference_number')->implode(', ');
        }

        if (empty($delivery_orders)) {
            return '-';
        }
        return $delivery_orders;
    }

    public static function getCommodityTrans($commodity, $lang)
    {

        App::setLocale($lang);

        if (Lang::has("product." . $commodity['code'])) {
            return trans("product." . $commodity['code']);
        } else {
            return $commodity['description'];
        }
    }

    public static function getDateTrans($date, $lang)
    {
        App::setLocale($lang);

        $temp_date = explode(' ', $date);

        if (Lang::has("date." . $temp_date[1])) {
            return $temp_date[0] . ' ' . trans("date." . $temp_date[1]) . ' ' . $temp_date[2];
        }

        return $date;
    }

    public static function getQuantityTrans($quantity)
    {
        return ucwords(Terbilang::make($quantity));
    }

    public static function getMinamasRemitTo($bank_account)
    {
        if ( ! $bank_account) {
            return '-';
        }

        $bank_name = '-';
        if ($bank_relationship = $bank_account->bank_relationship) {
            $bank_name = $bank_relationship->name;
        }

        $remit_to = "Banker: " . $bank_account->account_name;
        $remit_to .= "\nBank: " . $bank_name;
        $remit_to .= "\nAccount: " . $bank_account->account_number;

        return $remit_to;
    }

    public static function getMinamasShipmentMonth($shipment_date_from, $shipment_date_to)
    {
        $user = Auth::user();
        $shipment_date_from = Carbon::parse($shipment_date_from);
        $shipment_date_to = Carbon::parse($shipment_date_to);

        $shipment_month_from = $shipment_date_from->format('d F Y');
        $shipment_month_to = $shipment_date_to->format('d F Y');
        return $shipment_month_from . ' - ' . $shipment_month_to;
    }

    public static function getMinamasContractBasis($incoterms, $port_description)
    {
        if (isset($port_description) && $port_description != '-') {
            return self::getIncotermsBracketPortDescription($incoterms->description, $port_description);
        } else {
            return $incoterms->description;
        }
    }

    public static function convertToBasePriceQuantity($quantity_uom, $base_price_uom, $product_id, $quantity)
    {
        if ($quantity_uom !== $base_price_uom) {
            $uomConversionRepository = new UomConversionRepository();
            $quantity = $uomConversionRepository->doUomConversionRaw($quantity_uom, $base_price_uom, $product_id, $quantity);
        }
        return $quantity;
    }

    public static function getSummaryInvoicePrintoutBillingDocuments($contract, $invoice, $document_type)
    {
        return $contract->billingDocuments
            ->where('type', '=', $document_type)
            ->where('document_date', '<=', $invoice->document_date)
            ->whereIn('status', BillingDocument::INVOICE_STATUSES_EFFECTIVE);
    }

    public static function getSummaryCnDnPrintoutBillingDocuments($contract, $invoice, array $document_types)
    {
        return $contract->billingDocuments
            ->whereIn('type', $document_types)
            ->where('document_date', '<=', $invoice->document_date)
            ->whereIn('status', BillingDocument::INVOICE_STATUSES_EFFECTIVE);
    }

    public static function getMinamasContractPriceAttribute($final_price, $quantity, $quantity_uom, $base_price_uom, $product_id)
    {
        $quantity = self::convertToBasePriceQuantity($quantity_uom, $base_price_uom, $product_id, $quantity);
        return $final_price * $quantity;
    }

    private static function getUomModel($quantity_uom, $base_price_uom, $product_id)
    {
        if ($quantity_uom !== $base_price_uom) {
            $uom_code = $base_price_uom;
        } else {
            $uom_code = $quantity_uom;
        }
        $uomConversionRepository = new UomRepository();
        $uom_conversion = $uomConversionRepository->getUomMaster(['code' => $uom_code, 'product_id' => $product_id], false, false);
        return $uom_conversion;
    }

    public static function getSummaryInvoiceTotalTaxAmount($contract, $invoice)
    {
        $summary_invoices = self::getSummaryInvoicePrintoutBillingDocuments($contract, $invoice, BillingDocument::TYPE_NORMAL_INVOICE);
        $summary_advance_invoices = self::getSummaryInvoicePrintoutBillingDocuments($contract, $invoice, BillingDocument::TYPE_ADVANCE_INVOICE);
        $summary_invoices_amount = self::calculateTaxAmountFromSummaryDocument($summary_invoices);
        $summary_advance_invoices_amount = self::calculateTaxAmountFromSummaryDocument($summary_advance_invoices);
        $tax_amount = $summary_invoices_amount - $summary_advance_invoices_amount < 0 ? 0 : $summary_invoices_amount - $summary_advance_invoices_amount;

        return self::getFormattedPriceForPrintOut($tax_amount);
    }

    public static function getSummaryCnDnTotalTaxAmount($contract, $invoice)
    {
        $summary_invoices = self::getSummaryInvoicePrintoutBillingDocuments($contract, $invoice, BillingDocument::TYPE_NORMAL_INVOICE);
        $summary_advance_invoices = self::getSummaryInvoicePrintoutBillingDocuments($contract, $invoice, BillingDocument::TYPE_ADVANCE_INVOICE);
        $summary_debit_notes = self::getSummaryInvoicePrintoutBillingDocuments($contract, $invoice, BillingDocument::TYPE_DEBIT_NOTE);
        $summary_credit_notes = self::getSummaryInvoicePrintoutBillingDocuments($contract, $invoice, BillingDocument::TYPE_CREDIT_NOTE);

        $summary_invoices_amount = self::calculateTaxAmountFromSummaryDocument($summary_invoices);
        $summary_advance_invoices_amount = self::calculateTaxAmountFromSummaryDocument($summary_advance_invoices);
        $summary_debit_notes_amount = $summary_debit_notes->sum('tax_amount');
        $summary_credit_notes_amount = $summary_credit_notes->sum('tax_amount');

        return $summary_invoices_amount - $summary_advance_invoices_amount + $summary_debit_notes_amount - $summary_credit_notes_amount;
    }

    public static function calculateTaxAmountFromSummaryDocument($summary_billing_documents)
    {
        return $summary_billing_documents->reduce(function ($carry, $item) {
            $currency_parser = $fmt = new \NumberFormatter('en', \NumberFormatter::SPELLOUT);
            $tax_amount_after_knockoff = self::getTaxAmountWithoutDeductAdvance($item);
            return $carry + $currency_parser->parse($tax_amount_after_knockoff);
        }, 0);
    }

    public static function getSummaryInvoiceDocumentTotalAmount($contract, $invoice, $document_type, $include_tax = false)
    {
        $summary_invoices = self::getSummaryInvoicePrintoutBillingDocuments($contract, $invoice, $document_type);
        return $summary_invoices->reduce(function ($carry, $item) use ($include_tax) {
            switch ($include_tax) {
                case true:
                    return $carry + $item->amount_after_tax;
                    break;
                default:
                    return $carry + $item->amount_before_tax;
            }
        }, 0);
    }

    private static function calculateAdvanceDeductNormalInvoiceValue($contract, $invoice, $include_tax = false)
    {
        $advance_invoice_amount = self::getSummaryInvoiceDocumentTotalAmount($contract, $invoice, BillingDocument::TYPE_ADVANCE_INVOICE, $include_tax);
        $invoice_amount = self::getSummaryInvoiceDocumentTotalAmount($contract, $invoice, BillingDocument::TYPE_NORMAL_INVOICE, $include_tax);
        return $advance_invoice_amount - $invoice_amount;
    }

    public static function getSummaryInvoiceTotalAmount($contract, $invoice, $include_tax = true)
    {
        $carry_forward_amount = self::calculateAdvanceDeductNormalInvoiceValue($contract, $invoice, $include_tax);
        switch (true) {
            case $carry_forward_amount > 0:
                return 0;
                break;
            default :
                return abs($carry_forward_amount);
        }
    }

    public static function getSummaryCnDnTotalAmount($contract, $invoice, $include_tax = false)
    {
        //Advance minus Invoice amount
        $carry_forward_amount = self::calculateAdvanceDeductNormalInvoiceValue($contract, $invoice,$include_tax);
        $credit_note_amount = self::getSummaryInvoiceDocumentTotalAmount($contract, $invoice, BillingDocument::TYPE_CREDIT_NOTE, $include_tax);
        $debit_note_amount = self::getSummaryInvoiceDocumentTotalAmount($contract, $invoice, BillingDocument::TYPE_DEBIT_NOTE, $include_tax);

        return $debit_note_amount - $credit_note_amount - $carry_forward_amount;

    }

    public static function getMinamasContractLoadPortWithExLocation(Contractable $contract, $incoterms, $ex_location_name)
    {
        $load_port = DocumentHelper::getLoadPortExcludingNonApplicablePort($contract);
        $port_description = '-';
        if (isset($ex_location_name) && $ex_location_name !== '-') {
            $port_description = $ex_location_name;
        }

        if (isset($load_port) && $load_port !== '-') {
            if ($port_description !== '-') {
                $port_description = $port_description . ", " . $load_port;
            } else {
                $port_description = $load_port;
            }
        }

        return self::getMinamasContractBasis($incoterms, $port_description);
    }

    public static function getLoadPortExcludingNonApplicablePort(Contractable $contract)
    {
        $load_port = null;
        if ($contract->load_port_id != null) {
            $load_port = $contract->loadPort->code !== Port::NON_APPLICABLE_PORT_CODE ? $contract->loadPort->name : '-';
        } else {
            $proposed_load_ports = self::getProposedLoadPortsExcludingNonApplicablePort($contract->proposed_load_ports);
            $load_port = self::formatPortArrayToString($proposed_load_ports);
        }
        return $load_port;
    }

    private static function getProposedLoadPortsExcludingNonApplicablePort($proposed_load_ports)
    {
        $non_applicable_ports = Port::where('code', Port::NON_APPLICABLE_PORT_CODE)->pluck('name')->toArray();
        $proposed_load_ports = !is_array($proposed_load_ports) && !is_object($proposed_load_ports) ? json_decode($proposed_load_ports, true) : $proposed_load_ports;
        if (!is_array($proposed_load_ports) && !is_object($proposed_load_ports)) {
            throw new \Exception('Invalid Proposed Load Port Format');
        }
        foreach ($proposed_load_ports as $index => $port) {
            if (isset($port['name']) && in_array($port['name'], $non_applicable_ports)) {
                unset($proposed_load_ports[$index]);
            }
        }
        return $proposed_load_ports;
    }

    public static function getInvoiceCurrencyToSgdConversionRate(BillingDocument $invoice)
    {
        $to_sgd_conversion_rate = 1;

        if ($invoice->currency != 'SGD') {
            $billing_document_service = (new BillingDocumentFactory())->init($invoice->type);
            $forex_index_entry = $billing_document_service->getBillingDocumentForexRate($invoice->currency, 'SGD', $invoice->document_date, $invoice->document_date);
            // Return close exchange rate
            $to_sgd_conversion_rate = $forex_index_entry->close;
        }

        return $to_sgd_conversion_rate;
    }

    public static function getDocumentOriginalInvoiceData($invoice)
    {
        $original_invoice_number = null;
        $original_invoice_date = null;
        if (isset($invoice->original_invoice_number)) {
            $original_invoice = BillingDocument::where('reference_number', $invoice->original_invoice_number)->first();
            if ( $original_invoice !== null ){
                $original_invoice_number = $original_invoice->reference_number;
                $original_invoice_date = self::getDate($original_invoice->document_date);
            }
        }
        return [$original_invoice_number, $original_invoice_date];
    }

    public static function getCountryOfOrigin($contract)
    {
        $load_port_country = null;

        if ($contract->load_port_id != null) {
            $load_port_country = $contract->loadPort->country->name;
        } else {
            if ($ports = $contract->proposed_load_ports) {
                $ports = is_array($ports) ? $ports : json_decode($ports, true);
                $port = count($ports) > 0 ? Port::with(['country'])->where('id', $ports[0])->first() : null;
                $load_port_country = $port ? $port->country->name : null;
            }
        }

        return $load_port_country;
    }

    public static function getMinamasTaxApplicableTaxModel(PhysicalContract $contract)
    {

        // tax master time is always malaysia timezone, so we need to compare with malaysia timezone
        // tax master applicable datetime is always saved from malaysia time to UTC timezone
        $tax = Tax::where('code', Tax::MINAMAS_TAX_APPLICABLE_TAX_CODE)
            ->where('applicable_from', '<=', Carbon::parse($contract->contract_date, 'Asia/Kuala_Lumpur')->startOfDay()->tz('UTC')->toDateTimeString())
            ->where('applicable_to', '>=', Carbon::parse($contract->contract_date, 'Asia/Kuala_Lumpur')->endOfDay()->tz('UTC')->toDateTimeString())
            ->first();

        if ($tax == null) {
            throw new \Exception('Unable to find tax code ' . Tax::MINAMAS_TAX_APPLICABLE_TAX_CODE);
        }

        return $tax;
    }

    public static function getMinamasBillingTaxApplicableTaxModel(BillingDocument $invoice)
    {

        // tax master time is always malaysia timezone, so we need to compare with malaysia timezone
        // tax master applicable datetime is always saved from malaysia time to UTC timezone
        $tax = Tax::where('code', Tax::MINAMAS_TAX_APPLICABLE_TAX_CODE)
            ->where('applicable_from', '<=', Carbon::parse($invoice->document_date, 'Asia/Kuala_Lumpur')->startOfDay()->tz('UTC')->toDateTimeString())
            ->where('applicable_to', '>=', Carbon::parse($invoice->document_date, 'Asia/Kuala_Lumpur')->endOfDay()->tz('UTC')->toDateTimeString())
            ->first();

        if ($tax == null) {
            throw new \Exception('Unable to find tax code ' . Tax::MINAMAS_TAX_APPLICABLE_TAX_CODE);
        }

        return $tax;
    }

    public static function getInterestChargeText(Contractable $contract, LegalEntity $contract_legal_entity, BillingDocument $billing_document = null)
    {
        switch (get_class($contract)) {
            case PhysicalContract::class:
                if ($contract->isPngContract()) {
                    return 'We reserve the right to charge interest at 4.95% on all the overdue invoices';
                } else {
                    if ( isset($billing_document) && $billing_document->isEinvoice() && !in_array($contract_legal_entity->code, LegalEntity::EINVOICE_LEGAL_ENTITY_CODES_REQUIRING_INTEREST_CHARGE_TEXT) ){
                        return '';
                    }
                    // not einvoice / is einvoice + contract legal entity required interest charge text
                    return 'We reserve the right to charge interest at 1.5% per month on all the overdue invoices';
                }
            case FuturesContract::class: // currently interest charge text is used only in einvoice printout for futures contract
                if ( isset($billing_document) && $billing_document->isEinvoice() && in_array($contract_legal_entity->code, LegalEntity::EINVOICE_LEGAL_ENTITY_CODES_REQUIRING_INTEREST_CHARGE_TEXT) ){
                    // is einvoice + contract legal entity required interest charge text
                    return 'We reserve the right to charge interest at 1.5% per month on all the overdue invoices';
                }
                return '';
            default:
                return '';
        }

    }

    public static function getSignatureRequired(PhysicalContract $contract, BillingDocument $billing_document = null)
    {
        $is_einvoice = isset($billing_document) ? $billing_document->isEinvoice() : $contract->isFollowEInvoicingFlow();

        if ($is_einvoice){
            if (in_array($contract->legalEntity->code, [LegalEntity::SDOT_CODE, LegalEntity::SDOTL_CODE])) {
                return "true";
            }else{
                return "false";
            }
        }

        if ($contract->isPngContract()) {
            return "true";
        }

        if (isset($contract->profitCenter) && $contract->profitCenter->is_upstream) {
            return "false";
        }

        return "true";
    }

    public static function getTotalBlsQuantity($invoice)
    {
        $filtered = $invoice->lineItems->filter(function ($value) {
            return $value['description'] != 'GROSS TOTAL' && $value['billable_type'] == 'App\Models\BillOfLading';
        });
        return $filtered->sum('quantity');
    }

    public static function getGroupBlLineItemsDescription($invoice)
    {
        $contract = $invoice->contractable;
        $billingDocumentService = app()->make(InvoiceService::class);
        $used_unit_price = $contract->getContractPrice(false, false, $contract->bill_with_premium_discount);
        $price_uom = $contract->base_price_uom ?? $contract->quantity_uom;
        $unit_price_display = $contract->currency . ' ' . number_format($used_unit_price, 2) . '/' . $price_uom;
        $sum_quantity_in_price_uom = UomHelper::convert(self::getTotalBlsQuantity($invoice), $contract->quantity_uom, $price_uom, $contract->product_id);
        return NumberHelper::prettify($sum_quantity_in_price_uom, 4) . " {$price_uom} OF {$billingDocumentService->getProductName($contract)} AT {$unit_price_display}";
    }

    public static function validateAndGetSumBlQuantity(VesselNomination $vessel_nomination, $contracts_id)
    {
        if (!$contracts_id){
            throw new \Exception('No Contract Linked to Vessel.');
        }

        $tender_ids = $vessel_nomination->tenders->pluck('id');

        $bill_of_ladings = BillOfLading::whereIn('contractable_id', $contracts_id)->whereIn('vessel_nomination_tender_id', $tender_ids)->get();

        $missing_BL = true;
        $sum_bl_quantity = 0;

        foreach($bill_of_ladings as $bill_of_lading){
            $missing_BL = false;
            $sum_bl_quantity += $bill_of_lading->quantity;
        }

        if($missing_BL === true){
            throw new \Exception('Must have at least 1 Bill of Lading to generate shipment tender report.');
        }

        return $sum_bl_quantity;
    }

    public static function getSummaryInvoiceTotalAmountMinamas($advance_line_items, $advance_include_tax, $line_items, $include_tax, $is_advance_invoice_instead_of_advance_line_items = false){

        $advance_line_items_total_amount = $advance_include_tax ? $advance_line_items->sum('amount_after_tax')/100 : $advance_line_items->sum('amount_before_tax')/100;
        $line_items_total_amount = $include_tax ? $line_items->sum('amount_after_tax')/100 : $line_items->sum('amount_before_tax')/100;

        if($is_advance_invoice_instead_of_advance_line_items){
            $advance_line_items_total_amount = -$advance_line_items_total_amount*100;
        }

        $total_amount = $line_items_total_amount + $advance_line_items_total_amount;

        return $total_amount;
    }

    public static function getFormerlyKnownAs(LegalEntity $legal_entity, $document_date = null)
    {
        /** @var MasterLegalEntityHistoryService $historyService */
        $historyService = app(MasterLegalEntityHistoryService::class);

        return $historyService->determineFormerlyKnownAsName($legal_entity->id, $document_date);
    }

    public static function getWebsite(LegalEntity $legal_entity, $document_date = null)
    {
        /** @var MasterLegalEntityHistoryService $historyService */
        $historyService = app(MasterLegalEntityHistoryService::class);

        $old_value = $historyService->determineLegalEntityOldValueByDate($legal_entity->id, MasterLegalEntityHistory::WEBSITE_FIELD, $document_date);

        return $old_value ?? $legal_entity->website;
    }

    public static function getLegalEntityName(LegalEntity $legal_entity, $document_date = null)
    {
        /** @var MasterLegalEntityHistoryService $historyService */
        $historyService = app(MasterLegalEntityHistoryService::class);

        $old_value = $historyService->determineLegalEntityOldValueByDate($legal_entity->id, MasterLegalEntityHistory::NAME_FIELD, $document_date);

        return $old_value ?? $legal_entity->name;
    }

    // overwrite param's value to translated param's value before sending to jasper
    public static function overwriteParamsValueToTranslatedParamsValue($params)
    {
        if(!isset($params['language'])){
            return $params;
        }else{
            $language = $params['language'];
        }

        $list_of_params_to_be_overwritten = [
            'total_spellout',
            'amount_before_tax_minamas',
            'amount_after_tax_minamas',
            'invoice_date',
            'summary_invoice_total_spellout',
            'quantity_with_spellout',
            'tax_base_minamas',
            'tax_amount_minamas',
            ];

        foreach($list_of_params_to_be_overwritten as $param){
            if(isset($params[$param]) && isset($params[$param .'_'.$language])){
                $params[$param] = $params[$param .'_'.$language];
            }
        }

        return $params;
    }

    public static function getEinvoiceDocumentMapping($billing_document_type)
    {
        $mapping = [
            BillingDocument::TYPE_NORMAL_INVOICE => DocumentTemplates::EINVOICE_GENERIC_BILLING_PRINTOUT_NAME,
            BillingDocument::TYPE_INITIAL_INVOICE => DocumentTemplates::EINVOICE_GENERIC_BILLING_PRINTOUT_NAME,
            BillingDocument::TYPE_CREDIT_NOTE => DocumentTemplates::EINVOICE_GENERIC_BILLING_PRINTOUT_NAME,
            BillingDocument::TYPE_DEBIT_NOTE => DocumentTemplates::EINVOICE_GENERIC_BILLING_PRINTOUT_NAME,
            BillingDocument::TYPE_ADVANCE_INVOICE => DocumentTemplates::EINVOICE_GENERIC_BILLING_PRINTOUT_NAME,
            BillingDocument::TYPE_ADVANCE_CREDIT_NOTE => DocumentTemplates::EINVOICE_GENERIC_BILLING_PRINTOUT_NAME,
        ];

        return $mapping[$billing_document_type] ?? null;
    }

    public static function getEinvoiceTotalSpellOut(Contractable $contract, BillingDocument $invoice, $amount)
    {
        if(!$contract->currency){
            // StockTransferContract and DeliveryOrder don't have currency column
            throw new \Exception('Currency not found');
        }

        $currency_name = strtoupper($contract->currency_relationship->name);
        $number_formatter = new \NumberFormatter("en", \NumberFormatter::SPELLOUT);
        $number_formatter->setTextAttribute(\NumberFormatter::CURRENCY_CODE, "%spellout-numbering-verbose");

        $arr = explode(".", (string)$amount);

        $dollarPart = $number_formatter->formatCurrency((int)$arr[0], $invoice->currency);
        $centsPart = ! empty($arr[1]) ? $number_formatter->formatCurrency((int)str_pad($arr[1], 2, "0"),
        $invoice->currency) : '';

        $result = "{$currency_name}: {$dollarPart}";

        if ( ! empty($centsPart)) {
            $result .= " AND CENTS {$centsPart}";
        }

        $result .= " ONLY";

        return str_replace("-", " ", strtoupper($result));
    }

    public static function getEinvoiceRemitTo($bank_account, $legal_entity = null)
    {
        if ( ! $bank_account) {
            return '-';
        }

        $swift_code = '-';
        $bank_name = '-';
        if ($bank_relationship = $bank_account->bank_relationship) {
            $branch_no = $bank_relationship->branch_no;
            $swift_code = $bank_relationship->swift_code;
            $bank_name = $bank_relationship->name;
        }

        $remit_to = "Beneficiary Account: " . $bank_account->account_name;
        $remit_to .= "\nAccount No.: " . $bank_account->account_number;
        $remit_to .= "\nBeneficiary Bank: " . $bank_name;
        $remit_to .= $bank_account->bank_address ? "\n\n".$bank_account->bank_address : "";
        if (isset($legal_entity) && $legal_entity->is_png == true && !empty($branch_no)) {
            $remit_to .= "\nBeneficiary Branch No.: " . $branch_no;
        }
        $remit_to .= "\nSWIFT Code: " . $swift_code;

        return $remit_to;
    }

    public static function getFooterContent(Contractable $contract, BillingDocument $billing_document)
    {
        $profit_center = $contract->profitCenter;
        $footer_text = '';

        if ($billing_document->isEinvoice()) {

            switch (get_class($contract)) {
                case PhysicalContract::class:
                    $inventory_location_name = DocumentHelper::getDispatchesFrom($billing_document);

                    if ($profit_center->is_upstream && !$profit_center->is_trading) {
                        $footer_text = 'COMMODITY - ' . $contract->product->description . ' @ ' . $billing_document->currency . ' ' . number_format($billing_document->unit_price, 2) . ' PER ' . $billing_document->price_uom;

                        if (!empty($inventory_location_name)) {
                            $footer_text .= "\n" . 'Dispatches From - ' . $inventory_location_name;
                        }

                        if ($billing_document->type == BillingDocument::TYPE_ADVANCE_INVOICE) {
                            $basis_port = DocumentHelper::getBasisPortFromContract($contract);
                            if (!empty($basis_port) && $basis_port != '-') {
                                // incoterms description (basis_port)
                                $basis = DocumentHelper::getMinamasContractBasis($contract->incoterms_relationship, $basis_port);

                                $footer_text .= "\n" . 'BASIS - ' . $basis;
                            }
                        }
                    }

                    break;

                case FuturesContract::class:
                    $inventory_location_name = DocumentHelper::getDispatchesFrom($billing_document);

                    $nsr = $contract->nsrs->first();

                    $tender_point = ($nsr) ? $nsr->inventory_location_name : null;
                    $appraisal_date = ($nsr) ? DocumentHelper::getDate($nsr->appraisal_date) : null;

                    if ( $tender_point !== null ) {
                        $footer_text = 'TENDER POINT - ' . strtoupper($tender_point) . "\n";
                    }

                    if ( $appraisal_date !== null ) {
                        $footer_text .= 'APPRAISAL DATE - ' . $appraisal_date . "\n";
                    }

                    $footer_text .= 'COMMODITY - ' . $contract->product->description . ' @ ' . $billing_document->currency . ' ' . number_format($billing_document->unit_price, 2) . ' PER ' . $billing_document->price_uom;

                    if (!empty($inventory_location_name)) {
                        $footer_text .=  "\n" . 'Dispatches From - ' . $inventory_location_name;
                    }

                    break;
                default:
                    $footer_text = '';
                    break;
            }

        }

        return $footer_text;
    }

    public static function getEinvoiceBillingType(BillingDocument $billing_document, $legal_entity_code)
    {
        // if is initial invoice, set the type to invoice
        $billing_document_type = $billing_document->type === BillingDocument::TYPE_INITIAL_INVOICE ? BillingDocument::TYPE_NORMAL_INVOICE : $billing_document->type;

        if( in_array($legal_entity_code, ['A', 'F', 'E', 'H', 'T', 'S', 'I']) && $billing_document_type == BillingDocument::TYPE_NORMAL_INVOICE ){
            return 'COMMERCIAL INVOICE';
        }
        return str_replace("_", " ", $billing_document_type);
    }

    public static function getPackingListBulkDefaultValue($contract, $vessel_nomination)
    {
        $default_value = [];
        $contract->loadMissing(['legalEntity', 'billOfLadings', 'product', 'packingUnit']);
        $legal_entity = $contract->legalEntity;

        // Vessel
        $vessel_nomination_tender_ids = $vessel_nomination->tenders->pluck('id')->toArray();
        $vessel = $vessel_nomination->vessel;
        $vessel_details = $vessel->name;
        if(!empty($vessel->voyage_number)){
            $vessel_details .= " (Voyage No.: {$vessel->voyage_number})";
        }

        // Bill of Lading
        $bill_of_ladings = $contract->billOfLadings->whereIn('vessel_nomination_tender_id', $vessel_nomination_tender_ids);
        $bl_no_with_bl_date = '';
        $bill_of_ladings_stowage_tank = '';
        $bill_of_ladings_stowage_tank_weight = 0;

        foreach($bill_of_ladings as $bill_of_lading){

            if($bl_no_with_bl_date !== ''){
                $bl_no_with_bl_date .= "\n";
            }

            if($bill_of_ladings_stowage_tank !== '' && !empty($bill_of_lading->stowage) ){
                $bill_of_ladings_stowage_tank .= ', ';
            }

            $bl_no_with_bl_date .= $bill_of_lading->reference_no;
            $bl_no_with_bl_date .= isset($bill_of_lading->date) ? ' dated '. Carbon::parse($bill_of_lading->date)->timezone(auth()->user()->timezone)->format('d M Y') : '';
            $bill_of_ladings_stowage_tank .= $bill_of_lading->stowage;
            $bill_of_ladings_stowage_tank_weight = bcadd($bill_of_ladings_stowage_tank_weight, $bill_of_lading->quantity, 2);
        }

        // Discharge Ports
        $discharge_ports_string = '';
        if($discharge_ports = $vessel_nomination->getDischargePorts()){
            foreach($discharge_ports as $discharge_port){
                if( $discharge_ports_string !== ''){
                    $discharge_ports_string .= ';';
                }
                $discharge_ports_string .= $discharge_port->name . ", ". $discharge_port->country->name;
            }
        }

        $customer_type_list = \App\Models\ShippingDocument::PACKING_LIST_CUSTOMER_TYPE;

        $default_value['title'] = 'Packing List';
        $default_value['document_date'] = Carbon::now(Auth::user()->timezone)->format(config('spot.date'));
        $default_value['le_code'] = $legal_entity->code;
        $default_value['logo'] = self::getLogo($legal_entity, $contract->contract_date);
        $default_value['le_name'] = self::getLegalEntityName($legal_entity, $contract->contract_date);
        $default_value['former_name'] = self::getFormerlyKnownAs($legal_entity, $contract->contract_date);
        $default_value['le_company_registration_number'] = $legal_entity->company_registration_number ?? '';
        $default_value['cj_no'] = $legal_entity->tax_registration_number ?? '';
        $default_value['le_address'] = $legal_entity->address ?? '';
        $default_value['le_website'] = self::getWebsite($legal_entity, $contract->contract_date);
        $default_value['le_phone'] = $legal_entity->phone_1 ?? '';
        $default_value['le_phone_2'] = $legal_entity->phone_2 ?? '';
        $default_value['le_fax'] = $legal_entity->fax_1 ?? '';
        $default_value['le_fax_2'] = $legal_entity->fax_2 ?? '';
        $default_value['contract_no'] = $contract->contract_number;
        $default_value['vessel_name_with_voyage_no'] = $vessel_details;
        $default_value['description_of_goods'] = $contract->product->description . ' IN ' . $contract->packing_unit;
        $default_value['load_port'] = $vessel_nomination->loadPort ? ($vessel_nomination->loadPort->name . ', ' . $vessel_nomination->loadPort->country->name) : null;
        $default_value['discharge_port'] = $discharge_ports_string;
        $default_value['bl_no_with_bl_date'] = $bl_no_with_bl_date;
        $default_value['stowage_tank'] = $bill_of_ladings_stowage_tank;
        $default_value['weight'] =  \App\Helpers\NumberHelper::prettify($bill_of_ladings_stowage_tank_weight, 4) . ' ' . $contract->quantity_uom;
        $default_value['packing'] = $contract->packingUnit ? ("IN ". $contract->packingUnit->description) : '';
        $default_value['signature_required'] = "1";
        $default_value['customer_type'] = $customer_type_list[0];
        $default_value['customer'] = $contract->counterparty->long_name;

        return $default_value;
    }

    public static function getPackingListContainerDefaultValue($contract, $vessel_nomination)
    {
        $default_value = [];
        $contract->loadMissing(['legalEntity', 'billOfLadings', 'tenders', 'product', 'loads', 'counterparty', 'packingUnit']);
        $legal_entity = $contract->legalEntity;

        // Vessel
        $vessel_nomination_tender_ids = $vessel_nomination->tenders->pluck('id')->toArray();
        $vessel = $vessel_nomination->vessel;
        $vessel_details = $vessel->name;
        if(!empty($vessel->voyage_number)){
            $vessel_details .= " (Voyage No.: {$vessel->voyage_number})";
        }

        // Bill of Lading
        $bill_of_ladings = $contract->billOfLadings->whereIn('vessel_nomination_tender_id', $vessel_nomination_tender_ids);
        $bl_no_with_bl_date = '';
        foreach($bill_of_ladings as $bill_of_lading){

            if($bl_no_with_bl_date !== ''){
                $bl_no_with_bl_date .= "\n";
            }

            $bl_no_with_bl_date .= $bill_of_lading->reference_no;
            $bl_no_with_bl_date .= isset($bill_of_lading->date) ? ' dated '. Carbon::parse($bill_of_lading->date)->timezone(auth()->user()->timezone)->format('d M Y') : '';
        }

        $tender = $contract->tenders->where('vessel_nomination_id', $vessel_nomination->id)->first();
        $tendered_quantity = $tender ? \App\Helpers\NumberHelper::prettify($tender->quantity, 4) : 0;

        $description_of_goods = '';
        $description_of_goods .= $tendered_quantity . ' ' . $contract->quantity_uom . ' OF ';
        $description_of_goods .= $contract->product->description . "\n";
        $description_of_goods .= "PACKED IN " . $contract->packing_unit;

        $customer_type_list = \App\Models\ShippingDocument::PACKING_LIST_CUSTOMER_TYPE;

        // Load Containers
        $tender_containers_code = $contract->vesselContainers()->wherePivotIn('vessel_nomination_tender_id', $vessel_nomination_tender_ids)->pluck('code')->toArray();
        $load_containers = [];

        foreach($contract->loads as $load){
            if ( !empty($load->vehicle_no) && in_array($load->vehicle_no, $tender_containers_code) ){
                $load_containers[] = [
                    'container_number' => $load->vehicle_no,
                    'seal_number' => $load->seal_no,
                    'net_weight' => bcmul($load->dispatched_received_quantity, 1000), // convert MT to KG
                    'tare_weight' => 0,
                    'gross_weight' => 0,
                    'measurement' => 0,
                ];
            }
        }

        // Discharge Ports
        $discharge_ports_string = '';
        if($discharge_ports = $vessel_nomination->getDischargePorts()){
            foreach($discharge_ports as $discharge_port){
                if( $discharge_ports_string !== ''){
                    $discharge_ports_string .= ';';
                }
                $discharge_ports_string .= $discharge_port->name . ", ". $discharge_port->country->name;
            }
        }

        $default_value['title'] = 'Packing List';
        $default_value['document_date'] = Carbon::now(Auth::user()->timezone)->format(config('spot.date'));
        $default_value['le_code'] = $legal_entity->code;
        $default_value['logo'] = self::getLogo($legal_entity, $contract->contract_date);
        $default_value['le_name'] = self::getLegalEntityName($legal_entity, $contract->contract_date);
        $default_value['former_name'] = self::getFormerlyKnownAs($legal_entity, $contract->contract_date);
        $default_value['le_company_registration_number'] = $legal_entity->company_registration_number ?? '';
        $default_value['cj_no'] = $legal_entity->tax_registration_number ?? '';
        $default_value['le_address'] = $legal_entity->address ?? '';
        $default_value['le_website'] = self::getWebsite($legal_entity, $contract->contract_date);
        $default_value['le_phone'] = $legal_entity->phone_1 ?? '';
        $default_value['le_phone_2'] = $legal_entity->phone_2 ?? '';
        $default_value['le_fax'] = $legal_entity->fax_1 ?? '';
        $default_value['le_fax_2'] = $legal_entity->fax_2 ?? '';
        $default_value['contract_no'] = $contract->contract_number;
        $default_value['manufacturer'] = $legal_entity->name;
        $default_value['beneficiary'] = $legal_entity->name;
        $default_value['customer_type'] = $customer_type_list[0];
        $default_value['customer'] = $contract->counterparty->long_name;
        $default_value['vessel_name_with_voyage_no'] = $vessel_details;
        $default_value['description_of_goods'] = $description_of_goods;
        $default_value['load_port'] = $vessel_nomination->loadPort ? ($vessel_nomination->loadPort->name . ', ' . $vessel_nomination->loadPort->country->name) : null;
        $default_value['discharge_port'] = $discharge_ports_string;
        $default_value['final_destination'] = '';
        $default_value['bl_no_with_bl_date'] = $bl_no_with_bl_date;
        $default_value['packing'] = $contract->packingUnit ? ("IN ". $contract->packingUnit->description) : '';
        $default_value['signature_required'] = "1";
        $default_value['load_containers'] = $load_containers;

        return $default_value;
    }

    public static function getPackingListContainerTableDetailsString($containers)
    {
        $container_table_details = '';
        foreach($containers as $index => $container){
            if($container_table_details != ''){
                $container_table_details .= "|";
            }
            $index += 1;
            $container_table_details .= "item_no={$index}";
            $container_table_details .= ";container_number=" . (empty($container['container_number']) ? '-' : $container['container_number']);
            $container_table_details .= ";seal_number=" . (empty($container['seal_number']) ? '-' : $container['seal_number']);
            $container_table_details .= ";net_weight=" . (empty($container['net_weight']) ? 0 : $container['net_weight']);
            $container_table_details .= ";tare_weight=" . (empty($container['tare_weight']) ? 0 : $container['tare_weight']);
            $container_table_details .= ";gross_weight=" . (empty($container['gross_weight']) ? 0 : $container['gross_weight']);
            $container_table_details .= ";measurement=" . (empty($container['measurement']) ? 0 : $container['measurement']);
        }
        return $container_table_details;
    }

    public static function getVesselRemarks($vessel)
    {
        $remarks = NumberHelper::prettify($vessel->dead_weight, 2) . ' SDWT, ' . NumberHelper::prettify($vessel->draft, 2) . ' M SDRAFT' . PHP_EOL . NumberHelper::prettify($vessel->length_overall, 2) . ' M LOA, ' . NumberHelper::prettify($vessel->beam, 2) . ' M BEAM';

        return $remarks;
    }

    public static function getVesselTolerance($vessel)
    {
        $tolerance = '';
        if ($vessel->tolerance_type == \App\Models\Vessel::TOLERANCE_TYPE_ABSOLUTE) {
            $tolerance =  $vessel->tolerance_from . ' MT';
        } elseif ($vessel->tolerance_type == \App\Models\Vessel::TOLERANCE_TYPE_PERCENTAGE) {
            $tolerance = $vessel->tolerance_from . ' MT to ' . $vessel->tolerance_to . ' MT';
        }

        return $tolerance;
    }

    public static function checkShowTaxBase($date, $is_billing = false)
    {
        // force this show tax base for price and tax amount instead of following the toggle
        if ($is_billing) {
            $document_date = Carbon::parse($date, 'UTC')->tz(Auth::user()->timezone);
            $new_tax_cutoff = Carbon::parse('2025-01-01', Auth::user()->timezone);
        } else {
            $document_date = Carbon::parse($date)->startOfDay();
            $new_tax_cutoff = Carbon::parse('2025-01-01')->startOfDay();
        }

        return $document_date->gte($new_tax_cutoff);
    }

    public static function getTaxDescriptionByShowTaxBase($invoice)
    {
        $minamas_applicable_tax = DocumentHelper::getMinamasBillingTaxApplicableTaxModel($invoice);
        $show_tax_base = DocumentHelper::checkShowTaxBase($invoice->document_date);


        $tax_rate = $show_tax_base ? $minamas_applicable_tax->name : ($invoice->percentage ?? 0) . '%';

        return $tax_rate;
    }

    public static function getTaxShortDescription($contract_date, ?Tax $tax)
    {
        return isset($tax) && Carbon::parse($contract_date)->gte(Carbon::parse('2025-07-01')) ? $tax->short_description : null;
    }
}
