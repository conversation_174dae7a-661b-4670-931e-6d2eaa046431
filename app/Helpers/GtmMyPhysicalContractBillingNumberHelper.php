<?php

namespace App\Helpers;

use App\Interfaces\BillingNumberInterface;
use App\Models\BillingDocument;
use App\Models\PhysicalContract;
use App\Services\ContractNumberService;
use Carbon\Carbon;

class GtmMyPhysicalContractBillingNumberHelper implements BillingNumberInterface
{
    private $contractNumberService;

    const RUNNING_NUMBER_DIGITS = 6;

    public function __construct(ContractNumberService $contractNumberService)
    {
        $this->contractNumberService = $contractNumberService;
    }

    public function generateBillingNumber(PhysicalContract $contract, string $billing_document_type, Carbon $documentDate, $ar_ap_overwrite = null): string
    {
        // TODO: TO BE REFACTORED FROM ContractNumberHelper::generateInvoiceNumber BUT I FORESEE THIS WON'T GET DONE.
        // COZ TO MINIMIZE CHANGES TO GTM MY CODE

    }

}
