<?php
namespace App\Helpers;

use TeamTNT\TNTSearch\Support\TokenizerInterface;

class SmartSearchTokenizer implements TokenizerInterface
{
    public function tokenize($text, $stopwords = [])
    {
        $patterns = $this->getPattern();

        $split1 = preg_split($patterns[0], strtolower($text), -1, PREG_SPLIT_NO_EMPTY);
        $split2 = preg_split($patterns[1], strtolower($text), -1, PREG_SPLIT_NO_EMPTY);

        // Index one group with slash and one group without slash to enable partial search of contract number, inv number.
        $split = array_unique(array_merge($split1, $split2), SORT_REGULAR);

        // remove indexing of 1 character - reduce index size
        $split = collect($split)->filter(function($value){
            return strlen($value) > 1;
        });

        return $split->toArray();
    }

    public function getPattern()
    {
        return [
            "/[^\p{L}\p{N}\-\.\&\/]+/u",
            "/[^\p{L}\p{N}\-\.\&]+/u"
        ];
    }
}
