<?php

namespace App\Helpers;

use App\Models\User;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DateHelper
{

    public static function addTimeToDate($date, $isStart = true)
    {
        return $isStart ? $date . ' 00:00:00' : $date . ' 23:59:59';
    }

    public static function getDateInUTC($date)
    {
        // Force all tax master datetime to be in Malaysia timezone
        $date_in_tz = Carbon::parse($date, 'Asia/Kuala_Lumpur');
        $utc_date = $date_in_tz->tz('UTC');
        return $utc_date->toDateTimeString();
    }

    public static function getWeekendDates($date_start_str, $date_end_str)
    {
        $date_start = Carbon::parse($date_start_str, Auth::user()->timezone);
        $date_end = Carbon::parse($date_end_str, Auth::user()->timezone);

        $dates = [];

        for ($date = $date_start; $date->lte($date_end); $date->addDay()) {

            $weekend = [];

            if ($date->isSaturday()) {
                $weekend['day'] = 'Saturday';
            }

            if ($date->isSunday()) {
                $weekend['day'] = 'Sunday';
            }

            if (!empty($weekend['day'])) {
                $weekend['date_start'] = self::getDateInUTC(self::addTimeToDate($date->format('Y-m-d')));
                $weekend['date_end'] = self::getDateInUTC(self::addTimeToDate($date->format('Y-m-d'), false));
                $dates[] = $weekend;
            }
        }

        return $dates;
    }

    public static function generateShipmentMonths($timezone, $backwards = 0, $forwards = 0, $reverse_order = true)
    {

        $shipment_months = [];


        // forward months
        for ($i = $forwards; $i >= 0; $i--) {

            $date = Carbon::now($timezone)->firstOfMonth()->addMonths($i);

            $shipment_months[] = [
                'name' => $date->format('M Y'),
                'value' => $date->toDateString()
            ];
        }

        // past months
        for ($i = 1; $i <= $backwards; $i++) {

            $date = Carbon::now($timezone)->firstOfMonth()->subMonths($i);

            $shipment_months[] = [
                'name' => $date->format('M Y'),
                'value' => $date->toDateString()
            ];
        }

        // by default shipment month is created in reverse
        if ( $reverse_order ) {
            return $shipment_months;
        }else{
            return array_reverse($shipment_months);
        }
    }

    public static function removeMilliSecondFromDate($date_string)
    {
        return substr($date_string, 0, strlen($date_string) - 4);
    }

    public static function getDateFromPeriod($date_period)
    {
        $date = explode('-', $date_period);
        return [
            Carbon::parse($date[0])->startOfDay()->toDateTimeString(),
            Carbon::parse($date[1])->endOfDay()->toDateTimeString()
        ];
    }

    public static function getCarbonDateFromPeriod($date_period, $input_timezone = 'UTC')
    {
        $date = explode('-', $date_period);
        return [
            Carbon::parse($date[0], $input_timezone)->startOfDay(),
            Carbon::parse($date[1], $input_timezone)->endOfDay()
        ];
    }

    public static function getMonthSelections($months)
    {

        $startDate = Carbon::now()->subMonths($months);
        $endDate = Carbon::now()->addMonths($months);

        $selections = [];

        while (!$startDate->isSameMonth($endDate)) {

            if (end($selections) != $startDate->format('M Y')) {
                $selections[] = $startDate->format('M Y');
            }

            $startDate = $startDate->addDays(20);
        }

        return $selections;
    }

    public static function getMonthsBetweenDates($from, $to, $format = 'M Y')
    {
        $months = [];

        foreach (CarbonPeriod::create($from->toDateString(), '1 month', $to->toDateString()) as $month) {
            $months[] = $month->format($format);
        }

        return $months;
    }

    public static function getNearestPreviousDay($day)
    {
        $nearest = Carbon::now();

        while ($nearest->day != $day) {
            $nearest = $nearest->subDays(1);
        }

        return $nearest->startOfDay();
    }

    public static function getNearestNextDay($day)
    {
        $nearest = Carbon::now();

        while ($nearest->day != $day) {
            $nearest = $nearest->addDays(1);
        }

        return $nearest->endOfDay();
    }

    /**
     * Count working days between 2 given date, excluding public holiday & rest day is Saturday & Sunday
     *
     * @param string|\Carbon\Carbon $dateFrom
     * @param string|\Carbon\Carbon $dateEnd
     * @param string $localTimezone
     * @return int
     */
    public static function countWeekDays($dateFrom, $dateEnd, $localTimezone)
    {
        $weekends = self::getWeekendDate($dateFrom, $dateEnd, $localTimezone);

        $daysInCalendar = self::getCalendarDaysCount($dateFrom, $dateEnd, $localTimezone);

        return $daysInCalendar - count($weekends);

    }


    public static function getWeekendDate($dateFrom, $dateEnd, $localTimezone, $format = 'Y-m-d')
    {
        $weekends = [
            6,0 // saturday, sunday
        ];

        $from = Carbon::parse($dateFrom)->tz($localTimezone)->startOfDay();
        $to = Carbon::parse($dateEnd)->tz($localTimezone)->startOfDay();

        $diffInDays = $from->diffInDays($to);

        $weekendInDays = [];

        for ($i=0; $i <= $diffInDays ; $i++) {

            $currentDay = $from->copy()->addDays($i);

           if (in_array($currentDay->dayOfWeek, $weekends)) {
               array_push($weekendInDays, $currentDay->format($format));
           }
        }

        return $weekendInDays;
    }

    public static function getHolidayDate($dateFrom, $dateEnd, User $user)
    {
        $from = Carbon::parse($dateFrom)->tz($user->timezone)->startOfDay();
        $to = Carbon::parse($dateEnd)->tz($user->timezone)->startOfDay();

        return $user->calendars->flatMap(function($calendar) use ($from, $to) {
            return $calendar->holidays()
                ->select(DB::raw('date_start as holiday'))
                ->where('date_start', '>=', $from)
                ->where('date_end', '<=', $to)
                ->get();
        })->pluck('holiday')->unique()
        ->map(function($date) use ($user) {
            return Carbon::parse($date)->tz($user->timezone)->format('Y-m-d');
        })->toArray();
    }

    public static function getCalendarDaysCount($dateFrom, $dateEnd, $localTimezone)
    {
        $from = Carbon::parse($dateFrom)->tz($localTimezone)->startOfDay();
        $to = Carbon::parse($dateEnd)->tz($localTimezone)->startOfDay();

        return $to->diffInDays($from);
    }


    public static function countWorkingDays(Carbon $dateFrom, Carbon $dateEnd, User $user)
    {
        $totalCalendarDays = self::getCalendarDaysCount($dateFrom, $dateEnd, $user->timezone);

        $weekendDates = self::getWeekendDate($dateFrom, $dateEnd, $user->timezone);
        $holidayDates = self::getHolidayDate($dateFrom, $dateEnd, $user);

        $weekdayHoliday = array_diff($holidayDates, $weekendDates);

        return ($totalCalendarDays - count($weekendDates) - count($weekdayHoliday));

    }

    /**
     * @param $from_timezone
     * @param $to_timezone
     * @param Carbon $utc_time
     * @return Carbon|string
     */
    public static function convertDateTimezone($from_timezone, $to_timezone, Carbon $utc_time)
    {
        if ($from_timezone == $to_timezone) {
            return $utc_time;
        }
        return Carbon::parse($utc_time->tz($from_timezone)->toDateString(), $to_timezone);
    }
}
