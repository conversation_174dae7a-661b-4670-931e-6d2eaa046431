<?php

namespace App\Helpers;

use App\Helpers\StateSalesTaxHelper;
use App\Interfaces\Contractable;
use App\Models\BillingDocument;
use App\Models\BillOfLading;
use App\Models\PhysicalContract;
use App\Models\PhysicalLoad;
use App\Models\PhysicalLoadAdjustment;

class CreditDebitNoteHelper
{

    public static function getInventoryLocationId(Contractable $contract, $invoice, $line_item) {

        // For trading contracts with no allocation, use -1 and ignore line item loads inventory location
        if ( $contract->getClass() == PhysicalContract::class && $contract->objective == PhysicalContract::OBJECTIVE_TRADING && $contract->allocated_quantity == 0 ){
            $inventory_location_id = -1;
        }else{

            if ( $line_item->billable_type == PhysicalLoadAdjustment::class && !empty($line_item->billable->physicalLoad->inventory_location_id) ){
                $inventory_location_id = $line_item->billable->physicalLoad->inventory_location_id;
            }else{

                // Use invoice inventory location as backup
                $inventory_location_id = !empty($line_item->billable->inventory_location_id)
                    ? $line_item->billable->inventory_location_id
                    : (!empty($line_item->inventory_location_id) ? $line_item->inventory_location_id  : (isset($invoice->inventory_location_id) ? $invoice->inventory_location_id : -1));
            }

        }

        return $inventory_location_id;

    }

    public static function getLandPriceAdjusmentViewDataEInvoicing(Contractable $contract) {

        $return_data = [];

        $billing_documents = $contract->billingDocuments;
        $fixed_price = $contract->getContractPrice(true);

        $invoices = $billing_documents->filter(function ($value) {
            return in_array($value->type, [BillingDocument::TYPE_NORMAL_INVOICE, BillingDocument::TYPE_PAYMENT_VOUCHER]) && in_array($value->status, BillingDocument::INVOICE_STATUS_NOT_DRAFT_AND_VOIDED);
        })->values()->loadMissing('lineItems.billable');

        $invoices = $invoices->filter(function($value) {
            // Dont take into consideration invoices that do not have billable type - this is to avoid counting the transport cost only invoices
            return $value->lineItems->whereIn('billable_type', [PhysicalLoad::class, BillOfLading::class])->first() !== null;
        });

        $debit_notes = $billing_documents->filter(function ($value) {
            return ($value->type == BillingDocument::TYPE_DEBIT_NOTE || $value->type == BillingDocument::TYPE_NORMAL_INVOICE)
                && in_array($value->status, BillingDocument::INVOICE_STATUS_NOT_DRAFT_AND_VOIDED);
        })->values()->loadMissing('lineItems.billable');

        $credit_notes = $billing_documents->filter(function ($value) {
            return $value->type == BillingDocument::TYPE_CREDIT_NOTE && in_array($value->status, BillingDocument::INVOICE_STATUS_NOT_DRAFT_AND_VOIDED);
        })->values()->loadMissing('lineItems.billable');

        // get all load adjustments from CN/DN
        $loads_with_adjustments = [];
        $bl_with_discharge = [];

        foreach ( $debit_notes as $debit_note ) {
            foreach ( $debit_note->lineItems->where('billable_type', PhysicalLoadAdjustment::class) as $line_item ) {

                $load_adjustment = $line_item->billable;

                if ( $load_adjustment->load_id !== null ){
                    if ( !isset($loads_with_adjustments[$load_adjustment->load_id]) ){
                        $loads_with_adjustments[$load_adjustment->load_id] = [];
                    }
                    $loads_with_adjustments[$load_adjustment->load_id][] = [
                        'adjusted_quantity' => $load_adjustment->adjusted_quantity,
                        'unit_price' => $debit_note->unit_price,
                        'adjustment_amount_before_tax' => $line_item->amount_before_tax,
                    ];
                }
                else if ( $load_adjustment->bill_of_lading_id !== null ) {
                    if ( !isset($bl_with_discharge[$load_adjustment->bill_of_lading_id]) ){
                        $bl_with_discharge[$load_adjustment->bill_of_lading_id] = [];
                    }
                    $bl_with_discharge[$load_adjustment->bill_of_lading_id][] = [
                        'adjusted_quantity' => 0,   // set to 0 for BL because when have discharge, we wanna do price settlement on discharge weight
                        'unit_price' => $debit_note->unit_price,
                        'adjustment_amount_before_tax' => round($load_adjustment->adjusted_quantity * $debit_note->unit_price, 2),
                        'discharge_value_amount_before_tax' => $line_item->amount_before_tax,
                    ];
                }

            }
        }

        foreach ( $credit_notes as $credit_note ) {
            foreach ( $credit_note->lineItems->where('billable_type', PhysicalLoadAdjustment::class) as $line_item ) {

                $load_adjustment = $line_item->billable;

                if ( $load_adjustment->load_id !== null ){

                    if ( !isset($loads_with_adjustments[$load_adjustment->load_id]) ){
                        $loads_with_adjustments[$load_adjustment->load_id] = [];
                    }

                    $loads_with_adjustments[$load_adjustment->load_id][] = [
                        'adjusted_quantity' => $load_adjustment->adjusted_quantity,
                        'unit_price' => $credit_note->unit_price,
                        'adjustment_amount_before_tax' => $line_item->amount_before_tax,
                    ];
                }
                else if ( $load_adjustment->bill_of_lading_id !== null ) {
                    if ( !isset($bl_with_discharge[$load_adjustment->bill_of_lading_id]) ){
                        $bl_with_discharge[$load_adjustment->bill_of_lading_id] = [];
                    }
                    $bl_with_discharge[$load_adjustment->bill_of_lading_id][] = [
                        'adjusted_quantity' => 0,   // set to 0 for BL because when have discharge, we wanna do price settlement on discharge weight
                        'unit_price' => $credit_note->unit_price,
                        'adjustment_amount_before_tax' => 0,
                        'discharge_value_amount_before_tax' => $line_item->amount_before_tax,
                    ];
                }
            }
        }

        // calculate price diff / MT
        foreach ($invoices as $invoice) {
            foreach ($invoice->lineItems as $line_item) {
                if (!empty($line_item->billable_id)) {

                    $inventory_location_id = self::getInventoryLocationId($contract, $invoice, $line_item);

                    if (empty($return_data[$inventory_location_id])) {
                        $return_data[$inventory_location_id] = [];
                    }
                    if (empty($return_data[$inventory_location_id][$invoice->id])) {
                        $return_data[$inventory_location_id][$invoice->id] = [
                            'net_billed_qty' => 0,
                            'billed_qty' => 0,
                            'adjustments' => [],
                            'loads_id' => [],
                            'sst_value' => 0,
                            'goods_value' => 0,
                            'billing_document' => $invoice,
                            'provisional_price' => $invoice->unit_price,
                            'fixed_price' => $fixed_price,
                            'fixed_price_no_sst' => 0,
                            'price_diff_per_mt' => 0,
                        ];
                    }

                    if ( $line_item->billable_type === PhysicalLoad::class ){
                        $return_data[$inventory_location_id][$invoice->id]['loads_id'][] = $line_item->billable_id;
                    }

                    if ( !empty($line_item->quantity) ) {

                        // if is BL, check whether discharged. If discharged, should use discharge quantity
                        if ( $line_item->billable_type === BillOfLading::class && $line_item->billable !== null && $line_item->billable->discharge !== null ){
                            $return_data[$inventory_location_id][$invoice->id]['billed_qty'] += $line_item->billable->discharge->quantity;
                        }else{
                            $return_data[$inventory_location_id][$invoice->id]['billed_qty'] += $line_item->quantity;
                        }

                    } else {

                        $quantity = 0;

                        if ($line_item->billable_type === PhysicalLoad::class) {
                            $quantity = !empty($line_item->billable->adjusted_quantity) ? $line_item->billable->adjusted_quantity : $line_item->billable->invoiceable_quantity;
                        } else if ($line_item->billable_type == BillOfLading::class) {

                            $discharge = $line_item->billable->discharge;

                            // If discharge weight is available, use discharge weight, else use BL weight
                            if ( $discharge != null ){
                                $quantity = $discharge->quantity;
                            }else{
                                $quantity = $line_item->billable->quantity;
                            }

                        }

                        $return_data[$inventory_location_id][$invoice->id]['billed_qty'] += $quantity;
                    }


                    // if loads or BL, check for adjustments
                    if ( $line_item->billable_type === PhysicalLoad::class && isset($loads_with_adjustments[$line_item->billable_id]) ){
                        foreach ( $loads_with_adjustments[$line_item->billable_id] as $adjustment_data ) {
                            $return_data[$inventory_location_id][$invoice->id]['adjustments'][] = $adjustment_data;
                        }
                    }
                    else if ( $line_item->billable_type === BillOfLading::class && isset($bl_with_discharge[$line_item->billable_id]) ){

                        foreach ( $bl_with_discharge[$line_item->billable_id] as $adjustment_data ) {
                            $adjustment_data['adjustment_amount_before_tax'] = $adjustment_data['discharge_value_amount_before_tax'] - $line_item->amount_before_tax;
                            $return_data[$inventory_location_id][$invoice->id]['adjustments'][] = $adjustment_data;
                        }
                    }

                }

            }

        }

        // calculate prices and totals after we got the quantity
        // SST price must be BY INVENTORY LOCATION in case 1 contract got multiple invloc
        $debit_notes_by_invloc = $debit_notes->groupBy('inventory_location_id');
        $credit_notes_by_invloc = $credit_notes->groupBy('inventory_location_id');

        foreach ( $return_data as $inventory_location_id => &$invoices ) {

            // get sst_amount
            $sst_pmt = 0;
            $cpko_price = 0;
            $transport_cost_pmt = 0;

            if (get_class($contract) === PhysicalContract::class && $contract->canSSTSettlement() ) {
                $sst = StateSalesTaxHelper::getSSTSettlementDetails($contract, $contract->tax, $inventory_location_id);
                $sst_pmt = $sst['sst_value'];
                $cpko_price = $sst['cpko_price'];
            }
            if ( get_class($contract) === PhysicalContract::class && $contract->canSeperateTransportCost() ){
                $transport_cost_pmt = $contract->transport_cost;
            }

            // dont deduct sst pmt here first
            $fixed_price_no_sst = round($fixed_price + $sst_pmt, 2);

            $return_data[$inventory_location_id]['price'] = [
                'fixed_price' => $fixed_price,
                'fixed_price_no_sst' => $fixed_price_no_sst,
                'sst_per_mt' => $sst_pmt,
                'sst_cpko_price' => $cpko_price,
                'transport_cost_per_mt' => $transport_cost_pmt,
                'discount' => $contract->discount,
                'premium' => $contract->premium,
                'actual_billed_total' => 0,
                'settlement_total_with_sst' => 0,
            ];

            foreach ( $invoices as $invoice_id => &$details ){

                if ( $invoice_id === 'price' ){
                    continue;
                }

                // calculate net billed qty
                $details['fixed_price_no_sst'] = $fixed_price_no_sst;
                $details['price_diff_per_mt'] = $fixed_price_no_sst - $details['provisional_price'];
                $details['net_billed_qty'] = $details['billed_qty'] + collect($details['adjustments'])->sum('adjusted_quantity');
                $details['sst_value'] = round(-$sst_pmt * $details['net_billed_qty'], 2);

                $details['goods_value_benchmark'] = round($details['price_diff_per_mt'] * $details['net_billed_qty'], 2);   // not used, but more as a comparison

                // calculate net invoice value = invoice amount before tax + offset with any CN/DN value for that line item with load adjustment
                $billed_net_invoice_value = $details['billing_document']->amount_before_tax;

                foreach ( $details['adjustments'] as $adjustment_details ){
                    $billed_net_invoice_value += $adjustment_details['adjustment_amount_before_tax'];
                }

                // goods value has 2 parts = (net biilled qty x fixed_price) - (invoiced value + offset with any CN/DN value for that line item with load adjustment)
                $details['goods_value'] = round(round($details['net_billed_qty'] * $fixed_price_no_sst, 2) - $billed_net_invoice_value, 2);

                $return_data[$inventory_location_id]['price']['settlement_total_with_sst'] += ($details['net_billed_qty'] * $details['fixed_price']);
                $return_data[$inventory_location_id]['price']['actual_billed_total'] += $details['billing_document']->amount_before_tax;
            }

            // calculate CN/DN total by invloc
            $cn_total_by_invloc = isset($credit_notes_by_invloc[$inventory_location_id]) ? $credit_notes_by_invloc[$inventory_location_id]->sum('amount_before_tax') : 0;
            $dn_total_by_invloc = isset($debit_notes_by_invloc[$inventory_location_id]) ? $debit_notes_by_invloc[$inventory_location_id]->sum('amount_before_tax') : 0;

            $return_data[$inventory_location_id]['price']['actual_billed_total'] += ($dn_total_by_invloc - $cn_total_by_invloc);

            $return_data[$inventory_location_id]['price']['settlement_total_with_sst'] = round($return_data[$inventory_location_id]['price']['settlement_total_with_sst'], 2);
            $return_data[$inventory_location_id]['price']['actual_billed_total'] = round($return_data[$inventory_location_id]['price']['actual_billed_total'], 2);

        }

        return $return_data;

    }


    public static function getLandPriceAdjusmentViewData(Contractable $contract)
    {
        $return_data = [];

        $debit_note_total_by_invloc = [];
        $invoice_total_by_invloc = [];
        $credit_note_total_by_invloc = [];

        $billing_documents = $contract->billingDocuments;

        $current_fixed_price = $contract->getContractPrice(true);

        $invoices = $billing_documents->filter(function ($value) {
            return $value->type == BillingDocument::TYPE_NORMAL_INVOICE && in_array($value->status, BillingDocument::INVOICE_STATUS_NOT_DRAFT_AND_VOIDED);
        })->loadMissing('lineItems.billable');

        $debit_notes = $billing_documents->filter(function ($value) {
            return $value->type == BillingDocument::TYPE_DEBIT_NOTE && in_array($value->status, BillingDocument::INVOICE_STATUS_NOT_DRAFT_AND_VOIDED);
        })->loadMissing('lineItems.billable');

        $credit_notes = $billing_documents->filter(function ($value) {
            return $value->type == BillingDocument::TYPE_CREDIT_NOTE && in_array($value->status, BillingDocument::INVOICE_STATUS_NOT_DRAFT_AND_VOIDED);
        })->loadMissing('lineItems.billable');

        // quantity has to be pulled from invoices, CN and DN also to include load adjustments, load returns
        // Get latest quantity * latest price
        foreach ($invoices as $invoice) {
            foreach ($invoice->lineItems as $line_item) {

                if (!empty($line_item->billable_id)) {

                    $inventory_location_id = self::getInventoryLocationId($contract, $invoice, $line_item);

                    if (empty($return_data[$inventory_location_id])) {
                        $return_data[$inventory_location_id]['total_latest_quantity'] = 0;
                    }

                    if ( !empty($line_item->billable_id) && !empty($line_item->quantity)) {

                        // if is BL, check whether discharged. If discharged, should use discharge quantity
                        if ( $line_item->billable_type === BillOfLading::class && $line_item->billable !== null && $line_item->billable->discharge !== null ){
                            $return_data[$inventory_location_id]['total_latest_quantity'] += $line_item->billable->discharge->quantity;
                        }else{
                            $return_data[$inventory_location_id]['total_latest_quantity'] += $line_item->quantity;
                        }

                    } else {

                        $quantity = 0;

                        if ($line_item->billable_type == PhysicalLoad::class) {
                            $quantity = !empty($line_item->billable->adjusted_quantity) ? $line_item->billable->adjusted_quantity : $line_item->billable->invoiceable_quantity;
                        } else if ($line_item->billable_type == BillOfLading::class) {

                            $discharge = $line_item->billable->discharge;

                            // If discharge weight is available, use discharge weight, else use BL weight
                            if ( $discharge != null ){
                                $quantity = $discharge->quantity;
                            }else{
                                $quantity = $line_item->billable->quantity;
                            }

                        }

                        $return_data[$inventory_location_id]['total_latest_quantity'] += $quantity;
                    }
                }
            }
        }

        // get offset amount
        $invoice_offset = 0;

        foreach ($invoices as $invoice) {
            $line_item = $invoice->lineItems->whereIn('billable_type', [PhysicalLoad::class, BillOfLading::class])->first();

            // Dont take into consideration invoices that do not have billable type - this is to avoid counting the transport cost only invoices
            if ( $line_item == null ){
                continue;
            }

            $invoice_offset += $invoice->amount_before_tax;

            $inventory_location_id = self::getInventoryLocationId($contract, $invoice, $line_item);

            if ( !isset($invoice_total_by_invloc[$inventory_location_id]) ){
                $invoice_total_by_invloc[$inventory_location_id] = 0;
            }

            $invoice_total_by_invloc[$inventory_location_id] += $invoice->amount_before_tax;
        }

        // get debit note total
        $debit_note_total = 0;
        foreach ($debit_notes as $debit_note) {

            $debit_note_total += $debit_note->amount_before_tax;
            $inventory_location_id_top = null;

            foreach ($debit_note->lineItems as $line_item) {
                if (
                    $line_item->billable_type == PhysicalLoadAdjustment::class &&
                    !empty($line_item->billable->physicalLoad->inventory_location_id)
                ) {
                    //$return_data[$line_item->billable->physicalLoad->inventory_location_id]['total_latest_quantity'] +=
                    //    $line_item->billable->adjusted_quantity;
                    // Need to set to use line item quantity to not double count for transport cost related load adjustment CN/DN
                    $inventory_location_id = self::getInventoryLocationId($contract, $debit_note, $line_item);

                    $return_data[$inventory_location_id]['total_latest_quantity'] += $line_item->quantity;

                    if ( $inventory_location_id_top == null ){
                        $inventory_location_id_top = $inventory_location_id;
                    }
                }

                if (
                    $line_item->billable_type == get_class(new PhysicalLoad)
                    || $line_item->billable_type == get_class(new BillOfLading)
                ) {

                    $inventory_location_id = self::getInventoryLocationId($contract, $debit_note, $line_item);

                    if ( $inventory_location_id_top == null ){
                        $inventory_location_id_top = $inventory_location_id;
                    }

                    if (empty($return_data[$inventory_location_id])) {
                        $return_data[$inventory_location_id]['total_latest_quantity'] = 0;
                    }

                    if (!empty($line_item->billable_id) && !empty($line_item->quantity)) {
                        $return_data[$inventory_location_id]['total_latest_quantity'] += $line_item->quantity;
                    } else {
                        $quantity = 0;

                        if ($line_item->billable_type == get_class(new PhysicalLoad)) {
                            $quantity = !empty($line_item->billable->adjusted_quantity) ? $line_item->billable->adjusted_quantity : $line_item->billable->invoiceable_quantity;
                        } else if ($line_item->billable_type == get_class(new BillOfLading)) {
                            $discharge = $line_item->billable->discharge;

                            // If discharge weight is available, use discharge weight, else use BL weight
                            if ( $discharge != null ){
                                $quantity = $discharge->quantity;
                            }else{
                                $quantity = $line_item->billable->quantity;
                            }
                        }

                        $return_data[$inventory_location_id]['total_latest_quantity'] += $quantity;
                    }
                }
            }

            // invloc id
            if ( $inventory_location_id_top == null ){
                $inventory_location_id_top = $debit_note->inventory_location_id ?? null;
            }

            if ( !isset($debit_note_total_by_invloc[$inventory_location_id_top]) ){
                $debit_note_total_by_invloc[$inventory_location_id_top] = 0;
            }

            $debit_note_total_by_invloc[$inventory_location_id_top] += $debit_note->amount_before_tax;

        }

        // get credit note total
        $credit_note_total = 0;
        foreach ($credit_notes as $credit_note) {
            $credit_note_total -= $credit_note->amount_before_tax;
            $inventory_location_id_top = null;

            foreach ($credit_note->lineItems as $line_item) {

                // Physical load adjustment line item billable type
                if (
                    $line_item->billable_type == PhysicalLoadAdjustment::class &&
                    !empty($line_item->billable->physicalLoad->inventory_location_id)
                ) {
                    //$return_data[$line_item->billable->physicalLoad->inventory_location_id]['total_latest_quantity'] +=
                    //   $line_item->billable->adjusted_quantity;
                    // Need to set to use line item quantity to not double count for transport cost related load adjustment CN/DN
                    $inventory_location_id = self::getInventoryLocationId($contract, $credit_note, $line_item);

                    $return_data[$inventory_location_id]['total_latest_quantity'] += $line_item->quantity;

                    if ( $inventory_location_id_top == null ){
                        $inventory_location_id_top = $inventory_location_id;
                    }
                }

                // Physical load / BL line item billable type
                if ($line_item->billable_type == PhysicalLoad::class || $line_item->billable_type == BillOfLading::class) {

                    $inventory_location_id = self::getInventoryLocationId($contract, $credit_note, $line_item);

                    if ( $inventory_location_id_top == null ){
                        $inventory_location_id_top = $inventory_location_id;
                    }

                    if (empty($return_data[$inventory_location_id])) {
                        $return_data[$inventory_location_id]['total_latest_quantity'] = 0;
                    }

                    if (!empty($line_item->billable_id) && !empty($line_item->quantity)) {
                        $return_data[$inventory_location_id]['total_latest_quantity'] += $line_item->quantity;
                    } else {

                        $quantity = 0;

                        if ($line_item->billable_type == PhysicalLoad::class) {
                            $quantity = !empty($line_item->billable->adjusted_quantity) ? $line_item->billable->adjusted_quantity : $line_item->billable->invoiceable_quantity;
                        } else if ($line_item->billable_type == BillOfLading::class) {

                            $discharge = $line_item->billable->discharge;

                            // If discharge weight is available, use discharge weight, else use BL weight
                            if ( $discharge != null ){
                                $quantity = $discharge->quantity;
                            }else{
                                $quantity = $line_item->billable->quantity;
                            }

                        }

                        $return_data[$inventory_location_id]['total_latest_quantity'] += $quantity;
                    }
                }
            }

            // invloc id
            if ( $inventory_location_id_top == null ){
                $inventory_location_id_top = $credit_note->inventory_location_id ?? null;
            }

            if ( !isset($credit_note_total_by_invloc[$inventory_location_id_top]) ){
                $credit_note_total_by_invloc[$inventory_location_id_top] = 0;
            }

            $credit_note_total_by_invloc[$inventory_location_id_top] -= $credit_note->amount_before_tax;
        }

        // get sst_amount
        $sst_pmt = 0;

        if (get_class($contract) === PhysicalContract::class && $contract->canSSTSettlement() ) {
            $sst = StateSalesTaxHelper::getSSTSettlementDetails($contract, $contract->tax);
            $sst_pmt = $sst['sst_value'];
        }

        // dont deduct sst pmt here first
        $current_fixed_price_no_sst = round($current_fixed_price + $sst_pmt, 2);

        foreach ($return_data as $i => $dataset) {

            $quantity_in_price_uom = UomHelper::convert($dataset['total_latest_quantity'], $contract->quantity_uom, $contract->getPriceUom() ,$contract->product_id);

            $sst_amount = $quantity_in_price_uom * $sst_pmt;
            $latest_quantity_price = $quantity_in_price_uom * $current_fixed_price_no_sst;
            $latest_quantity_price_with_sst = round($latest_quantity_price - $sst_amount, 2);
            $actual_billed_total_by_invloc = ($invoice_total_by_invloc[$i] ?? 0) + ($debit_note_total_by_invloc[$i] ?? 0) + ($credit_note_total_by_invloc[$i] ?? 0);

            $return_data[$i] = array_merge($return_data[$i], [
                'provisional_price' => $contract->base_price + $contract->premium - $contract->discount,
                'fixed_price' => $current_fixed_price_no_sst,
                'fixed_price_with_sst' => $current_fixed_price,
                'billed_total' => $invoice_offset - $debit_note_total + $credit_note_total,
                'settlement_total' => $latest_quantity_price,
                'settlement_total_with_sst' => $latest_quantity_price_with_sst,
                'net_total' => $latest_quantity_price - $actual_billed_total_by_invloc - $sst_amount,
                'sst_amount' => $sst_amount,
                'actual_billed_total' => $actual_billed_total_by_invloc,
                'actual_billed_total_all_invloc' => $invoice_offset + $debit_note_total + $credit_note_total       // actual amount that has been billed
            ]);

        }

        // Still perform SST Settlement & Price Fixing although no loads
        if (empty($return_data)) {
            $return_data[null] = [
                'total_latest_quantity' => 0,
                'provisional_price' => $contract->base_price + $contract->premium - $contract->discount,
                'fixed_price' => $current_fixed_price_no_sst,
                'fixed_price_with_sst' => $current_fixed_price,
                'billed_total' => $invoice_offset - $debit_note_total + $credit_note_total,
                'actual_billed_total' => $invoice_offset + $debit_note_total + $credit_note_total,
                'actual_billed_total_all_invloc' => $invoice_offset + $debit_note_total + $credit_note_total
            ];
        }

        // To cater for situations where inventory location was not selected due to no allocation (trading scenario)
        if ( count($return_data) == 1 ){
            foreach ( array_keys($return_data) as $key ){
                $return_data[$key]['actual_billed_total'] = $return_data[$key]['actual_billed_total_all_invloc'];
            }
        }

        return $return_data;
    }

    /**
     * Determine whether we need to perform price settlement. Uses price adjustment data getLandPriceAdjusmentViewData()
     * @param $price_adjustment_data
     * @return bool
     */
    public static function needPriceSettlement($price_adjustment_data, $filter_inventory_location_id = null) {

        $needs_settlement = false;

        foreach ( $price_adjustment_data as $inventory_location_id => $data ) {

            if ( $filter_inventory_location_id != null && $filter_inventory_location_id != $inventory_location_id){
                continue;
            }

            // If no settlement is required, throw error
            // if it's SST based settlement without any billed loads - allow settlement
            // Tolerance of 20 cents difference. More than 20 cents only consider need settlement
            if ( isset($data['settlement_total_with_sst']) &&
                bccomp(round($data['actual_billed_total'], 2), round($data['settlement_total_with_sst'], 2), 2) !== 0 &&
                abs(round($data['actual_billed_total'], 2) - round($data['settlement_total_with_sst'], 2)) >= 0.2
            ) {
                $needs_settlement = true;
                break;
            }

        }

        return $needs_settlement;

    }

    public static function needPriceSettlementEinvoicing($price_adjustment_data, $filter_inventory_location_id = null) {

        $needs_settlement = false;

        foreach ( $price_adjustment_data as $inventory_location_id => $data ) {

            if ( $filter_inventory_location_id !== null && $filter_inventory_location_id != $inventory_location_id){
                continue;
            }

            $pricing = $data['price'];

            // If no settlement is required, throw error
            // if it's SST based settlement without any billed loads - allow settlement
            // Tolerance of 20 cents difference. More than 20 cents only consider need settlement
            if (
                bccomp(round($pricing['actual_billed_total'], 2), round($pricing['settlement_total_with_sst'], 2), 2) !== 0 &&
                abs(round($pricing['actual_billed_total'], 2) - round($pricing['settlement_total_with_sst'], 2)) >= 0.2
            ) {
                $needs_settlement = true;
                break;
            }

        }

        return $needs_settlement;

    }

}
