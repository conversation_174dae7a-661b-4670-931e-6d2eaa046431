<?php

namespace App\Helpers;

use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Sentry\State\Scope;
use function Sentry\configureScope;

class ErrorHelper
{
    public static function sentry(\Throwable $exception, $extras = []){

        $user =  Auth::user();

        if (app()->bound('sentry')) {

            configureScope(function(Scope $scope) use ($user, $extras){

                if ( $user !== null ){
                    $scope->setExtra('user_id', $user->id);
                    $scope->setExtra('user_email', $user->email);
                }

                if ( count($extras) > 0 ){
                    $scope->setExtras($extras);
                }

            });

            app('sentry')->captureException($exception);
        }

        return true;

    }
}
