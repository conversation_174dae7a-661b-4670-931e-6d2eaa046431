<?php

namespace App\Helpers;

use App\Interfaces\Contractable;
use App\Models\Product;
use App\Models\RubberContract;
use App\Models\Uom;
use App\Repositories\UomConversionRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class UomHelper
{

    /**
     * Convert from one uom to another uom
     * @param $from_quantity
     * @param $from_uom_code
     * @param $to_uom_code
     * @return float|int
     */
    public static function convert($from_quantity, $from_uom_code, $to_uom_code, $product_id) {

        if ( $from_uom_code == null || $to_uom_code == null ){
            Log::warning('FROM uom code and TO uom code is null. No conversion is done');
            return $from_quantity;
        }

        if ( $from_uom_code == $to_uom_code ){
            return $from_quantity;
        }

        $uomConversionRepo = new UomConversionRepository();
        return $uomConversionRepo->doUomConversionRaw($from_uom_code, $to_uom_code, $product_id, $from_quantity);

    }

    public static function useProvidedUomOrDefault(Contractable $contract, $uom_code = null) {

        // Get default UOM in case load no UOM
        if ( get_class($contract) == RubberContract::class ){
            $default_uom = Uom::KILOGRAMS;
        }else{
            $default_uom = Uom::METRIC_TONNES;
        }

        return $uom_code ?? $default_uom;
    }

    public static function getUomConversionRate($from_uom_code, $to_uom_code, $product_id) {

        $uomConversionRepo = new UomConversionRepository();

        $from_uom = Uom::where('code', $from_uom_code)->first();
        $to_uom = Uom::where('code', $to_uom_code)->first();
        $product = Product::where('id', $product_id)->first();

        if ( $from_uom == null || $to_uom == null || $product == null ){
            throw new \Exception('UOM or product information is missing.');
        }

        return $uomConversionRepo->getUomConversionRate($from_uom, $to_uom, $product);

    }
}
