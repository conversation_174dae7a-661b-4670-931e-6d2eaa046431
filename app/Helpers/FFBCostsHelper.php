<?php

namespace App\Helpers;

use App\Http\Controllers\Contract\LongTermAgreementController;
use App\Models\ContractFFBCosts;
use App\Models\FFBCost;
use App\Models\MonthlyContract;
use App\Models\BillingDocumentFFBMetadata;
use App\Models\PriceIndex;
use App\Repositories\PriceIndexEntryRepository;
use App\Services\PriceIndexEntryService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class FFBCostsHelper
{

    public static function calculateValue($ffb_cost, $target_value = null)
    {

        $value = 0;

        // validate
        if ( $target_value === null && $ffb_cost->range_type != FFBCost::RANGE_TYPE_STANDARD ){
            throw new \Exception($ffb_cost->cost_name . ' has an unsupported configuration. Special logic is required for non-standard type FFB Cost.');
        }

        if ($ffb_cost->range_type == FFBCost::RANGE_TYPE_STANDARD) {

            $condition = $ffb_cost->conditionals->first();
            $value = $condition->value;

        } else if ($ffb_cost->range_type == FFBCost::RANGE_TYPE_ABSOLUTE) {

            foreach ($ffb_cost->conditionals as $condition) {
                if ($target_value >= $condition->min && $target_value <= $condition->max) {
                    $value = $condition->value;
                    break;
                }
            }

        } else if ($ffb_cost->range_type == FFBCost::RANGE_TYPE_STAGERRED) {

            $balance_to_calculate = $target_value;

            foreach ($ffb_cost->conditionals as $condition) {

                if ($target_value > $condition->max && $condition->max !== 0 ) {
                    $value += $condition->value * ($condition->max - $condition->min);
                    $balance_to_calculate -= abs($condition->max - $condition->min);

                } else {
                    $value += $balance_to_calculate * $condition->value;

                    if ( $condition->max == 0 ){
                        $balance_to_calculate = 0;
                    }else{
                        $balance_to_calculate -= abs($condition->max - $condition->min);
                    }
                }

                if ( $balance_to_calculate <= 0 ){
                    break;
                }

            }

            if ( $target_value != 0 ){
                $value = $value / $target_value;
            }

        }

        // If percentage, need to divide
        if ( $ffb_cost->value_type == ContractFFBCosts::VALUE_TYPE_PERCENTAGE ){
            $value = $value / 100;
        }

        // Decide value should add or minus
        $value = $ffb_cost->calculation_operation == ContractFFBCosts::OPERATION_ADDITION ? $value : (0 - $value);

        return $value;
    }

    public static function calculateOer(MonthlyContract $contract, $awarded_oer = 0)
    {

        // If contract has base OER and KER, overwrite mill's OER and KER
        // Also puts in MIN/MAX logic
        if ($contract->oer_base !== 0 && $contract->oer_max === 0 && $contract->oer_min === 0) {
            $awarded_oer = $contract->oer_base;
        } else {
            if ($contract->oer_max !== 0 && $contract->oer_min === 0 && $awarded_oer > $contract->oer_max) {
                $awarded_oer = $contract->oer_max;
            } else if ($contract->oer_max == 0 && $contract->oer_min !== 0 && $awarded_oer < $contract->oer_min) {
                $awarded_oer = $contract->oer_min;
            } else if ($contract->oer_max !== 0 && $contract->oer_min !== 0) {

                if ($awarded_oer > $contract->oer_max) {
                    $awarded_oer = $contract->oer_max;
                } else if ($awarded_oer < $contract->oer_min) {
                    $awarded_oer = $contract->oer_min;
                }
            }
        }

        return $awarded_oer;

    }

    public static function calculateKer(MonthlyContract $contract, $awarded_ker = 0)
    {

        // If contract has base OER and KER, overwrite mill's OER and KER
        // Also puts in MIN/MAX logic
        if ($contract->ker_base !== 0 && $contract->ker_max === 0 && $contract->ker_min === 0) {
            $awarded_ker = $contract->ker_base;
        } else {
            if ($contract->ker_max !== 0 && $contract->ker_min === 0 && $awarded_ker > $contract->ker_max) {
                $awarded_ker = $contract->ker_max;
            } else if ($contract->ker_max == 0 && $contract->ker_min !== 0 && $awarded_ker < $contract->ker_min) {
                $awarded_ker = $contract->ker_min;
            } else if ($contract->ker_max !== 0 && $contract->ker_min !== 0) {

                if ($awarded_ker > $contract->ker_max) {
                    $awarded_ker = $contract->ker_max;
                } else if ($awarded_ker < $contract->ker_min) {
                    $awarded_ker = $contract->ker_min;
                }
            }
        }

        return $awarded_ker;
    }

    public static function generateFfbPriceDetails($contract, $loads, $data, $payment_percent, $is_final) {

        $user = Auth::user();

        $data = [
            'total_quantity' => 0,
            'payment_percent' => $payment_percent,
            'cpo_price' => $data['mpob_cpo_price'],
            'oer' => $data['awarded_oer'],
            'pk_price' => $data['mpob_pk_price'],
            'ker' => $data['awarded_ker'],
            'actual_oer' => $data['actual_oer'],
            'actual_ker' => $data['actual_ker'],
            'loose_fruit_quantity' => $data['loose_fruit_quantity'],
            'loose_fruit_percent' => 0,
            'cpo_subtotal' => 0,
            'pk_subtotal' => 0,
            'additionals_unit_value' => 0,
            'additionals_subtotal' => 0,
            'subtotal' => 0,
            'total' => 0, // after percentage
            'total_with_additionals' => 0, // after percentage
            'additionals' => [],
        ];

        // NOTE: user requested to use whatever OER/KER they enter as final OER/KER. so don't need to recalculate anymore.
        /*
        if ( $is_final ){
            $data['oer'] = FFBCostsHelper::calculateOer($contract, $data['oer']);
            $data['ker'] = FFBCostsHelper::calculateKer($contract, $data['ker']);
        }*/

        // get total weight
        foreach ($loads as $load) {
            $data['total_quantity'] += $load->getActualBillableLoad();
        }

        if ($data['total_quantity'] <= 0) {
            throw new \Exception('Unable to bill for quantity of 0 MT or less.');
        }

        // Update LF percentage
        $data['loose_fruit_percent'] = $data['loose_fruit_quantity'] / $data['total_quantity'] * 100;

        // Factor in any costs, incentives, etc. for final voucher
        if ($is_final) {

            $ffb_costs = $contract->ffbCosts()->with(['conditionals', 'ffbCostMaster'])->get();

            // LF incentives have to be first coz it will recalculate OER (Update: no longer relevant since user will enter the final OER already)
            $ffb_costs = ArrayHelper::moveFfbCostToFront($ffb_costs, ContractFFBCosts::COST_TYPE_OER_INCENTIVE);
            $ffb_costs = ArrayHelper::moveFfbCostToFront($ffb_costs, ContractFFBCosts::COST_TYPE_LOOSE_FRUIT_INCENTIVE);

            foreach ($ffb_costs as $ffb_cost) {

                // Ignore these ffb costs
                if ( in_array($ffb_cost->cost_type, [ContractFFBCosts::COST_TYPE_MILL_MARGIN]) ){
                    continue;
                }

                // Calculate value from absolute, standard or staggered first
                if ($ffb_cost->cost_type == ContractFFBCosts::COST_TYPE_OER_INCENTIVE) {

                    // Calculate OER difference between base and actual (in this case, if contract is set to oer_base, 'oer' will always be 'oer_base' value)
                    $target_value = $data['actual_oer'] - $data['oer'];
                    $value = FFBCostsHelper::calculateValue($ffb_cost, $target_value);

                }
                else if ($ffb_cost->cost_type == ContractFFBCosts::COST_TYPE_LOOSE_FRUIT_INCENTIVE) {
                    $target_value = $data['loose_fruit_percent'];
                    $value = FFBCostsHelper::calculateValue($ffb_cost, $target_value);
                }
                else if ($ffb_cost->cost_type == ContractFFBCosts::COST_TYPE_MILL_PROCESSING) {
                    $target_value = $data['total_quantity'];
                    $value = FFBCostsHelper::calculateValue($ffb_cost, $target_value);
                }
                else {
                    $value = FFBCostsHelper::calculateValue($ffb_cost);
                }

                // If percentage, we need to retain the original percentage for calculation
                if ($ffb_cost->value_type == ContractFFBCosts::VALUE_TYPE_PERCENTAGE) {
                    $display_value = round($value * 100, 2);
                    $display_unit = '%';
                } else {
                    $display_value = round($value, 2);

                    if ($ffb_cost->value_type == ContractFFBCosts::VALUE_TYPE_PMT) {
                        $display_unit = 'PMT';
                    } else {
                        $display_unit = 'Fixed';
                    }

                }

                // Generate objects for the additional costs/incentives/levies
                switch ($ffb_cost->cost_type) {

                    case ContractFFBCosts::COST_TYPE_MPOB_CESS:
                    case ContractFFBCosts::COST_TYPE_CPO_SABAH_DISCOUNT:
                    case ContractFFBCosts::COST_TYPE_CPO_SARAWAK_DISCOUNT:
                    case ContractFFBCosts::COST_TYPE_CPO_TRANSPORT:

                        $total = $value * $data['total_quantity'] * $data['oer'] / 100;

                        $data['additionals']['cpo'][] = [
                            'name' => $ffb_cost->cost_name,
                            'description' => $ffb_cost->description,
                            'operation' => $ffb_cost->calculation_operation,
                            'value' => round($display_value, 2),
                            'total' => round($total, 2),
                            'unit' => $display_unit,
                            'included' => false,
                        ];

                        $data['additionals_unit_value'] += $value;
                        $data['additionals_subtotal'] += $total;

                        break;

                    case ContractFFBCosts::COST_TYPE_CPO_SABAH_TAX:

                        // if not available in contract level, try to resolve master data level
                        if ( $ffb_cost->price_index_id == null && $ffb_cost->ffbCostMaster ){
                            $ffb_cost->price_index_id = $ffb_cost->ffbCostMaster->price_index_id;
                        }

                        if ( $ffb_cost->price_index_id == null ){
                            throw new \Exception('Price Index not set up for FFB cost ' . ContractFFBCosts::COST_TYPE_CPO_SABAH_TAX);
                        }

                        $from_date = Carbon::parse($contract->shipment_month, $user->timezone)->startOfMonth();
                        $to_date = Carbon::parse($contract->shipment_month, $user->timezone)->endOfMonth();

                        $priceIndexEntryService = app()->make(PriceIndexEntryService::class);
                        $price_index_price = $priceIndexEntryService->getPriceIndexValue(
                            $ffb_cost->priceIndex,
                            $from_date,
                            $to_date
                        )->settle;

                        $total = round($value * $price_index_price * $data['total_quantity'] * $data['oer'] / 100, 2);

                        $data['additionals']['cpo'][] = [
                            'name' => $ffb_cost->cost_name,
                            'description' => $ffb_cost->description,
                            'operation' => $ffb_cost->calculation_operation,
                            'value' => round($total /  $data['total_quantity'] / ($data['oer'] / 100), 2),
                            'total' => $total,
                            'unit' => $display_unit,
                            'included' => false,
                        ];

                        $data['additionals_unit_value'] += $value;
                        $data['additionals_subtotal'] += $total;

                        break;

                    case ContractFFBCosts::COST_TYPE_CPO_SARAWAK_TAX:

                        // if not available in contract level, try to resolve master data level
                        if ( $ffb_cost->price_index_id == null && $ffb_cost->ffbCostMaster ){
                            $ffb_cost->price_index_id = $ffb_cost->ffbCostMaster->price_index_id;
                        }

                        if ( $ffb_cost->price_index_id == null ){
                            throw new \Exception('Price Index not set up for FFB cost ' . ContractFFBCosts::COST_TYPE_CPO_SARAWAK_TAX);
                        }

                        $from_date = Carbon::parse($contract->shipment_month, $user->timezone)->startOfMonth();
                        $to_date = Carbon::parse($contract->shipment_month, $user->timezone)->endOfMonth();

                        $priceIndexEntryService = app()->make(PriceIndexEntryService::class);
                        $price_index_price = $priceIndexEntryService->getPriceIndexValue(
                            $ffb_cost->priceIndex,
                            $from_date,
                            $to_date
                        )->settle;

                        // For CPO sarawak tax, we ignore the value from master data since it's a customized calculation
                        $total = 0 - round((($price_index_price - 40) / 1.05 * 0.05) * $data['total_quantity'] * $data['oer'] / 100, 2);

                        $data['additionals']['cpo'][] = [
                            'name' => $ffb_cost->cost_name,
                            'description' => $ffb_cost->description,
                            'operation' => $ffb_cost->calculation_operation,
                            'value' => round($total /  $data['total_quantity'] / ($data['oer'] / 100), 2),
                            'total' => $total,
                            'unit' => $display_unit,
                            'included' => false,
                        ];

                        $data['additionals_unit_value'] += $value;
                        $data['additionals_subtotal'] += $total;

                        break;

                    case ContractFFBCosts::COST_TYPE_MILL_PROCESSING:
                    case ContractFFBCosts::COST_TYPE_FFB_TRANSPORT:
                    case ContractFFBCosts::COST_TYPE_TRANSPORT_INCENTIVE:
                    case ContractFFBCosts::COST_TYPE_SPECIAL_VOLUME_INCENTIVE:
                    case ContractFFBCosts::COST_TYPE_VOLUME_INCENTIVE:
                    case ContractFFBCosts::COST_TYPE_OTHER_INCENTIVES:

                        $total = $value * $data['total_quantity'];

                        $data['additionals']['others'][] = [
                            'name' => $ffb_cost->cost_name,
                            'description' => $ffb_cost->description,
                            'operation' => $ffb_cost->calculation_operation,
                            'value' => $display_value,
                            'total' => $total,
                            'unit' => $display_unit,
                            'included' => false,
                        ];

                        $data['additionals_unit_value'] += $value;
                        $data['additionals_subtotal'] += $total;

                        break;

                    case ContractFFBCosts::COST_TYPE_PK_TRANSPORT:
                    case ContractFFBCosts::COST_TYPE_PK_SABAH_DISCOUNT:
                    case ContractFFBCosts::COST_TYPE_PK_SARAWAK_DISCOUNT:
                    case ContractFFBCosts::COST_TYPE_PK_SABAH_TAX:
                    case ContractFFBCosts::COST_TYPE_PK_SARAWAK_TAX:

                        $total = $value * $data['total_quantity'] * $data['ker'] / 100;

                        $data['additionals']['pk'][] = [
                            'name' => $ffb_cost->cost_name,
                            'description' => $ffb_cost->description,
                            'operation' => $ffb_cost->calculation_operation,
                            'value' => round($display_value, 2),
                            'total' => round($total, 2),
                            'unit' => $display_unit,
                            'included' => false,
                        ];

                        $data['additionals_unit_value'] += $value;
                        $data['additionals_subtotal'] += $total;

                        break;

                    case ContractFFBCosts::COST_TYPE_OER_INCENTIVE:

                        // Update: Do not calculate OER incentives since user will enter final OER in form.

                        /*$data['additionals']['oer'][] = [
                            'name' => $ffb_cost->cost_name,
                            'description' => $ffb_cost->description,
                            'operation' => $ffb_cost->calculation_operation,
                            'value' => round($display_value, 2),
                            'unit' => $display_unit,
                            'total' => round($display_value, 2),
                            'included' => true,
                        ];*/

                        // only for OER incentives we use original percentage instead of divided by 100.
                        // because we are summing percentage with another percentage


                        break;

                    case ContractFFBCosts::COST_TYPE_LOOSE_FRUIT_INCENTIVE:

                        $total = $data['loose_fruit_quantity'] * $value;

                        if ( $total > 0 ){

                            $data['additionals']['others'][] = [
                                'name' => $ffb_cost->cost_name . ' - ' . $data['loose_fruit_quantity'] . ' MT',
                                'description' => $ffb_cost->description . ' - ' . $data['loose_fruit_quantity'] . ' MT',
                                'operation' => $ffb_cost->calculation_operation,
                                'value' => round($display_value, 2),
                                'total' => 0,
                                'unit' => $display_unit,
                                'included' => true,
                            ];

                            // Update awarded OER value to take into account LF 24% OER
                            //$data['oer'] += round(((($data['total_quantity'] - $data['loose_fruit_quantity']) * $data['actual_oer'] / 100) + $total) / $data['total_quantity'], 2);

                        }

                        break;

                    default:
                        break;

                }

            }

        }

        $data['cpo_subtotal'] = round($data['total_quantity'] * $data['cpo_price'] * $data['oer'] / 100, 2);
        $data['pk_subtotal'] = round($data['total_quantity'] * $data['pk_price'] * $data['ker'] / 100, 2);
        $data['subtotal'] = $data['cpo_subtotal'] + $data['pk_subtotal'];
        $data['total'] = round($data['subtotal'] * $data['payment_percent'] / 100, 2);
        $data['total_with_additionals'] = $data['total'] + $data['additionals_subtotal'];
        $data['ffb_price'] = round(($data['subtotal'] + $data['additionals_subtotal']) / $data['total_quantity'], 2);

        return $data;
    }

    public static function appendVoucherSpecificData($data, $ffb_metadata, $tax)
    {
        $data['amount_before_tax'] = $ffb_metadata['total'];

        // factor in additionals
        if (isset($ffb_metadata['additionals'])) {

            foreach ($ffb_metadata['additionals'] as $group => $list) {

                foreach ($list as $additional) {

                    if ($additional['included'] == true) {
                        continue;
                    }

                    $data['amount_before_tax'] += $additional['total'];

                }
            }
        }

        $data['tax_percentage'] = $tax->percentage;
        $data['tax_amount'] = $tax->percentage / 100 * $data['amount_before_tax'];
        $data['amount_after_tax'] = $data['amount_before_tax'] + $data['tax_amount'];
        $data['amount_after_tax_and_knockoff'] = $data['amount_after_tax'];
        $data['unit_price'] = $ffb_metadata['ffb_price'];

        return $data;
    }

    public static function generateVoucherLineItems($contract, $invoice, $loads, $ffb_metadata)
    {

        $dataset = [];

        $data = [];
        $data['billing_document_id'] = $invoice->id;
        $data['product_id'] = $contract->product_id;
        $data['product_code'] = $contract->product_code;
        $data['quantity_uom'] = $contract->quantity_uom;
        $data['currency'] = $invoice->currency;
        $data['forex_rate'] = $invoice->forex_rate;
        $data['price_uom'] = $invoice->price_uom;
        $data['unit_price'] = $contract->getContractPrice();
        $data['created_by'] = Auth::user()->id;

        foreach ($loads as $load) {

            $data['billable_type'] = get_class($load);
            $data['billable_id'] = $load->id;
            $data['billable_reference_no'] = $load->reference_no;
            $data['vehicle_no'] = $load->vehicle_no;
            $data['inventory_location_id'] = !empty($load->mill) ? $load->mill->id : null;
            $data['inventory_location_name'] = !empty($load->mill) ? $load->mill->name : null;

            $data['quantity'] = $load->getActualBillableLoad(); // TODO: Update this to take account of adjustments
            $data['tax_percentage'] = $invoice->tax_percentage;
            $data['amount_before_tax'] = self::getFfbPriceBeforeTax($load, $ffb_metadata);
            $data['tax_amount'] = $data['tax_percentage'] / 100 * $data['amount_before_tax'];
            $data['amount_after_tax'] = $data['amount_before_tax'] + $data['tax_amount'];

            $data['description'] = 'Delivery of ' . $data['quantity'] . ' ' . $data['quantity_uom'] . ' ' . $contract->product_code;

            $dataset[] = $data;
        }

        if (isset($ffb_metadata['additionals'])) {

            $data['billable_type'] = null;
            $data['billable_id'] = null;
            $data['billable_reference_no'] = null;

            foreach ($ffb_metadata['additionals'] as $group => $list) {

                foreach ($list as $additional) {

                    if ($additional['included'] == true) {
                        continue;
                    }

                    $data['quantity'] = 1;
                    $data['unit_price'] = $additional['value'];
                    $data['tax_percentage'] = $invoice->tax_percentage;
                    $data['amount_before_tax'] = $additional['total'];
                    $data['tax_amount'] = $data['tax_percentage'] / 100 * $additional['total'];
                    $data['amount_after_tax'] = $data['amount_before_tax'] + $data['tax_amount'];

                    $data['description'] = $additional['name'];

                    $dataset[] = $data;

                }

            }

        }

        return $dataset;

    }

    public static function createFFBMetadata($billing_document, $ffb_metadata){

        $invoice_ffb = BillingDocumentFFBMetadata::create([
            'billing_document_id' => $billing_document->id,
            'quantity' => $ffb_metadata['total_quantity'],
            'cpo_price' => $ffb_metadata['cpo_price'],
            'pk_price' => $ffb_metadata['pk_price'],
            'oer_percent' => $ffb_metadata['oer'],
            'ker_percent' => $ffb_metadata['ker'],
            'actual_oer_percent' => $ffb_metadata['actual_oer'],
            'actual_ker_percent' => $ffb_metadata['actual_ker'],
            'cpo_subtotal' => $ffb_metadata['cpo_subtotal'],
            'pk_subtotal' => $ffb_metadata['pk_subtotal'],
            'subtotal' => $ffb_metadata['subtotal'],
            'payment_percent' => $ffb_metadata['payment_percent'],
            'total' => $ffb_metadata['total'],
            'total_with_additionals' => $ffb_metadata['total_with_additionals'],
            'additionals_subtotal' => $ffb_metadata['additionals_subtotal'],
            'loose_fruit_quantity' => $ffb_metadata['loose_fruit_quantity'] ?? 0,
            'loose_fruit_percent' => $ffb_metadata['loose_fruit_percent'] ?? null,
            'billing_period_start' => $ffb_metadata['billing_period_start'],
            'billing_period_end' => $ffb_metadata['billing_period_end'],
            'additionals' => $ffb_metadata['additionals'] ?? null
        ]);

        return $invoice_ffb;
    }

    public static function getFfbPriceBeforeTax($load, $ffb_metadata)
    {
        $billable_load = $load->getActualBillableLoad();

        $cpo = $billable_load * $ffb_metadata['cpo_price'] * $ffb_metadata['oer'] / 100;
        $pk = $billable_load * $ffb_metadata['pk_price'] * $ffb_metadata['ker'] / 100;

        return $cpo + $pk;
    }
}
