<?php

namespace App\Helpers;

use App\Interfaces\BillingNumberInterface;
use App\Models\BillingDocument;
use App\Models\PhysicalContract;
use App\Services\ContractNumberService;
use Carbon\Carbon;

class MinamasUpstreamBillingNumberHelper implements BillingNumberInterface
{
    private $contractNumberService;

    const TYPE_TO_FUNCTION = [
        BillingDocument::TYPE_CREDIT_NOTE => 'getCreditNoteFormat',
        BillingDocument::TYPE_ADVANCE_CREDIT_NOTE => 'getCreditNoteFormat',
        BillingDocument::TYPE_DEBIT_NOTE => 'getDebitNoteFormat',
        BillingDocument::TYPE_ADVANCE_INVOICE => 'getInvoiceFormat',
        BillingDocument::TYPE_NORMAL_INVOICE => 'getInvoiceFormat',
        BillingDocument::TYPE_PROFORMA_INVOICE => 'getInvoiceFormat',
        BillingDocument::TYPE_PAYMENT_VOUCHER => 'getPaymentVoucherFormat',
        BillingDocument::TYPE_ADVANCE_PAYMENT_VOUCHER => 'getPaymentVoucherFormat'
    ];

    const RUNNING_NUMBER_DIGITS = 5;

    public function __construct(ContractNumberService $contractNumberService)
    {
        $this->contractNumberService = $contractNumberService;
    }

    public function generateBillingNumber(PhysicalContract $contract, string $billing_document_type, Carbon $documentDate, $ar_ap_overwrite = null): string
    {
        $legal_entity = $contract->legalEntity;

        // Proforma invoice and Advance invoice use invoice running number
        if ( $billing_document_type == BillingDocument::TYPE_PROFORMA_INVOICE || $billing_document_type == BillingDocument::TYPE_ADVANCE_INVOICE ){
            $billing_document_type = BillingDocument::TYPE_NORMAL_INVOICE;
        }
        if ($billing_document_type == BillingDocument::TYPE_ADVANCE_CREDIT_NOTE) {
            $billing_document_type = BillingDocument::TYPE_CREDIT_NOTE;
        }
        if ($billing_document_type == BillingDocument::TYPE_ADVANCE_PAYMENT_VOUCHER) {
            $billing_document_type = BillingDocument::TYPE_PAYMENT_VOUCHER;
        }

        // Note: Minamas upstream is using legal entity + is_international as parameter for running number!!
        $runningNumber = $this
            ->contractNumberService
            ->allocateContractNumber(false, $billing_document_type, null, null,
                null, null, $legal_entity->id, self::RUNNING_NUMBER_DIGITS, null, $contract->is_international);

        return $this->{$this->typeToFunctionMap($billing_document_type)}($contract, $runningNumber, $documentDate);
    }

    public function typeToFunctionMap(string $type): string
    {
        if (empty(self::TYPE_TO_FUNCTION[$type])) {
            throw new \Exception('Minamas doesn\'t support billing this document type yet');
        }

        return self::TYPE_TO_FUNCTION[$type];
    }

    public function getCreditNoteFormat(PhysicalContract $contract, string $runningNumber, Carbon $documentDate): string
    {
        $isInternational = $contract->is_international ? 'E/' : '';
        $documentDateRomanMonth = NumberHelper::numberToRomanRepresentation($documentDate->month);
        $documentDateYear = $documentDate->year;

        return "{$isInternational}CN/{$runningNumber}/{$contract->legalEntity->code}/{$documentDateRomanMonth}/{$documentDateYear}";
    }

    public function getDebitNoteFormat(PhysicalContract $contract, string $runningNumber, Carbon $documentDate): string
    {
        $isInternational = $contract->is_international ? 'E/' : '';
        $documentDateRomanMonth = NumberHelper::numberToRomanRepresentation($documentDate->month);
        $documentDateYear = $documentDate->year;

        return "{$isInternational}DN/{$runningNumber}/{$contract->legalEntity->code}/{$documentDateRomanMonth}/{$documentDateYear}";
    }

    public function getInvoiceFormat(PhysicalContract $contract, string $runningNumber, Carbon $documentDate): string
    {
        $isInternational = $contract->is_international ? 'E/' : '';
        $documentDateRomanMonth = NumberHelper::numberToRomanRepresentation($documentDate->month);
        $documentDateYear = $documentDate->year;

        return "{$isInternational}{$runningNumber}/{$contract->legalEntity->code}/{$documentDateRomanMonth}/{$documentDateYear}";
    }

    public function getPaymentVoucherFormat(PhysicalContract $contract, string $runningNumber, Carbon $documentDate): string
    {
        $isInternational = $contract->is_international ? 'E/' : '';
        $documentDateRomanMonth = NumberHelper::numberToRomanRepresentation($documentDate->month);
        $documentDateYear = $documentDate->year;
        //$productCode = strtoupper($contract->product->contractNumberReference(true));
        $legalEntityCode = strtoupper($contract->legalEntity->code);
        $transactionType = strtoupper($contract->transaction_type);

        return "{$isInternational}{$runningNumber}/PV/{$legalEntityCode}-{$transactionType}/{$documentDateRomanMonth}/{$documentDateYear}";
    }

}
