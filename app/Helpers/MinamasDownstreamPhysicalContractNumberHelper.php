<?php

namespace App\Helpers;

use App\Interfaces\Contractable;
use App\Interfaces\PhysicalContractNumberInterface;
use App\Models\BillingDocument;
use App\Models\Contract;
use App\Models\ContractType;
use App\Models\Counterparty;
use App\Models\PhysicalContract;
use App\Models\PricingType;
use App\Services\ContractNumberService;
use Carbon\Carbon;

class MinamasDownstreamPhysicalContractNumberHelper implements PhysicalContractNumberInterface
{
    private $contractNumberService, $contract;

    public function __construct(ContractNumberService $contractNumberService)
    {
        $this->contractNumberService = $contractNumberService;
    }

    public function setContract(PhysicalContract $contract) : PhysicalContractNumberInterface {
        $this->contract = $contract;
        return $this;
    }

    public function allocateRunningNumber(): string
    {
        $legal_entity_id = $this->contract->legal_entity_id;

        $running_number = $this->contractNumberService->allocateContractNumber(
            false,
            ContractType::TYPE_PHYSICAL,
            null,
            null,
            null,
            null,
            $legal_entity_id,
            5,
            null,
            null,
            null
        );

        return $running_number;
    }

    public function generateContractNumber(): string
    {
        $runningNumber = $this->allocateRunningNumber();
        $contract_number = null;

        $contract_number = $this
            ->getDownstreamMinamasContractNumber($this->contract, $runningNumber);

        return $contract_number;
    }

    public function getDownstreamMinamasContractNumber(PhysicalContract $contract, string $runningNumber): string
    {
        $shipmentDate = Carbon::parse($contract->shipment_month);
        $shipmentMonth = str_pad($shipmentDate->month, 2, "0", STR_PAD_LEFT);
        $shipmentYear = $shipmentDate->format('y');
        $isTolling = $contract->is_tolling ? 'T' : '';
        $isProvisional = $contract->pricing_type !== PricingType::TYPE_OUTRIGHT ? 'P' : '';

        return strtoupper(
            "{$contract->transaction_type}/{$contract->legalEntity->code}/{$shipmentMonth}{$shipmentYear}/{$contract->product->contractNumberReference(true)}{$runningNumber}{$isTolling}{$isProvisional}"
        );
    }
}
