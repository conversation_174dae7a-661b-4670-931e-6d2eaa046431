<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

class DispatchAuditTrailJobParams
{
    public $event,
        $description,
        $contractable_id,
        $contractable_type,
        $previous_data,
        $auditable_id,
        $auditable_type,
        $user,
        $user_agent,
        $ip_address;

    public function __construct(
        string $event,
        string $description,
        string $contractable_id = null,
        string $contractable_type = null,
        array $previous_data = [],
        string $auditable_id = null,
        string $auditable_type = null,
        array $custom_input = []
    ) {
        $this->event = $event;
        $this->description = $description;
        $this->contractable_id = $contractable_id;
        $this->contractable_type = $contractable_type;
        $this->previous_data = $previous_data;
        $this->auditable_id = $auditable_id;
        $this->auditable_type = $auditable_type;

        $this->user = Auth::user();
        $this->ip_address = isset($custom_input['ip_address']) ? $custom_input['ip_address'] : AuditHelper::getClientIpAddress();
        $this->user_agent = isset($custom_input['user_agent']) ? $custom_input['user_agent'] : request()->header('User-Agent');
        $this->url = isset($custom_input['url']) ? $custom_input['url'] : (!empty(Route::current()) ? Route::current()->uri : null);
    }
}
