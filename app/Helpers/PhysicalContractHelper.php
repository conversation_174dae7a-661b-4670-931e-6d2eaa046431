<?php

namespace App\Helpers;

use App\Interfaces\Contractable;
use App\Models\ContractStatus;
use App\Models\InventoryLocation;
use App\Models\PhysicalContract;
use App\Models\Uom;
use App\Repositories\MasterDataRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;

class PhysicalContractHelper
{

    // must match name of fields
    const EDITABLE_FIELD_NAMES = [
        ContractStatus::TYPE_DRAFT => ['ALL'],
        ContractStatus::TYPE_POSTED => [
            'quantity', 'quantity_uom', 'pricing_type',
            'trader_user_id', 'incoterms_id', 'currency',
            'forex_rate', 'base_price', 'base_price_uom', 'premium', 'discount',
            'NON-CORE', 'counterparty_id', 'counterparty_id_search', 'remarks', 'quantity_in_mt',
            'contract_date', 'deliver_to'
        ],
        ContractStatus::TYPE_CONFIRMED => [
            'remarks', 'additional_costs', 'estimated_payment_date',
            'buyer_reference', 'seller_reference', 'broker_reference', 'external_sales_contract_reference'
        ],
        ContractStatus::TYPE_VOIDED => ['remarks'],
        ContractStatus::TYPE_FULFILLED => ['remarks', 'additional_costs', 'external_sales_contract_reference'],
        ContractStatus::TYPE_DEFAULTED => ['remarks'],
        ContractStatus::TYPE_BILLED => ['remarks']
    ];

    public static function getEditableFieldsForContractStatus($status)
    {

        if (array_key_exists($status, PhysicalContractHelper::EDITABLE_FIELD_NAMES)) {
            return PhysicalContractHelper::EDITABLE_FIELD_NAMES[$status];
        };

        throw new \Exception('Unknown Contract Status');
    }


    // Minamas upstream contract number got local/export depending on is_international flag, need to disable that after posted
    public static function getEditableFieldsForContractStatusMinamasUpstream($status)
    {

        if (array_key_exists($status, PhysicalContractHelper::EDITABLE_FIELD_NAMES)) {
            $field_names = PhysicalContractHelper::EDITABLE_FIELD_NAMES[$status];

            if ($status == ContractStatus::TYPE_POSTED) {
                $field_names = array_values(array_diff($field_names, ['counterparty_id', 'counterparty_id_search']));
            }
            return $field_names;
        };

        throw new \Exception('Unknown Contract Status');
    }

    public static function getExLocationText(PhysicalContract $contract)
    {
        return \App\Helpers\ConfigHelper::shouldContractUseCreditLimitCheckFlow($contract) ?
            'Inventory Location' : 'Ex Location';
    }

    public static function getInventoryLocationName($inventory_location_id)
    {
        $inventory_location = InventoryLocation::withTrashed()->where('id', $inventory_location_id)->first();

        return $inventory_location ? $inventory_location->name : '-';
    }
}
