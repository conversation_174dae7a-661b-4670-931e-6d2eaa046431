<?php

namespace App\Helpers;

class PhysicalContractNumberParams
{
    public $transaction_type,
        $legal_entity_code,
        $profit_center_code,
        $ship_month_year,
        $product_contract_number_reference,
        $running_number;

    public function __construct(
        string $transaction_type,
        string $legal_entity_code,
        string $profit_center_code,
        string $ship_month_year,
        string $product_contract_number_reference,
        string $running_number
    ) {
        $this->transaction_type =  $transaction_type;
        $this->legal_entity_code =  $legal_entity_code;
        $this->profit_center_code =  $profit_center_code;
        $this->ship_month_year =  $ship_month_year;
        $this->product_contract_number_reference =  $product_contract_number_reference;
        $this->running_number =  $running_number;
    }
}
