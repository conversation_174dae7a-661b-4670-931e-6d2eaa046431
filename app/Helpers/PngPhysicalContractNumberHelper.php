<?php

namespace App\Helpers;

use App\Interfaces\PhysicalContractNumberInterface;
use App\Models\ContractType;
use App\Models\PhysicalContract;
use App\Services\ContractNumberService;
use Carbon\Carbon;

class PngPhysicalContractNumberHelper implements PhysicalContractNumberInterface
{
    private $contractNumberService, $contract;

    public function __construct(ContractNumberService $contractNumberService)
    {
        $this->contractNumberService = $contractNumberService;
    }

    public function setContract(PhysicalContract $contract) : PhysicalContractNumberInterface {
        $this->contract = $contract;
        return $this;
    }

    public function allocateRunningNumber(): string
    {
        $profitCenter = $this->contract->profitCenter;
        $shipmentMonthString = $this->contract->shipment_month;

        $shipmentMonthDate = Carbon::parse($shipmentMonthString);

        $ship_month = $shipmentMonthDate->format('n');
        $ship_year = $shipmentMonthDate->format('Y');

        $running_number = $this->contractNumberService->allocateContractNumber(
            false,
            ContractType::TYPE_PHYSICAL,
            $profitCenter->id,
            null,
            $ship_month,
            $ship_year,
            null,
            5,
            null,
            null,
            null
        );

        return $running_number;
    }

    public function generateContractNumber(): string
    {
        $runningNumber = $this->allocateRunningNumber();
        $contract_number = null;

        $contract_number = $this
            ->getPngContractNumber($this->contract, $runningNumber);

        return $contract_number;
    }

    public function getPngContractNumber(PhysicalContract  $contract, string $runningNumber): string
    {
        $shipmentDate = Carbon::parse($contract->shipment_month);
        $shipmentMonth = str_pad($shipmentDate->month, 2, "0", STR_PAD_LEFT);
        $shipmentYear = $shipmentDate->format('y');

        $profit_center_code = $contract->is_internal ? 'C-' . $contract->profitCenter->code : $contract->profitCenter->code;

        return strtoupper(
            "{$contract->transaction_type}/{$profit_center_code}/{$shipmentMonth}{$shipmentYear}/{$contract->product->contractNumberReference(false)}{$runningNumber}"
        );
    }
}
