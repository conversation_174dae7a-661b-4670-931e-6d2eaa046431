<?php

namespace App\Factories;

use App\Interfaces\StringSettlementInterface;
use App\Services\Billing\StringSettlement\BypassSettlementService;
use App\Services\Billing\StringSettlement\CircleSettlementService;
use App\Services\Billing\StringSettlement\WashoutSettlementService;

class StringSettlementFactory
{

    public final static function init($string_type): StringSettlementInterface
    {
        if ($string_type->isWashoutString()) {
            return new WashoutSettlementService();
        }

        if ($string_type->isCircleString()) {
            return new CircleSettlementService();
        }

        if ($string_type->isBypassString()) {
            return new BypassSettlementService();
        }

        throw new \Exception('Type of Contract String not supported');
    }
}
