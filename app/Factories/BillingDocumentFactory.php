<?php

namespace App\Factories;

use App\Interfaces\BillingDocumentInterface;
use App\Models\BillingDocument;
use App\Services\Billing\AdvanceInvoiceService;
use App\Services\Billing\AdvancePaymentVoucherService;
use App\Services\Billing\InvoiceService;
use App\Services\Billing\AdvanceCreditNoteService;
use App\Services\Billing\ProformaInvoiceService;
use App\Services\Billing\CreditDebitNoteService;
use App\Services\Billing\AdvanceVoucherService;
use App\Services\Billing\FinalVoucherService;
use App\Services\Billing\PaymentVoucherService;
use App\Services\Billing\FuturesPaymentVoucherService;
use App\Services\Billing\InitialInvoiceService;
use App\Services\Billing\FuturesInvoiceService;

class BillingDocumentFactory
{

    public final static function init($billing_document_type): BillingDocumentInterface
    {

        switch ($billing_document_type) {

            case BillingDocument::TYPE_ADVANCE_INVOICE:
                return new AdvanceInvoiceService();

            case BillingDocument::TYPE_NORMAL_INVOICE:
                return new InvoiceService();

            case BillingDocument::TYPE_ADVANCE_CREDIT_NOTE:
                return new AdvanceCreditNoteService();

            case BillingDocument::TYPE_PROFORMA_INVOICE:
                return new ProformaInvoiceService();

            case BillingDocument::TYPE_CREDIT_NOTE:
                return new CreditDebitNoteService();

            case BillingDocument::TYPE_DEBIT_NOTE:
                return new CreditDebitNoteService();

            case BillingDocument::TYPE_ADVANCE_VOUCHER:
                return new AdvanceVoucherService();

            case BillingDocument::TYPE_VOUCHER:
                return new FinalVoucherService();

            case BillingDocument::TYPE_PAYMENT_VOUCHER:
                return new PaymentVoucherService();

            case BillingDocument::TYPE_ADVANCE_PAYMENT_VOUCHER:
                return new AdvancePaymentVoucherService();

            case BillingDocument::TYPE_INITIAL_VOUCHER:
                return new FuturesPaymentVoucherService();

            case BillingDocument::TYPE_INITIAL_INVOICE:
                return new InitialInvoiceService();

            case 'futures-invoice':
                return new FuturesInvoiceService();

            default:
                throw new \Exception('Billing document type not supported by BillingDocumentFactory.');
        }
    }

    public final static function initFromModel(BillingDocument $billingDocument): BillingDocumentInterface
    {

        $billingDocument->loadMissing(['contractable', 'lineItems']);

        $instance = self::init($billingDocument->type);
        $instance->setContract($billingDocument->contractable);
        $instance->setBillingDocument($billingDocument);
        $instance->setLineItems($billingDocument->lineItems);
        $instance->setBillingData($billingDocument->toArray());

        return $instance;
    }
}
