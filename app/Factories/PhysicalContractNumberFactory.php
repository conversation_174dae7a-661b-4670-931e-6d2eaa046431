<?php

namespace App\Factories;

use App\Helpers\GtmMyPhysicalContractNumberHelper;
use App\Helpers\MinamasDownstreamPhysicalContractNumberHelper;
use App\Helpers\MinamasUpstreamPhysicalContractNumberHelper;
use App\Helpers\PngPhysicalContractNumberHelper;
use App\Interfaces\PhysicalContractNumberInterface;
use App\Models\LegalEntity;
use App\Services\ContractNumberService;

class PhysicalContractNumberFactory
{
    public function getConcreteClass($contract_number_format): PhysicalContractNumberInterface
    {
        if ( $contract_number_format == null ){
            throw new \Exception('Contract number format not defined in Legal Entity Master.');
        }

        if ($contract_number_format == LegalEntity::CONTRACT_NUMBER_FORMAT_GTM_MY) {
            return app()->make(GtmMyPhysicalContractNumberHelper::class);
        } else if ($contract_number_format == LegalEntity::CONTRACT_NUMBER_FORMAT_MINAMAS_UPSTREAM) {
            return app()->make(MinamasUpstreamPhysicalContractNumberHelper::class);
        } else if ($contract_number_format == LegalEntity::CONTRACT_NUMBER_FORMAT_MINAMAS_DOWNSTREAM) {
            return app()->make(MinamasDownstreamPhysicalContractNumberHelper::class);
        } else if ($contract_number_format == LegalEntity::CONTRACT_NUMBER_FORMAT_PNG) {
            return app()->make(PngPhysicalContractNumberHelper::class);
        } else {
            throw new \Exception('Unsupported contract number format. Please contact administrator.');
        }
    }


    /**
     * GENERATE DRAFT CONTRACT NUMBER. SINCE ALL USES THE SAME DRAFT NUMBER ANYWAYS
     * @return string
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function generateContractDraftNumber(): string
    {
        $contractNumberService = app()->make(ContractNumberService::class);

        $running_number = $contractNumberService->allocateContractNumber(true, null, null, null, null, null, null, 0);
        return $running_number;
    }


}
