<?php

namespace App\Factories;

use App\Helpers\MinamasUpstreamBillingNumberHelper;
use App\Helpers\MinamasDownstreamBillingNumberHelper;
use App\Interfaces\BillingNumberInterface;
use App\Models\LegalEntity;

class BillingNumberFactory
{
    public function getConcreteClass(string $billingNumberFormat): BillingNumberInterface
    {
        if ($billingNumberFormat == LegalEntity::BILLING_NUMBER_FORMAT_MINAMAS_UPSTREAM) {
            return app()->make(MinamasUpstreamBillingNumberHelper::class);
        } else if ($billingNumberFormat == LegalEntity::BILLING_NUMBER_FORMAT_MINAMAS_DOWNSTREAM) {
            return app()->make(MinamasDownstreamBillingNumberHelper::class);
        } else {
            throw new \Exception('Unsupported billing number format. Please contact administrator.');
        }
    }
}
