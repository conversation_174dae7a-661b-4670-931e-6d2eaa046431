<?php

namespace App\Custom\Database;

use App\Custom\Database\Query\Grammars\SqlServerGrammar as QueryGrammar;
use App\Custom\Database\Schema\Grammars\SqlServerGrammar as SchemaGrammar;

class SqlServerConnection extends \Illuminate\Database\SqlServerConnection {
    /**
     * Get the default query grammar instance.
     *
     * @return \Illuminate\Database\Query\Grammars\SqlServerGrammar
     */
    protected function getDefaultQueryGrammar()
    {
        return $this->withTablePrefix(new QueryGrammar);
    }

    /**
     * Get the default schema grammar instance.
     *
     * @return \Illuminate\Database\Schema\Grammars\SqlServerGrammar
     */
    protected function getDefaultSchemaGrammar()
    {
        return $this->withTablePrefix(new SchemaGrammar);
    }
}
