<?php

namespace App\Custom\Database\Schema\Grammars;

use Illuminate\Support\Fluent;

class SqlServerGrammar extends \Illuminate\Database\Schema\Grammars\SqlServerGrammar {
    /**
     * Create the column definition for a timestamp type.
     *
     * @param  \Illuminate\Support\Fluent  $column
     * @return string
     */
    protected function typeTimestamp(Fluent $column)
    {
        $columnType = $column->precision ? "datetime2($column->precision)" : 'datetime2(3)';

        return $column->useCurrent ? "$columnType default CURRENT_TIMESTAMP" : $columnType;
    }
}