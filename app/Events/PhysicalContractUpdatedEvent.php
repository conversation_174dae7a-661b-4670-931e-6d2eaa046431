<?php

namespace App\Events;

use App\Models\PhysicalContract;
use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Support\Facades\Log;

class PhysicalContractUpdatedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $contract;
    public $name = 'PhysicalContractUpdatedEvent';

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(PhysicalContract $physicalContract)
    {
        $this->contract = $physicalContract;
    }

}
