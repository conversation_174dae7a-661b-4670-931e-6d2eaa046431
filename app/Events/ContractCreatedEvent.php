<?php

namespace App\Events;

use App\Interfaces\Contractable;
use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class ContractCreatedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $contractable;
    public $name = 'ContractCreatedEvent';

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Contractable $contractable)
    {
        $this->contractable = $contractable;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
