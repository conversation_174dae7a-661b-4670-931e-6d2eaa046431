<?php

namespace App\Exceptions;

use Exception;
use Throwable;
use Maatwebsite\Excel\Exceptions\LaravelExcelException;

class InvalidExcelHeaderException extends Exception implements LaravelExcelException
{
    /**
     * @param string         $message
     * @param int            $code
     * @param Throwable|null $previous
     */
    public function __construct(
        $message = 'File could not be read',
        $code = 0,
        Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }
}
