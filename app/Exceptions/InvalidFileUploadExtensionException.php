<?php

namespace App\Exceptions;

use Exception;
use Throwable;
use Maatwebsite\Excel\Exceptions\LaravelExcelException;

class InvalidFileUploadExtensionException extends Exception implements LaravelExcelException
{
    /**
     * @param string         $message
     * @param int            $code
     * @param Throwable|null $previous
     */
    public function __construct(
        $message = 'Invalid file extension',
        $code = 0,
        Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }
}
