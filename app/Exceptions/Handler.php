<?php

namespace App\Exceptions;

use Carbon\Carbon;
use Exception;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function report(Exception $exception)
    {
        if ( app()->environment('production') || app()->environment('staging') ){

            if ( app()->bound('sentry') && $this->shouldReport($exception) ) {
                app('sentry')->captureException($exception);
            }

        }

        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Exception  $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, Exception $exception)
    {
        if ( $request->expectsJson() ){

            if ( $exception instanceof MethodNotAllowedHttpException ){

                return response()->json([
                    'code' => $exception->getStatusCode(),
                    'message' => $request->getMethod() . ' method is not allowed for this route.',
                    'data' => null,
                    'timestamp' => Carbon::now('UTC')->toIso8601String()
                ], $exception->getStatusCode());

            }
            else if ( $exception instanceof ValidationException ) {

                return response()->json([
                    'code' => $exception->status,
                    'message' => $exception->errors(),
                    'data' => null,
                    'timestamp' =>  Carbon::now('UTC')->toIso8601String()
                ], $exception->status);

            }
            else if ( $exception instanceof NotFoundHttpException ){

                return response()->json([
                    'code' => JsonResponse::HTTP_NOT_FOUND,
                    'message' => 'Route not found.',
                    'data' => null,
                    'timestamp' => Carbon::now('UTC')->toIso8601String()
                ], JsonResponse::HTTP_NOT_FOUND);

            }else{

                return response()->json([
                    'code' => $exception->getCode(),
                    'message' => $exception->getMessage(),
                    'data' => null,
                    'timestamp' => Carbon::now('UTC')->toIso8601String()
                ], JsonResponse::HTTP_BAD_REQUEST);

            }

        }


        return parent::render($request, $exception);
    }
}
