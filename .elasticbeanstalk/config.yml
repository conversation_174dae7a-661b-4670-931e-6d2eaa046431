branch-defaults:
  development:
    environment: spot-dev
  fix-for-monthly-billing:
    environment: spot-dev
  qas:
    environment: spot-dev
environment-defaults:
  spot-dev:
    branch: null
    repository: null
global:
  application_name: spot
  default_ec2_keyname: spot-key
  default_platform: arn:aws:elasticbeanstalk:ap-southeast-1::platform/Multi-container
    Docker running on 64bit Amazon Linux/2.15.2
  default_region: ap-southeast-1
  include_git_submodules: true
  instance_profile: null
  platform_name: null
  platform_version: null
  profile: null
  sc: git
  workspace_type: Application
